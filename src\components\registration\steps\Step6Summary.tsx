"use client";

import React, { useEffect, useState } from 'react';
import { useReactToPrint } from 'react-to-print';
import SimplePrintButton from '@/components/print/SimplePrintButton';

interface Step6Props {
  onComplete: (data: any, isValid: boolean) => void;
  formData: Record<string, any>;
}

const Step6Summary: React.FC<Step6Props> = ({ onComplete, formData }) => {
  const printRef = React.useRef<HTMLDivElement>(null);
  const [showPrintModal, setShowPrintModal] = useState(false);

  // تحويل بيانات النموذج إلى الهيكل المطلوب للطباعة
  const student = {
    id: '1',
    first_name: formData.fullName?.split(' ')[0] || '',
    middle_name: formData.fullName?.split(' ')[1] || '',
    last_name: formData.fullName?.split(' ').slice(2).join(' ') || '',
    national_id: formData.idNumber || '',
    birth_date: formData.birthDate || '',
    gender: formData.gender || 'male',
    email: formData.email || '',
    phone: formData.phone || '',
    address: formData.address || '',
    grade: formData.grade || '',
    registration_date: new Date().toISOString()
  };

  // تحويل بيانات الأوصياء
  const guardians = formData.guardians?.map((guardian: any, index: number) => ({
    id: `${index + 1}`,
    student_id: '1',
    name: guardian.fullName || '',
    relation: guardian.relationship || '',
    national_id: guardian.idNumber || '',
    phone: guardian.phone || '',
    email: guardian.email || ''
  })) || [];

  // تحويل البيانات المالية
  const financialInfo = {
    id: '1',
    student_id: '1',
    tuition_fee: parseFloat(formData.tuitionFee) || 0,
    discount_amount: parseFloat(formData.discountAmount) || 0,
    discount_reason: formData.discountReason || '',
    paid_amount: parseFloat(formData.paidAmount) || 0,
    payment_method: formData.paymentMethod || 'cash',
    installments_count: parseInt(formData.installmentsCount) || 0
  };

  // تحويل بيانات الأقساط
  const installments = Array.from({ length: parseInt(formData.installmentsCount) || 0 }, (_, i) => {
    const installmentAmount = parseFloat(formData.installmentAmount) || 0;
    const dueDate = new Date();
    dueDate.setMonth(dueDate.getMonth() + i + 1);

    return {
      id: `${i + 1}`,
      financial_info_id: '1',
      amount: installmentAmount,
      due_date: dueDate.toISOString(),
      paid: 0,
      discount: 0,
      status: 'pending'
    };
  });

  // تحويل المدفوعات الأخرى
  const otherPayments = [
    formData.fileOpeningFee && parseFloat(formData.fileOpeningFee) > 0 ? {
      id: '1',
      student_id: '1',
      description: 'رسوم فتح الملف',
      amount: parseFloat(formData.fileOpeningFee)
    } : null,
    formData.booksFee && parseFloat(formData.booksFee) > 0 ? {
      id: '2',
      student_id: '1',
      description: 'رسوم الكتب',
      amount: parseFloat(formData.booksFee)
    } : null,
    formData.uniformTotal && parseFloat(formData.uniformTotal) > 0 ? {
      id: '3',
      student_id: '1',
      description: `الزي المدرسي (${formData.uniformCount || 0} قطعة)`,
      amount: parseFloat(formData.uniformTotal)
    } : null,
    formData.transportationFee && parseFloat(formData.transportationFee) > 0 ? {
      id: '4',
      student_id: '1',
      description: 'النقل المدرسي',
      amount: parseFloat(formData.transportationFee)
    } : null
  ].filter(Boolean);

  // تحديث البيانات عند تحميل المكون
  useEffect(() => {
    onComplete({ summaryReviewed: true }, true);
  }, [onComplete]);

  // وظيفة الطباعة
  const handlePrint = useReactToPrint({
    content: () => printRef.current,
    documentTitle: 'ملخص التسجيل',
  });

  // حساب إجمالي المستحق
  const calculateTotalDue = () => {
    const tuitionFee = parseFloat(formData.tuitionFee) || 0;
    const discountAmount = parseFloat(formData.discountAmount) || 0;
    const otherPaymentsTotal = parseFloat(formData.totalAmount) || 0;

    return tuitionFee - discountAmount + otherPaymentsTotal;
  };

  // حساب إجمالي المدفوع
  const calculateTotalPaid = () => {
    const paidAmount = parseFloat(formData.paidAmount) || 0;
    return paidAmount;
  };

  // حساب إجمالي المتبقي
  const calculateTotalRemaining = () => {
    return calculateTotalDue() - calculateTotalPaid();
  };

  return (
    <div className="bg-white rounded-lg p-6">
      <h2 className="text-xl font-bold text-primary mb-6 border-r-4 border-primary pr-3">
        ملخص التسجيل
      </h2>

      <div className="mb-4 flex justify-end">
        <SimplePrintButton
          student={student}
          guardians={guardians}
          financialInfo={financialInfo}
          installments={installments}
          otherPayments={otherPayments}
        />
      </div>

      <div ref={printRef} className="p-4">
        <div className="text-center mb-6">
          <h1 className="text-2xl font-bold text-primary">نظام إدارة المدرسة</h1>
          <h2 className="text-xl font-bold mt-2">ملخص تسجيل طالب</h2>
          <p className="text-gray-500 mt-1">تاريخ التسجيل: {new Date().toLocaleDateString('ar-EG')}</p>
        </div>

        {/* بيانات الطالب */}
        <div className="mb-6">
          <h3 className="text-lg font-bold text-primary mb-3 border-r-4 border-primary pr-2">
            بيانات الطالب
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-4 bg-gray-50 rounded-lg border border-gray-200">
            <div className="flex justify-between">
              <span className="font-medium">الاسم الكامل:</span>
              <span>{formData.fullName}</span>
            </div>
            <div className="flex justify-between">
              <span className="font-medium">رقم الهوية:</span>
              <span>{formData.idNumber}</span>
            </div>
            <div className="flex justify-between">
              <span className="font-medium">تاريخ الميلاد:</span>
              <span>{formData.birthDate}</span>
            </div>
            <div className="flex justify-between">
              <span className="font-medium">الجنس:</span>
              <span>{formData.gender === 'male' ? 'ذكر' : 'أنثى'}</span>
            </div>
            {formData.email && (
              <div className="flex justify-between">
                <span className="font-medium">البريد الإلكتروني:</span>
                <span>{formData.email}</span>
              </div>
            )}
            {formData.phone && (
              <div className="flex justify-between">
                <span className="font-medium">رقم الهاتف:</span>
                <span>{formData.phone}</span>
              </div>
            )}
          </div>
        </div>

        {/* بيانات ولي الأمر */}
        <div className="mb-6">
          <h3 className="text-lg font-bold text-primary mb-3 border-r-4 border-primary pr-2">
            بيانات ولي الأمر
          </h3>
          {formData.guardians && formData.guardians.map((guardian: any, index: number) => (
            <div key={index} className="p-4 bg-gray-50 rounded-lg border border-gray-200 mb-3">
              <h4 className="font-medium text-secondary mb-2">ولي الأمر {index + 1}</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex justify-between">
                  <span className="font-medium">الاسم الكامل:</span>
                  <span>{guardian.fullName}</span>
                </div>
                <div className="flex justify-between">
                  <span className="font-medium">العلاقة بالطالب:</span>
                  <span>{guardian.relationship}</span>
                </div>
                <div className="flex justify-between">
                  <span className="font-medium">رقم الهوية:</span>
                  <span>{guardian.idNumber}</span>
                </div>
                <div className="flex justify-between">
                  <span className="font-medium">رقم الهاتف:</span>
                  <span>{guardian.phone}</span>
                </div>
                {guardian.email && (
                  <div className="flex justify-between">
                    <span className="font-medium">البريد الإلكتروني:</span>
                    <span>{guardian.email}</span>
                  </div>
                )}
                {guardian.occupation && (
                  <div className="flex justify-between">
                    <span className="font-medium">المهنة:</span>
                    <span>{guardian.occupation}</span>
                  </div>
                )}
                {guardian.workplace && (
                  <div className="flex justify-between">
                    <span className="font-medium">مكان العمل:</span>
                    <span>{guardian.workplace}</span>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>

        {/* البيانات المالية */}
        <div className="mb-6">
          <h3 className="text-lg font-bold text-primary mb-3 border-r-4 border-primary pr-2">
            البيانات المالية
          </h3>
          <div className="p-4 bg-gray-50 rounded-lg border border-gray-200">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <div className="flex justify-between">
                <span className="font-medium">الرسوم الدراسية:</span>
                <span>{parseFloat(formData.tuitionFee) || 0} ج.م</span>
              </div>
              <div className="flex justify-between">
                <span className="font-medium">قيمة الخصم:</span>
                <span>{parseFloat(formData.discountAmount) || 0} ج.م</span>
              </div>
              <div className="flex justify-between">
                <span className="font-medium">الإجمالي بعد الخصم:</span>
                <span className="font-bold text-primary">{parseFloat(formData.totalAfterDiscount) || 0} ج.م</span>
              </div>
              <div className="flex justify-between">
                <span className="font-medium">طريقة الدفع:</span>
                <span>{formData.paymentMethod === 'cash' ? 'نقدي' : 'تقسيط'}</span>
              </div>
              {formData.paymentMethod === 'installments' && (
                <>
                  <div className="flex justify-between">
                    <span className="font-medium">عدد الأقساط:</span>
                    <span>{formData.installmentsCount}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="font-medium">قيمة القسط:</span>
                    <span>{parseFloat(formData.installmentAmount).toFixed(2)} ج.م</span>
                  </div>
                </>
              )}
            </div>

            <h4 className="font-medium text-secondary mb-2 mt-4">المدفوعات الأخرى</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex justify-between">
                <span className="font-medium">رسوم فتح الملف:</span>
                <span>{parseFloat(formData.fileOpeningFee) || 0} ج.م</span>
              </div>
              <div className="flex justify-between">
                <span className="font-medium">رسوم الكتب:</span>
                <span>{parseFloat(formData.booksFee) || 0} ج.م</span>
              </div>
              <div className="flex justify-between">
                <span className="font-medium">الزي المدرسي:</span>
                <span>{parseFloat(formData.uniformTotal) || 0} ج.م ({formData.uniformCount} قطعة)</span>
              </div>
              <div className="flex justify-between">
                <span className="font-medium">النقل المدرسي:</span>
                <span>{parseFloat(formData.transportationFee) || 0} ج.م</span>
              </div>
              <div className="flex justify-between">
                <span className="font-medium">إجمالي المدفوعات الأخرى:</span>
                <span className="font-bold text-secondary">{parseFloat(formData.totalAmount) || 0} ج.م</span>
              </div>
            </div>
          </div>
        </div>

        {/* الإجماليات */}
        <div className="mb-6">
          <h3 className="text-lg font-bold text-primary mb-3 border-r-4 border-primary pr-2">
            الإجماليات
          </h3>
          <div className="p-4 bg-gray-50 rounded-lg border border-gray-200">
            <div className="grid grid-cols-1 gap-4">
              <div className="flex justify-between p-2 border-b border-gray-200">
                <span className="font-medium">إجمالي المستحق:</span>
                <span className="font-bold">{calculateTotalDue().toFixed(2)} ج.م</span>
              </div>
              <div className="flex justify-between p-2 border-b border-gray-200">
                <span className="font-medium">إجمالي المدفوع:</span>
                <span className="font-bold text-success">{calculateTotalPaid().toFixed(2)} ج.م</span>
              </div>
              <div className="flex justify-between p-2">
                <span className="font-medium">إجمالي المتبقي:</span>
                <span className="font-bold text-danger">{calculateTotalRemaining().toFixed(2)} ج.م</span>
              </div>
            </div>
          </div>
        </div>

        {/* التوقيعات */}
        <div className="mt-10 grid grid-cols-1 md:grid-cols-3 gap-8">
          <div className="text-center">
            <div className="h-16 border-b border-gray-300 mb-2"></div>
            <p className="font-medium">توقيع ولي الأمر</p>
          </div>
          <div className="text-center">
            <div className="h-16 border-b border-gray-300 mb-2"></div>
            <p className="font-medium">توقيع المحاسب</p>
          </div>
          <div className="text-center">
            <div className="h-16 border-b border-gray-300 mb-2"></div>
            <p className="font-medium">توقيع المدير</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Step6Summary;
