"use client";

import React, { useState, useEffect } from 'react';
import { supabase } from '@/lib/supabase';
import { Student } from '@/types/table.types';
import { FaUser, FaEdit } from 'react-icons/fa';
import EditModal from '@/components/ui/EditModal';
import { useRealtime } from '@/context/RealtimeContext';

interface EditBasicInfoProps {
  student: Student | null;
  onUpdate?: () => void;
}

const EditBasicInfo: React.FC<EditBasicInfoProps> = ({ student, onUpdate }) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [formData, setFormData] = useState<Partial<Student>>({});
  const [errors, setErrors] = useState<Record<string, string>>({});
  const { refreshStudents } = useRealtime();

  // تحديث بيانات النموذج عند تغيير الطالب
  useEffect(() => {
    if (student) {
      setFormData({
        full_name: student.full_name,
        id_number: student.id_number,
        birth_date: student.birth_date ? new Date(student.birth_date).toISOString().split('T')[0] : '',
        gender: student.gender,
        religion: student.religion,
        marital_status: student.marital_status,
        email: student.email || '',
        phone: student.phone || '',
      });
    }
  }, [student]);

  // فتح النافذة المنبثقة
  const handleOpenModal = () => {
    setIsModalOpen(true);
  };

  // إغلاق النافذة المنبثقة
  const handleCloseModal = () => {
    setIsModalOpen(false);
    setErrors({});
  };

  // تحديث بيانات النموذج
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    
    // إزالة الخطأ عند تعديل الحقل
    if (errors[name]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
  };

  // التحقق من صحة البيانات
  const validateForm = () => {
    const newErrors: Record<string, string> = {};
    
    if (!formData.full_name?.trim()) {
      newErrors.full_name = 'الاسم الكامل مطلوب';
    }
    
    if (!formData.id_number?.trim()) {
      newErrors.id_number = 'رقم الهوية مطلوب';
    }
    
    if (!formData.birth_date) {
      newErrors.birth_date = 'تاريخ الميلاد مطلوب';
    }
    
    if (!formData.gender) {
      newErrors.gender = 'الجنس مطلوب';
    }
    
    if (formData.email && !/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'البريد الإلكتروني غير صالح';
    }
    
    if (formData.phone && !/^\d{10,15}$/.test(formData.phone.replace(/\D/g, ''))) {
      newErrors.phone = 'رقم الهاتف غير صالح';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // حفظ التغييرات
  const handleSave = async () => {
    if (!student?.id || !validateForm()) return;
    
    try {
      setIsSaving(true);
      
      const { data, error } = await supabase
        .from('students')
        .update({
          full_name: formData.full_name,
          id_number: formData.id_number,
          birth_date: formData.birth_date,
          gender: formData.gender,
          religion: formData.religion,
          marital_status: formData.marital_status,
          email: formData.email || null,
          phone: formData.phone || null,
          updated_at: new Date().toISOString(),
        })
        .eq('id', student.id)
        .select()
        .single();
      
      if (error) {
        throw new Error(error.message);
      }
      
      console.log('تم تحديث البيانات الأساسية:', data);
      
      // تحديث البيانات في الواجهة
      if (refreshStudents) {
        refreshStudents();
      }
      
      // استدعاء دالة التحديث إذا كانت موجودة
      if (onUpdate) {
        onUpdate();
      }
      
      // إغلاق النافذة المنبثقة
      handleCloseModal();
    } catch (err: any) {
      console.error('خطأ في تحديث البيانات الأساسية:', err);
      alert(`حدث خطأ أثناء حفظ البيانات: ${err.message}`);
    } finally {
      setIsSaving(false);
    }
  };

  // إذا لم يكن هناك طالب، لا تعرض شيئًا
  if (!student) return null;

  return (
    <>
      {/* زر التعديل */}
      <button
        onClick={handleOpenModal}
        className="bg-[#5578EB] hover:bg-[#5578EB]/90 text-white px-3 py-1 rounded-md flex items-center transition-colors duration-200 text-sm"
        title="تعديل البيانات الأساسية"
      >
        <FaEdit className="ml-1" />
        تعديل
      </button>

      {/* نافذة التعديل المنبثقة */}
      <EditModal
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        onSave={handleSave}
        title="تعديل البيانات الأساسية"
        isSaving={isSaving}
        color="#21ADE7"
        icon={<FaUser size={18} />}
      >
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* الاسم الكامل */}
          <div className="col-span-2">
            <label className="block text-gray-700 mb-1">الاسم الكامل <span className="text-red-500">*</span></label>
            <input
              type="text"
              name="full_name"
              value={formData.full_name || ''}
              onChange={handleChange}
              className={`w-full p-2 border rounded-md ${errors.full_name ? 'border-red-500' : 'border-gray-300'}`}
              placeholder="أدخل الاسم الكامل"
              dir="rtl"
            />
            {errors.full_name && <p className="text-red-500 text-sm mt-1">{errors.full_name}</p>}
          </div>

          {/* رقم الهوية */}
          <div>
            <label className="block text-gray-700 mb-1">رقم الهوية <span className="text-red-500">*</span></label>
            <input
              type="text"
              name="id_number"
              value={formData.id_number || ''}
              onChange={handleChange}
              className={`w-full p-2 border rounded-md ${errors.id_number ? 'border-red-500' : 'border-gray-300'}`}
              placeholder="أدخل رقم الهوية"
              dir="rtl"
            />
            {errors.id_number && <p className="text-red-500 text-sm mt-1">{errors.id_number}</p>}
          </div>

          {/* تاريخ الميلاد */}
          <div>
            <label className="block text-gray-700 mb-1">تاريخ الميلاد <span className="text-red-500">*</span></label>
            <input
              type="date"
              name="birth_date"
              value={formData.birth_date || ''}
              onChange={handleChange}
              className={`w-full p-2 border rounded-md ${errors.birth_date ? 'border-red-500' : 'border-gray-300'}`}
              dir="rtl"
            />
            {errors.birth_date && <p className="text-red-500 text-sm mt-1">{errors.birth_date}</p>}
          </div>

          {/* الجنس */}
          <div>
            <label className="block text-gray-700 mb-1">الجنس <span className="text-red-500">*</span></label>
            <select
              name="gender"
              value={formData.gender || ''}
              onChange={handleChange}
              className={`w-full p-2 border rounded-md ${errors.gender ? 'border-red-500' : 'border-gray-300'}`}
              dir="rtl"
            >
              <option value="">اختر الجنس</option>
              <option value="male">ذكر</option>
              <option value="female">أنثى</option>
            </select>
            {errors.gender && <p className="text-red-500 text-sm mt-1">{errors.gender}</p>}
          </div>

          {/* الديانة */}
          <div>
            <label className="block text-gray-700 mb-1">الديانة</label>
            <select
              name="religion"
              value={formData.religion || ''}
              onChange={handleChange}
              className="w-full p-2 border border-gray-300 rounded-md"
              dir="rtl"
            >
              <option value="">اختر الديانة</option>
              <option value="islam">الإسلام</option>
              <option value="christianity">المسيحية</option>
            </select>
          </div>

          {/* الحالة الاجتماعية */}
          <div>
            <label className="block text-gray-700 mb-1">الحالة الاجتماعية</label>
            <select
              name="marital_status"
              value={formData.marital_status || ''}
              onChange={handleChange}
              className="w-full p-2 border border-gray-300 rounded-md"
              dir="rtl"
            >
              <option value="">اختر الحالة الاجتماعية</option>
              <option value="single">أعزب</option>
              <option value="married">متزوج</option>
              <option value="divorced">مطلق</option>
              <option value="widowed">أرمل</option>
            </select>
          </div>

          {/* البريد الإلكتروني */}
          <div>
            <label className="block text-gray-700 mb-1">البريد الإلكتروني</label>
            <input
              type="email"
              name="email"
              value={formData.email || ''}
              onChange={handleChange}
              className={`w-full p-2 border rounded-md ${errors.email ? 'border-red-500' : 'border-gray-300'}`}
              placeholder="أدخل البريد الإلكتروني"
              dir="rtl"
            />
            {errors.email && <p className="text-red-500 text-sm mt-1">{errors.email}</p>}
          </div>

          {/* رقم الهاتف */}
          <div>
            <label className="block text-gray-700 mb-1">رقم الهاتف</label>
            <input
              type="tel"
              name="phone"
              value={formData.phone || ''}
              onChange={handleChange}
              className={`w-full p-2 border rounded-md ${errors.phone ? 'border-red-500' : 'border-gray-300'}`}
              placeholder="أدخل رقم الهاتف"
              dir="rtl"
            />
            {errors.phone && <p className="text-red-500 text-sm mt-1">{errors.phone}</p>}
          </div>
        </div>
      </EditModal>
    </>
  );
};

export default EditBasicInfo;
