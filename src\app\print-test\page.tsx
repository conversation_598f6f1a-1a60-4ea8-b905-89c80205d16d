'use client';

import React from 'react';
import SimplePrintButton from '@/components/print/SimplePrintButton';

const PrintTestPage = () => {
  // بيانات تجريبية للطباعة
  const student = {
    id: '1',
    first_name: 'هب<PERSON>',
    middle_name: 'موسي',
    last_name: 'عوني',
    national_id: '29512172102658',
    birth_date: '2025-05-16',
    gender: 'female',
    email: 'hassan<PERSON><PERSON>@gmail.com',
    phone: '01111585527',
    address: 'القاهرة، مصر',
    grade: 'الصف الثالث الابتدائي',
    registration_date: '2025-05-16'
  };

  const guardians = [
    {
      id: '1',
      student_id: '1',
      name: 'موسي',
      relation: 'father',
      national_id: '29512172102858',
      phone: '01111585527',
      email: 'hassan<PERSON><EMAIL>'
    }
  ];

  const financialInfo = {
    id: '1',
    student_id: '1',
    tuition_fee: 5000,
    discount_amount: 500,
    discount_reason: 'خصم الأخوة',
    paid_amount: 1000,
    payment_method: 'installments',
    installments_count: 2
  };

  const installments = [
    {
      id: '1',
      financial_info_id: '1',
      amount: 2250,
      due_date: '2025-06-16',
      paid: 0,
      discount: 0,
      status: 'pending'
    },
    {
      id: '2',
      financial_info_id: '1',
      amount: 2250,
      due_date: '2025-07-16',
      paid: 0,
      discount: 0,
      status: 'pending'
    }
  ];

  const otherPayments = [
    {
      id: '1',
      student_id: '1',
      description: 'رسوم فتح الملف',
      amount: 300
    },
    {
      id: '2',
      student_id: '1',
      description: 'رسوم الكتب',
      amount: 300
    },
    {
      id: '3',
      student_id: '1',
      description: 'الزي المدرسي (3 قطعة)',
      amount: 900
    },
    {
      id: '4',
      student_id: '1',
      description: 'النقل المدرسي',
      amount: 300
    }
  ];

  return (
    <div className="container mx-auto p-4 text-right" dir="rtl">
      <h1 className="text-2xl font-bold mb-6">اختبار صفحة الطباعة</h1>

      <div className="bg-white rounded-lg p-6 shadow-md">
        <h2 className="text-xl font-bold text-[#0F2A4A] mb-6 border-r-4 border-[#0F2A4A] pr-3">ملخص التسجيل</h2>

        <div className="mb-4 flex justify-end">
          <SimplePrintButton
            student={student}
            guardians={guardians}
            financialInfo={financialInfo}
            installments={installments}
            otherPayments={otherPayments}
          />
        </div>

        <div className="p-4">
          <div className="text-center mb-6">
            <h1 className="text-2xl font-bold text-[#0F2A4A]">نظام إدارة المدرسة</h1>
            <h2 className="text-xl font-bold mt-2">ملخص تسجيل طالب</h2>
            <p className="text-gray-500 mt-1">تاريخ التسجيل: {student.registration_date}</p>
          </div>

          <div className="mb-6">
            <h3 className="text-lg font-bold text-[#0F2A4A] mb-3 border-r-4 border-[#0F2A4A] pr-2">بيانات الطالب</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-4 bg-gray-50 rounded-lg border border-gray-200">
              <div className="flex justify-between">
                <span className="font-medium">الاسم الكامل:</span>
                <span>{`${student.first_name} ${student.middle_name} ${student.last_name}`}</span>
              </div>
              <div className="flex justify-between">
                <span className="font-medium">رقم الهوية:</span>
                <span>{student.national_id}</span>
              </div>
              <div className="flex justify-between">
                <span className="font-medium">تاريخ الميلاد:</span>
                <span>{student.birth_date}</span>
              </div>
              <div className="flex justify-between">
                <span className="font-medium">الجنس:</span>
                <span>{student.gender === 'male' ? 'ذكر' : 'أنثى'}</span>
              </div>
              <div className="flex justify-between">
                <span className="font-medium">البريد الإلكتروني:</span>
                <span>{student.email}</span>
              </div>
              <div className="flex justify-between">
                <span className="font-medium">رقم الهاتف:</span>
                <span>{student.phone}</span>
              </div>
            </div>
          </div>

          {/* المزيد من البيانات يمكن عرضها هنا */}

          <div className="mt-10 grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="h-16 border-b border-gray-300 mb-2"></div>
              <p className="font-medium">توقيع ولي الأمر</p>
            </div>
            <div className="text-center">
              <div className="h-16 border-b border-gray-300 mb-2"></div>
              <p className="font-medium">توقيع المحاسب</p>
            </div>
            <div className="text-center">
              <div className="h-16 border-b border-gray-300 mb-2"></div>
              <p className="font-medium">توقيع المدير</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PrintTestPage;
