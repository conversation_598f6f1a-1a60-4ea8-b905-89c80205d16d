"use client";

import React, { useState } from 'react';
import { FaFileImport, Fa<PERSON><PERSON><PERSON>, <PERSON>a<PERSON><PERSON><PERSON>, FaExclamationTriangle, FaFileExcel, FaFileCsv, FaFileCode } from 'react-icons/fa';
import { mapCSVDataToStudentForm } from '@/lib/csvHelper';
import * as XLSX from 'xlsx';

interface ImportStudentDataProps {
  onImport: (data: any) => void;
  onSaveToDatabase?: (data: any) => void;
}

const ImportStudentData: React.FC<ImportStudentDataProps> = ({ onImport, onSaveToDatabase }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [showOptions, setShowOptions] = useState(false);
  const [importedData, setImportedData] = useState<any[]>([]);
  const [showSaveButton, setShowSaveButton] = useState(false);

  // تنزيل نموذج CSV
  const handleDownloadCSVTemplate = () => {
    try {
      // تنزيل النموذج الثابت من المجلد العام
      const link = document.createElement('a');
      link.href = '/student-template-utf8.csv';
      link.download = 'نموذج_بيانات_الطالب.csv';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } catch (error) {
      console.error('Error downloading CSV template:', error);
      setError('حدث خطأ أثناء تنزيل نموذج CSV');
    }
  };

  // تنزيل نموذج Excel
  const handleDownloadExcelTemplate = () => {
    try {
      // تنزيل النموذج من API
      const link = document.createElement('a');
      link.href = '/api/excel-template';
      link.download = 'نموذج_بيانات_الطالب.xlsx';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } catch (error) {
      console.error('Error downloading Excel template:', error);
      setError('حدث خطأ أثناء تنزيل نموذج Excel');
    }
  };

  // استيراد البيانات من ملف CSV
  const handleCSVImport = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // التحقق من نوع الملف
    if (!file.name.endsWith('.csv')) {
      setError('يرجى اختيار ملف CSV صالح (بامتداد .csv)');
      return;
    }

    setIsLoading(true);
    setError(null);
    setSuccess(null);
    setShowSaveButton(false);

    try {
      // قراءة الملف كنص
      const text = await file.text();

      // تحليل النص CSV
      let rows = text.split('\n');

      // إزالة BOM إذا كان موجودًا
      if (rows[0].startsWith('\uFEFF')) {
        rows[0] = rows[0].substring(1);
      }

      // التحقق من وجود بيانات كافية
      if (rows.length < 2) {
        throw new Error('ملف CSV غير صالح، يجب أن يحتوي على صف العناوين وصف البيانات على الأقل');
      }

      // استخراج العناوين
      const headers = rows[0].split(',');

      // التحقق من وجود العناوين المطلوبة
      const requiredHeaders = ['الاسم الكامل', 'رقم الهوية'];
      const missingHeaders = requiredHeaders.filter(header => !headers.includes(header));

      if (missingHeaders.length > 0) {
        throw new Error(`الملف يفتقد إلى العناوين المطلوبة: ${missingHeaders.join(', ')}`);
      }

      // معالجة جميع الصفوف (باستثناء صف العناوين)
      const studentsData = [];

      for (let i = 1; i < rows.length; i++) {
        if (!rows[i].trim()) continue; // تخطي الصفوف الفارغة

        const dataRow = rows[i].split(',');

        // إنشاء كائن البيانات
        const rowData: Record<string, any> = {};
        headers.forEach((header, index) => {
          rowData[header.trim()] = dataRow[index]?.trim() || '';
        });

        // التحقق من وجود البيانات المطلوبة
        if (!rowData['الاسم الكامل'] || !rowData['رقم الهوية']) {
          console.warn(`تم تخطي الصف ${i + 1} بسبب نقص البيانات المطلوبة`);
          continue;
        }

        // تحويل البيانات إلى تنسيق نموذج التسجيل
        const mappedData = mapCSVDataToStudentForm(rowData);
        studentsData.push(mappedData);
      }

      if (studentsData.length === 0) {
        throw new Error('لم يتم العثور على بيانات صالحة في الملف');
      }

      // تخزين البيانات المستوردة
      setImportedData(studentsData);

      // استخدام أول طالب في القائمة للعرض
      onImport(studentsData[0]);

      setSuccess(`تم استيراد ${studentsData.length} طالب بنجاح من ملف CSV`);
      setShowSaveButton(true);
    } catch (error) {
      console.error('Error importing from CSV:', error);
      setError(error instanceof Error ? error.message : 'حدث خطأ أثناء استيراد البيانات من ملف CSV');
    } finally {
      setIsLoading(false);
    }
  };

  // استيراد البيانات من ملف Excel
  const handleExcelImport = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // التحقق من نوع الملف
    const validExcelTypes = ['.xlsx', '.xls'];
    const fileExtension = file.name.substring(file.name.lastIndexOf('.')).toLowerCase();
    if (!validExcelTypes.includes(fileExtension)) {
      setError(`يرجى اختيار ملف Excel صالح (بامتداد ${validExcelTypes.join(' أو ')})`);
      return;
    }

    setIsLoading(true);
    setError(null);
    setSuccess(null);
    setShowSaveButton(false);

    const reader = new FileReader();
    reader.onload = async (e) => {
      try {
        const data = new Uint8Array(e.target?.result as ArrayBuffer);
        const workbook = XLSX.read(data, { type: 'array' });

        // التحقق من وجود أوراق عمل
        if (workbook.SheetNames.length === 0) {
          throw new Error('ملف Excel غير صالح، لا يحتوي على أي أوراق عمل');
        }

        // استخدام الورقة الأولى
        const firstSheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[firstSheetName];

        // تحويل البيانات إلى JSON
        const jsonData = XLSX.utils.sheet_to_json(worksheet);

        if (jsonData && jsonData.length > 0) {
          // معالجة جميع الصفوف
          const studentsData = [];

          for (const row of jsonData) {
            const rowData = row as Record<string, any>;

            // التحقق من وجود البيانات المطلوبة
            if (!rowData['الاسم الكامل'] || !rowData['رقم الهوية']) {
              console.warn(`تم تخطي صف بسبب نقص البيانات المطلوبة`);
              continue;
            }

            // تحويل البيانات إلى تنسيق نموذج التسجيل
            const mappedData = mapExcelDataToStudentForm(rowData);
            studentsData.push(mappedData);
          }

          if (studentsData.length === 0) {
            throw new Error('لم يتم العثور على بيانات صالحة في الملف');
          }

          // تخزين البيانات المستوردة
          setImportedData(studentsData);

          // استخدام أول طالب في القائمة للعرض
          onImport(studentsData[0]);

          setSuccess(`تم استيراد ${studentsData.length} طالب بنجاح من ملف Excel`);
          setShowSaveButton(true);
        } else {
          setError('لم يتم العثور على بيانات في ملف Excel');
        }
      } catch (error) {
        console.error('Error importing from Excel:', error);
        setError(error instanceof Error ? error.message : 'حدث خطأ أثناء استيراد البيانات من ملف Excel');
      } finally {
        setIsLoading(false);
      }
    };

    reader.onerror = () => {
      setError('حدث خطأ أثناء قراءة الملف');
      setIsLoading(false);
    };

    reader.readAsArrayBuffer(file);
  };

  // استيراد البيانات من ملف JSON
  const handleJSONImport = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // التحقق من نوع الملف
    if (!file.name.endsWith('.json')) {
      setError('يرجى اختيار ملف JSON صالح (بامتداد .json)');
      return;
    }

    setIsLoading(true);
    setError(null);
    setSuccess(null);
    setShowSaveButton(false);

    const reader = new FileReader();
    reader.onload = async (e) => {
      try {
        const jsonText = e.target?.result as string;
        const jsonData = JSON.parse(jsonText);

        // التحقق من صحة بنية JSON
        if (!jsonData) {
          throw new Error('ملف JSON غير صالح');
        }

        // التعامل مع مصفوفة أو كائن واحد
        const dataArray = Array.isArray(jsonData) ? jsonData : [jsonData];

        if (dataArray.length === 0) {
          throw new Error('لم يتم العثور على بيانات في ملف JSON');
        }

        // معالجة جميع العناصر
        const studentsData = [];

        for (const item of dataArray) {
          // التحقق من وجود البيانات المطلوبة
          if (!item['الاسم الكامل'] || !item['رقم الهوية']) {
            console.warn('تم تخطي عنصر بسبب نقص البيانات المطلوبة');
            continue;
          }

          // تحويل البيانات إلى تنسيق نموذج التسجيل
          const mappedData = mapDataToStudentForm(item);
          studentsData.push(mappedData);
        }

        if (studentsData.length === 0) {
          throw new Error('لم يتم العثور على بيانات صالحة في الملف');
        }

        // تخزين البيانات المستوردة
        setImportedData(studentsData);

        // استخدام أول طالب في القائمة للعرض
        onImport(studentsData[0]);

        setSuccess(`تم استيراد ${studentsData.length} طالب بنجاح من ملف JSON`);
        setShowSaveButton(true);
      } catch (error) {
        console.error('Error importing from JSON:', error);
        setError(error instanceof Error ? error.message : 'حدث خطأ أثناء استيراد البيانات من ملف JSON');
      } finally {
        setIsLoading(false);
      }
    };

    reader.onerror = () => {
      setError('حدث خطأ أثناء قراءة الملف');
      setIsLoading(false);
    };

    reader.readAsText(file);
  };

  // إنشاء نموذج JSON
  const handleCreateJSONTemplate = () => {
    try {
      // إنشاء نموذج JSON
      const template = {
        'الاسم الكامل': '',
        'رقم الهوية': '',
        'تاريخ الميلاد': '',
        'الجنس': 'ذكر/أنثى',
        'الديانة': 'الإسلام/المسيحية',
        'الحالة الاجتماعية': '',
        'البريد الإلكتروني': '',
        'رقم الهاتف': '',
      };

      // تحويل النموذج إلى نص JSON
      const jsonString = JSON.stringify(template, null, 2);

      // إنشاء ملف للتنزيل
      const blob = new Blob([jsonString], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = 'نموذج_بيانات_الطالب.json';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } catch (error) {
      console.error('Error creating JSON template:', error);
      setError('حدث خطأ أثناء إنشاء نموذج JSON');
    }
  };



  // تحويل البيانات إلى تنسيق نموذج التسجيل
  const mapDataToStudentForm = (rowData: Record<string, any>) => {
    // تعيين الحقول من البيانات المستوردة إلى حقول نموذج التسجيل
    const mappings: Record<string, string> = {
      'الاسم الكامل': 'fullName',
      'رقم الهوية': 'idNumber',
      'تاريخ الميلاد': 'birthDate',
      'الجنس': 'gender',
      'الديانة': 'religion',
      'الحالة الاجتماعية': 'maritalStatus',
      'البريد الإلكتروني': 'email',
      'رقم الهاتف': 'phone',
      // يمكن إضافة المزيد من التعيينات حسب الحاجة
    };

    // تحويل قيم الجنس إلى القيم المتوقعة في النموذج
    const genderMapping: Record<string, string> = {
      'ذكر': 'male',
      'أنثى': 'female',
      'male': 'male',
      'female': 'female',
    };

    // تحويل قيم الديانة إلى القيم المتوقعة في النموذج
    const religionMapping: Record<string, string> = {
      'الإسلام': 'islam',
      'المسيحية': 'christianity',
      'islam': 'islam',
      'christianity': 'christianity',
    };

    // إنشاء كائن البيانات المحولة
    const formData: Record<string, any> = {};

    // تعيين البيانات باستخدام التعيينات
    Object.entries(rowData).forEach(([key, value]) => {
      const formField = mappings[key];
      if (formField) {
        if (formField === 'gender' && value) {
          formData[formField] = genderMapping[value.toString()] || value;
        } else if (formField === 'religion' && value) {
          formData[formField] = religionMapping[value.toString()] || value;
        } else {
          formData[formField] = value;
        }
      }
    });

    return formData;
  };

  // تحويل بيانات Excel إلى تنسيق نموذج التسجيل
  const mapExcelDataToStudentForm = (rowData: Record<string, any>) => {
    return mapDataToStudentForm(rowData);
  };

  // حفظ البيانات المستوردة في قاعدة البيانات
  const handleSaveToDatabase = async () => {
    if (!importedData || importedData.length === 0) {
      setError('لا توجد بيانات للحفظ');
      return;
    }

    setIsLoading(true);
    setError(null);
    setSuccess(null);

    try {
      // إرسال البيانات إلى API
      const response = await fetch('/api/students/import', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ students: importedData }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'فشل حفظ البيانات في قاعدة البيانات');
      }

      const result = await response.json();

      // إذا كان هناك دالة للتعامل مع نتيجة الحفظ
      if (onSaveToDatabase) {
        onSaveToDatabase(result);
      }

      setSuccess(`تم حفظ البيانات بنجاح: ${result.results.newStudents} طالب جديد، ${result.results.updatedStudents} طالب تم تحديثه`);
      setShowSaveButton(false);
    } catch (error) {
      console.error('Error saving to database:', error);
      setError(error instanceof Error ? error.message : 'حدث خطأ أثناء حفظ البيانات في قاعدة البيانات');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="mb-6">
      <button
        type="button"
        onClick={() => setShowOptions(!showOptions)}
        className="flex items-center justify-center gap-2 bg-[#21ADE7] hover:bg-[#1D9BCE] text-white py-2 px-4 rounded-md transition-colors duration-300"
      >
        <FaFileImport size={16} />
        <span>استيراد بيانات الطالب</span>
      </button>

      {showOptions && (
        <div className="mt-4 p-4 bg-gray-50 rounded-md border border-gray-200">
          <h3 className="text-lg font-bold text-gray-700 mb-4">اختر مصدر البيانات</h3>

          {showSaveButton && (
            <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
              <div className="flex justify-between items-center">
                <p className="text-blue-700">
                  تم استيراد {importedData.length} طالب. هل تريد حفظ البيانات في قاعدة البيانات؟
                </p>
                <button
                  type="button"
                  onClick={handleSaveToDatabase}
                  disabled={isLoading}
                  className="flex items-center justify-center gap-2 bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md transition-colors duration-300"
                >
                  {isLoading ? <FaSpinner className="animate-spin" /> : <FaCheck />}
                  <span>حفظ في قاعدة البيانات</span>
                </button>
              </div>
            </div>
          )}

          <div className="space-y-4">
            {/* استيراد من ملف CSV */}
            <div className="p-4 bg-white rounded-md border border-gray-200">
              <h4 className="flex items-center text-md font-bold text-gray-700 mb-2">
                <FaFileCsv className="ml-2 text-[#217346]" />
                استيراد من ملف CSV
              </h4>
              <div className="space-y-3">
                <div>
                  <label htmlFor="csvFile" className="block text-sm font-medium text-gray-700 mb-1">
                    اختر ملف CSV
                  </label>
                  <input
                    type="file"
                    id="csvFile"
                    accept=".csv"
                    onChange={handleCSVImport}
                    className="w-full p-2 border border-gray-300 rounded-md"
                  />
                </div>
                <div className="flex gap-2">
                  <button
                    type="button"
                    onClick={handleDownloadCSVTemplate}
                    className="flex items-center justify-center gap-2 bg-[#217346] hover:bg-[#1a5c38] text-white py-2 px-4 rounded-md transition-colors duration-300 w-full"
                  >
                    <FaFileCsv size={16} />
                    <span>تنزيل نموذج CSV</span>
                  </button>
                  <button
                    type="button"
                    onClick={handleDownloadExcelTemplate}
                    className="flex items-center justify-center gap-2 bg-[#1F497D] hover:bg-[#163a64] text-white py-2 px-4 rounded-md transition-colors duration-300 w-full"
                  >
                    <FaFileExcel size={16} />
                    <span>تنزيل نموذج Excel</span>
                  </button>
                </div>
              </div>
            </div>

            {/* استيراد من ملف Excel */}
            <div className="p-4 bg-white rounded-md border border-gray-200">
              <h4 className="flex items-center text-md font-bold text-gray-700 mb-2">
                <FaFileExcel className="ml-2 text-[#217346]" />
                استيراد من ملف Excel
              </h4>
              <div>
                <label htmlFor="excelFile" className="block text-sm font-medium text-gray-700 mb-1">
                  اختر ملف Excel
                </label>
                <input
                  type="file"
                  id="excelFile"
                  accept=".xlsx, .xls"
                  onChange={handleExcelImport}
                  className="w-full p-2 border border-gray-300 rounded-md"
                />
              </div>
            </div>

            {/* استيراد من ملف JSON */}
            <div className="p-4 bg-white rounded-md border border-gray-200">
              <h4 className="flex items-center text-md font-bold text-gray-700 mb-2">
                <FaFileCode className="ml-2 text-[#F0DB4F]" />
                استيراد من ملف JSON
              </h4>
              <div className="space-y-3">
                <div>
                  <label htmlFor="jsonFile" className="block text-sm font-medium text-gray-700 mb-1">
                    اختر ملف JSON
                  </label>
                  <input
                    type="file"
                    id="jsonFile"
                    accept=".json"
                    onChange={handleJSONImport}
                    className="w-full p-2 border border-gray-300 rounded-md"
                  />
                </div>
                <button
                  type="button"
                  onClick={handleCreateJSONTemplate}
                  className="flex items-center justify-center gap-2 bg-[#F0DB4F] hover:bg-[#e6d349] text-black py-2 px-4 rounded-md transition-colors duration-300 w-full"
                >
                  <FaFileCode size={16} />
                  <span>تنزيل نموذج JSON</span>
                </button>
              </div>
            </div>


          </div>

          {/* رسائل النجاح والخطأ */}
          {error && (
            <div className="mt-4 p-3 bg-red-50 text-red-700 rounded-md border border-red-200 flex items-start">
              <FaExclamationTriangle className="ml-2 mt-1 flex-shrink-0" />
              <span>{error}</span>
            </div>
          )}

          {success && (
            <div className="mt-4 p-3 bg-green-50 text-green-700 rounded-md border border-green-200 flex items-start">
              <FaCheck className="ml-2 mt-1 flex-shrink-0" />
              <span>{success}</span>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default ImportStudentData;
