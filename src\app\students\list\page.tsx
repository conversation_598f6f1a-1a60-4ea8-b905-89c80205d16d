"use client";

import React, { useState, useEffect, useRef } from 'react';
import { useRouter } from 'next/navigation';
import Layout from "@/components/Layout";
import { Student } from '@/types/table.types';
import { supabase } from '@/lib/supabase';
import {
  FaPlus,
  FaUserGraduate,
  FaSearch,
  FaEdit,
  FaTrash,
  FaEye,
  FaTimes,
  FaCheck,
  FaSave,
  FaUndo
} from 'react-icons/fa';
import { useSupabaseRealtime } from '@/hooks/useSupabaseRealtime';
import ExpandableStudentEdit from '@/components/students/ExpandableStudentEdit';

export default function StudentList() {
  const router = useRouter();

  // حالة البيانات
  const [students, setStudents] = useState<Student[]>([]);
  const [filteredStudents, setFilteredStudents] = useState<Student[]>([]);
  const [searchQuery, setSearchQuery] = useState('');

  // حالة التحميل والأخطاء
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  // حالة التحرير
  const [editingStudent, setEditingStudent] = useState<Student | null>(null);
  const [editFormData, setEditFormData] = useState<Partial<Student>>({});

  // حالة الحذف
  const [deletingStudent, setDeletingStudent] = useState<Student | null>(null);

  // حالة الإضافة
  const [isAddingStudent, setIsAddingStudent] = useState(false);
  const [newStudent, setNewStudent] = useState<Partial<Student>>({
    full_name: '',
    id_number: '',
    birth_date: new Date().toISOString().split('T')[0],
    gender: 'male',
    phone: '',
    email: '',
  });

  // مراجع للتحكم في التحميل
  const initialLoadDoneRef = useRef(false);

  // إعداد اشتراك الوقت الحقيقي مع Supabase
  useSupabaseRealtime(
    {
      table: 'students',
      event: '*',
    },
    (payload) => {
      console.log('Realtime update received:', payload);

      if (payload.eventType === 'INSERT') {
        // إضافة طالب جديد
        setStudents(prev => {
          const newData = [...prev, payload.new as Student];
          applySearchFilter(newData, searchQuery);
          return newData;
        });

        // عرض رسالة نجاح إذا كنا نضيف طالب
        if (isAddingStudent) {
          setSuccessMessage('تم إضافة الطالب بنجاح');
          setIsAddingStudent(false);
          setNewStudent({
            full_name: '',
            id_number: '',
            birth_date: new Date().toISOString().split('T')[0],
            gender: 'male',
            phone: '',
            email: '',
          });

          // إخفاء رسالة النجاح بعد 3 ثوان
          setTimeout(() => {
            setSuccessMessage(null);
          }, 3000);
        }
      }
      else if (payload.eventType === 'UPDATE') {
        // تحديث طالب موجود
        setStudents(prev => {
          const updatedData = prev.map(student =>
            student.id === payload.new.id ? payload.new as Student : student
          );
          applySearchFilter(updatedData, searchQuery);
          return updatedData;
        });

        // إلغاء وضع التحرير إذا كنا نحرر هذا الطالب
        if (editingStudent?.id === payload.new.id) {
          setEditingStudent(null);
          setSuccessMessage('تم تحديث بيانات الطالب بنجاح');

          // إخفاء رسالة النجاح بعد 3 ثوان
          setTimeout(() => {
            setSuccessMessage(null);
          }, 3000);
        }
      }
      else if (payload.eventType === 'DELETE') {
        // حذف طالب
        setStudents(prev => {
          const filteredData = prev.filter(student => student.id !== payload.old.id);
          applySearchFilter(filteredData, searchQuery);
          return filteredData;
        });

        // إلغاء وضع الحذف إذا كنا نحذف هذا الطالب
        if (deletingStudent?.id === payload.old.id) {
          setDeletingStudent(null);
          setSuccessMessage('تم حذف الطالب بنجاح');

          // إخفاء رسالة النجاح بعد 3 ثوان
          setTimeout(() => {
            setSuccessMessage(null);
          }, 3000);
        }
      }
    }
  );

  // جلب بيانات الطلاب عند تحميل الصفحة
  useEffect(() => {
    if (!initialLoadDoneRef.current) {
      fetchStudents();
      initialLoadDoneRef.current = true;
    }
  }, []);

  // تطبيق البحث عند تغيير استعلام البحث
  useEffect(() => {
    applySearchFilter(students, searchQuery);
  }, [searchQuery, students]);

  // دالة لجلب بيانات الطلاب
  const fetchStudents = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const { data, error } = await supabase
        .from('students')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) {
        throw new Error(error.message);
      }

      setStudents(data || []);
      setFilteredStudents(data || []);
    } catch (err: any) {
      console.error('Error fetching students:', err);
      setError(err.message || 'حدث خطأ أثناء جلب بيانات الطلاب');
    } finally {
      setIsLoading(false);
    }
  };

  // دالة لتطبيق البحث
  const applySearchFilter = (data: Student[], query: string) => {
    if (!query.trim()) {
      setFilteredStudents(data);
      return;
    }

    const lowercaseQuery = query.toLowerCase();
    const filtered = data.filter(student =>
      student.full_name.toLowerCase().includes(lowercaseQuery) ||
      student.id_number.toLowerCase().includes(lowercaseQuery) ||
      (student.phone && student.phone.toLowerCase().includes(lowercaseQuery)) ||
      (student.email && student.email.toLowerCase().includes(lowercaseQuery))
    );

    setFilteredStudents(filtered);
  };

  // دالة لإضافة طالب جديد
  const handleAddStudent = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      setIsLoading(true);
      setError(null);

      // التحقق من البيانات المطلوبة
      if (!newStudent.full_name || !newStudent.id_number || !newStudent.birth_date || !newStudent.gender) {
        throw new Error('يرجى ملء جميع الحقول المطلوبة');
      }

      // إضافة الطالب إلى قاعدة البيانات
      const { error } = await supabase
        .from('students')
        .insert({
          ...newStudent,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        });

      if (error) {
        throw new Error(error.message);
      }

      // سيتم تحديث البيانات تلقائيًا من خلال اشتراك الوقت الحقيقي
    } catch (err: any) {
      console.error('Error adding student:', err);
      setError(err.message || 'حدث خطأ أثناء إضافة الطالب');
    } finally {
      setIsLoading(false);
    }
  };

  // دالة لتحديث بيانات الطالب
  const handleUpdateStudent = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!editingStudent) return;

    try {
      setIsLoading(true);
      setError(null);

      // التحقق من البيانات المطلوبة
      if (!editFormData.full_name || !editFormData.id_number || !editFormData.birth_date || !editFormData.gender) {
        throw new Error('يرجى ملء جميع الحقول المطلوبة');
      }

      // تحديث بيانات الطالب في قاعدة البيانات
      const { error } = await supabase
        .from('students')
        .update({
          ...editFormData,
          updated_at: new Date().toISOString(),
        })
        .eq('id', editingStudent.id);

      if (error) {
        throw new Error(error.message);
      }

      // سيتم تحديث البيانات تلقائيًا من خلال اشتراك الوقت الحقيقي
    } catch (err: any) {
      console.error('Error updating student:', err);
      setError(err.message || 'حدث خطأ أثناء تحديث بيانات الطالب');
    } finally {
      setIsLoading(false);
    }
  };

  // دالة لحذف الطالب
  const handleDeleteStudent = async () => {
    if (!deletingStudent) return;

    try {
      setIsLoading(true);
      setError(null);

      // حذف الطالب من قاعدة البيانات
      const { error } = await supabase
        .from('students')
        .delete()
        .eq('id', deletingStudent.id);

      if (error) {
        throw new Error(error.message);
      }

      // سيتم تحديث البيانات تلقائيًا من خلال اشتراك الوقت الحقيقي
    } catch (err: any) {
      console.error('Error deleting student:', err);
      setError(err.message || 'حدث خطأ أثناء حذف الطالب');
    } finally {
      setIsLoading(false);
    }
  };

  // دالة لبدء تحرير طالب
  const startEditing = (student: Student) => {
    setEditingStudent(student);
    setEditFormData({ ...student });
  };

  // دالة لإلغاء التحرير
  const cancelEditing = () => {
    setEditingStudent(null);
    setEditFormData({});
  };

  // دالة لعرض تفاصيل الطالب
  const handleViewStudent = (student: Student) => {
    router.push(`/students/view/${student.id}`);
  };

  // تنسيق التاريخ للعرض
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-EG');
  };

  return (
    <Layout>
      <div className="p-6">
        {/* رأس الصفحة */}
        <div className="mb-6 bg-gradient-to-l from-[#21ADE7]/10 to-transparent p-4 rounded-lg border-r-4 border-[#21ADE7]">
          <div className="flex justify-between items-center">
            <div className="flex items-center">
              <div className="w-12 h-12 rounded-full bg-[#21ADE7] flex items-center justify-center text-white ml-4">
                <FaUserGraduate size={24} />
              </div>
              <div>
                <h1 className="text-2xl font-bold" style={{ color: '#21ADE7' }}>قائمة الطلاب</h1>
                <p className="text-gray-600 mt-1">
                  عرض وإدارة بيانات الطلاب المسجلين في النظام
                </p>
              </div>
            </div>
            <button
              onClick={() => setIsAddingStudent(true)}
              className="bg-[#21ADE7] hover:bg-[#21ADE7]/90 text-white px-4 py-2 rounded-md flex items-center transition-colors duration-200 clickable"
            >
              <FaPlus className="ml-2" />
              إضافة طالب
            </button>
          </div>
        </div>

        {/* رسائل النجاح والخطأ */}
        {successMessage && (
          <div className="mb-6 p-4 bg-green-100 border-r-4 border-green-500 rounded-md">
            <p className="text-green-700">{successMessage}</p>
          </div>
        )}

        {error && (
          <div className="mb-6 p-4 bg-[#FD1361]/10 border-r-4 border-[#FD1361] rounded-md">
            <p className="text-[#FD1361]">{error}</p>
          </div>
        )}

        {/* نموذج إضافة طالب جديد */}
        {isAddingStudent && (
          <div className="mb-6 p-4 bg-white rounded-lg shadow-md border-r-4 border-[#21ADE7]">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-bold text-[#21ADE7]">إضافة طالب جديد</h2>
              <button
                onClick={() => setIsAddingStudent(false)}
                className="text-gray-500 hover:text-gray-700"
              >
                <FaTimes size={20} />
              </button>
            </div>

            <form onSubmit={handleAddStudent} className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-gray-700 mb-2">الاسم الكامل <span className="text-red-500">*</span></label>
                <input
                  type="text"
                  value={newStudent.full_name || ''}
                  onChange={(e) => setNewStudent({...newStudent, full_name: e.target.value})}
                  className="w-full p-2 border border-gray-300 rounded-md focus:ring-[#21ADE7] focus:border-[#21ADE7]"
                  required
                />
              </div>

              <div>
                <label className="block text-gray-700 mb-2">رقم الهوية <span className="text-red-500">*</span></label>
                <input
                  type="text"
                  value={newStudent.id_number || ''}
                  onChange={(e) => setNewStudent({...newStudent, id_number: e.target.value})}
                  className="w-full p-2 border border-gray-300 rounded-md focus:ring-[#21ADE7] focus:border-[#21ADE7]"
                  required
                />
              </div>

              <div>
                <label className="block text-gray-700 mb-2">تاريخ الميلاد <span className="text-red-500">*</span></label>
                <input
                  type="date"
                  value={newStudent.birth_date || ''}
                  onChange={(e) => setNewStudent({...newStudent, birth_date: e.target.value})}
                  className="w-full p-2 border border-gray-300 rounded-md focus:ring-[#21ADE7] focus:border-[#21ADE7]"
                  required
                />
              </div>

              <div>
                <label className="block text-gray-700 mb-2">الجنس <span className="text-red-500">*</span></label>
                <select
                  value={newStudent.gender || 'male'}
                  onChange={(e) => setNewStudent({...newStudent, gender: e.target.value})}
                  className="w-full p-2 border border-gray-300 rounded-md focus:ring-[#21ADE7] focus:border-[#21ADE7]"
                  required
                >
                  <option value="male">ذكر</option>
                  <option value="female">أنثى</option>
                </select>
              </div>

              <div>
                <label className="block text-gray-700 mb-2">رقم الهاتف</label>
                <input
                  type="tel"
                  value={newStudent.phone || ''}
                  onChange={(e) => setNewStudent({...newStudent, phone: e.target.value})}
                  className="w-full p-2 border border-gray-300 rounded-md focus:ring-[#21ADE7] focus:border-[#21ADE7]"
                />
              </div>

              <div>
                <label className="block text-gray-700 mb-2">البريد الإلكتروني</label>
                <input
                  type="email"
                  value={newStudent.email || ''}
                  onChange={(e) => setNewStudent({...newStudent, email: e.target.value})}
                  className="w-full p-2 border border-gray-300 rounded-md focus:ring-[#21ADE7] focus:border-[#21ADE7]"
                />
              </div>

              <div className="md:col-span-2 flex justify-end mt-4">
                <button
                  type="button"
                  onClick={() => setIsAddingStudent(false)}
                  className="px-4 py-2 bg-gray-200 text-gray-700 rounded-md ml-2 hover:bg-gray-300 transition-colors duration-200"
                >
                  إلغاء
                </button>
                <button
                  type="submit"
                  className="px-4 py-2 bg-[#21ADE7] text-white rounded-md hover:bg-[#21ADE7]/90 transition-colors duration-200 flex items-center"
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white ml-2"></div>
                      جاري الحفظ...
                    </>
                  ) : (
                    <>
                      <FaSave className="ml-2" />
                      حفظ
                    </>
                  )}
                </button>
              </div>
            </form>
          </div>
        )}

        {/* مربع البحث */}
        <div className="mb-6 bg-white rounded-lg shadow-md p-4 border-r-4 border-[#21ADE7]">
          <div className="relative">
            <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
              <FaSearch className="text-gray-400" />
            </div>
            <input
              type="text"
              placeholder="ابحث عن طالب..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="block w-full p-2 pr-10 text-sm text-gray-900 border border-gray-300 rounded-lg bg-gray-50 focus:ring-[#21ADE7] focus:border-[#21ADE7]"
            />
          </div>
        </div>

        {/* جدول الطلاب */}
        <div className="bg-white rounded-lg shadow-md p-4 border-r-4 border-[#21ADE7]">
          {/* عرض عدد النتائج */}
          <div className="mb-4 flex justify-between items-center">
            <div className="flex items-center text-sm text-gray-500">
              {filteredStudents.length} من أصل {students.length} سجل
            </div>
          </div>

          {/* الجدول */}
          <div className="overflow-x-auto">
            <table className="w-full text-sm text-right text-gray-700 border-collapse">
              <thead className="text-xs text-black uppercase bg-gray-100">
                <tr>
                  <th className="px-4 py-3 font-medium border-b" style={{ width: '50px' }}>
                    <div className="flex items-center justify-between">#</div>
                  </th>
                  <th className="px-4 py-3 font-medium border-b">
                    <div className="flex items-center justify-between cursor-pointer select-none">
                      اسم الطالب
                    </div>
                  </th>
                  <th className="px-4 py-3 font-medium border-b">
                    <div className="flex items-center justify-between cursor-pointer select-none">
                      رقم الهوية
                    </div>
                  </th>
                  <th className="px-4 py-3 font-medium border-b">
                    <div className="flex items-center justify-between cursor-pointer select-none">
                      الجنس
                    </div>
                  </th>
                  <th className="px-4 py-3 font-medium border-b">
                    <div className="flex items-center justify-between cursor-pointer select-none">
                      تاريخ الميلاد
                    </div>
                  </th>
                  <th className="px-4 py-3 font-medium border-b">
                    <div className="flex items-center justify-between cursor-pointer select-none">
                      رقم الهاتف
                    </div>
                  </th>
                  <th className="px-4 py-3 font-medium border-b">
                    <div className="flex items-center justify-between">
                      الإجراءات
                    </div>
                  </th>
                </tr>
              </thead>
              <tbody>
                {isLoading ? (
                  <tr>
                    <td colSpan={7} className="px-4 py-8 text-center">
                      <div className="flex justify-center items-center">
                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#21ADE7]"></div>
                        <span className="mr-3">جاري التحميل...</span>
                      </div>
                    </td>
                  </tr>
                ) : filteredStudents.length === 0 ? (
                  <tr>
                    <td colSpan={7} className="px-4 py-8 text-center text-gray-500">
                      لا يوجد طلاب
                    </td>
                  </tr>
                ) : (
                  filteredStudents.map((student, index) => (
                    <tr
                      key={student.id}
                      className={`${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'} hover:bg-gray-100 transition-colors duration-150 ease-in-out`}
                    >
                      {/* إذا كان الطالب قيد التحرير، نعرض نموذج التحرير الموسع */}
                      {editingStudent?.id === student.id ? (
                        <td colSpan={7} className="px-0 py-0">
                          <ExpandableStudentEdit
                            student={student}
                            onCancel={cancelEditing}
                            onSave={() => {
                              // سيتم تحديث البيانات تلقائيًا من خلال اشتراك الوقت الحقيقي
                              setEditingStudent(null);
                              setSuccessMessage('تم تحديث بيانات الطالب بنجاح');
                              setTimeout(() => {
                                setSuccessMessage(null);
                              }, 3000);
                            }}
                          />
                        </td>
                      ) : (
                        // عرض بيانات الطالب العادية
                        <>
                          <td className="px-4 py-3 border-b">{index + 1}</td>
                          <td className="px-4 py-3 border-b font-medium">{student.full_name}</td>
                          <td className="px-4 py-3 border-b">{student.id_number}</td>
                          <td className="px-4 py-3 border-b">
                            {student.gender === 'male' ? 'ذكر' : 'أنثى'}
                          </td>
                          <td className="px-4 py-3 border-b">{formatDate(student.birth_date)}</td>
                          <td className="px-4 py-3 border-b">{student.phone || '-'}</td>
                          <td className="px-4 py-3 border-b">
                            <div className="flex items-center space-x-2 space-x-reverse">
                              <button
                                onClick={() => handleViewStudent(student)}
                                className="p-1 text-blue-600 hover:text-blue-800 transition-colors duration-200"
                                title="عرض التفاصيل"
                              >
                                <FaEye size={18} />
                              </button>
                              <button
                                onClick={() => startEditing(student)}
                                className="p-1 text-green-600 hover:text-green-800 transition-colors duration-200"
                                title="تعديل"
                              >
                                <FaEdit size={18} />
                              </button>
                              <button
                                onClick={() => setDeletingStudent(student)}
                                className="p-1 text-red-600 hover:text-red-800 transition-colors duration-200"
                                title="حذف"
                              >
                                <FaTrash size={18} />
                              </button>
                            </div>
                          </td>
                        </>
                      )}
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        </div>

        {/* مربع حوار تأكيد الحذف */}
        {deletingStudent && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 max-w-md w-full">
              <h3 className="text-xl font-bold mb-4 text-gray-900">تأكيد الحذف</h3>
              <p className="mb-6 text-gray-700">
                هل أنت متأكد من رغبتك في حذف الطالب "{deletingStudent.full_name}"؟ لا يمكن التراجع عن هذا الإجراء.
              </p>
              <div className="flex justify-end">
                <button
                  onClick={() => setDeletingStudent(null)}
                  className="px-4 py-2 bg-gray-200 text-gray-700 rounded-md ml-2 hover:bg-gray-300 transition-colors duration-200"
                >
                  إلغاء
                </button>
                <button
                  onClick={handleDeleteStudent}
                  className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors duration-200 flex items-center"
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white ml-2"></div>
                      جاري الحذف...
                    </>
                  ) : (
                    <>
                      <FaTrash className="ml-2" />
                      تأكيد الحذف
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </Layout>
  );
}
