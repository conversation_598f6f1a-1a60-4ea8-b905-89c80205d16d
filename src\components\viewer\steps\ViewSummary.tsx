"use client";

import React, { useState, useEffect } from 'react';
import { supabase } from '@/lib/supabase';
import { FinancialInfo, Installment, OtherPayment } from '@/types/table.types';
import { FaFileInvoiceDollar, FaMoneyBillWave, FaMoneyCheckAlt, FaArrowDown, FaArrowUp, FaEquals, FaSync } from 'react-icons/fa';

interface ViewSummaryProps {
  studentId: string | number;
}

const ViewSummary: React.FC<ViewSummaryProps> = ({ studentId }) => {
  const [financialInfo, setFinancialInfo] = useState<FinancialInfo | null>(null);
  const [installments, setInstallments] = useState<Installment[]>([]);
  const [otherPayments, setOtherPayments] = useState<OtherPayment[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastRefresh, setLastRefresh] = useState<Date>(new Date());

  // تحميل البيانات المالية
  const loadData = async () => {
    try {
      setIsLoading(true);
      setError(null);

      if (!studentId) {
        throw new Error('معرف الطالب غير موجود');
      }

      console.log('Loading summary data for student:', studentId);

      // جلب المعلومات المالية مباشرة من قاعدة البيانات
      const { data: financialData, error: financialError } = await supabase
        .from('financial_info')
        .select('*')
        .eq('student_id', studentId)
        .single();

      if (financialError && financialError.code !== 'PGRST116') {
        console.error('Error fetching financial info for summary:', financialError);
        throw new Error(financialError.message);
      }

      console.log('Financial info loaded for summary:', financialData);
      setFinancialInfo(financialData);

      // جلب الأقساط إذا وجدت معلومات مالية
      if (financialData) {
        const { data: installmentsData, error: installmentsError } = await supabase
          .from('installments')
          .select('*')
          .eq('financial_info_id', financialData.id);

        if (installmentsError) {
          console.error('Error fetching installments for summary:', installmentsError);
          throw new Error(installmentsError.message);
        }

        console.log('Installments loaded for summary:', installmentsData);
        setInstallments(installmentsData || []);
      }

      // جلب المدفوعات الأخرى
      const { data: paymentsData, error: paymentsError } = await supabase
        .from('other_payments')
        .select('*')
        .eq('student_id', studentId);

      if (paymentsError) {
        console.error('Error fetching other payments for summary:', paymentsError);
        throw new Error(paymentsError.message);
      }

      console.log('Other payments loaded for summary:', paymentsData);
      setOtherPayments(paymentsData || []);

      setLastRefresh(new Date());
    } catch (err: any) {
      console.error('Error fetching financial summary:', err);
      setError(err.message || 'حدث خطأ أثناء جلب البيانات المالية');
    } finally {
      setIsLoading(false);
    }
  };

  // تحميل البيانات عند تغيير معرف الطالب
  useEffect(() => {
    loadData();

    // إعداد اشتراك الوقت الحقيقي للبيانات المالية
    const channel = supabase.channel('summary-changes');
    
    // الاشتراك في تغييرات المعلومات المالية
    channel.on('postgres_changes', {
      event: '*',
      schema: 'public',
      table: 'financial_info',
      filter: `student_id=eq.${studentId}`
    }, (payload) => {
      console.log('Financial info change received for summary:', payload);
      loadData();
    }).subscribe();
    
    // الاشتراك في تغييرات الأقساط
    channel.on('postgres_changes', {
      event: '*',
      schema: 'public',
      table: 'installments'
    }, (payload) => {
      console.log('Installments change received for summary:', payload);
      loadData();
    }).subscribe();
    
    // الاشتراك في تغييرات المدفوعات الأخرى
    channel.on('postgres_changes', {
      event: '*',
      schema: 'public',
      table: 'other_payments',
      filter: `student_id=eq.${studentId}`
    }, (payload) => {
      console.log('Other payments change received for summary:', payload);
      loadData();
    }).subscribe();
    
    // دالة التنظيف
    return () => {
      supabase.removeChannel(channel);
    };
  }, [studentId]);

  // تنسيق المبلغ
  const formatAmount = (amount: number | null | undefined) => {
    if (amount === null || amount === undefined) {
      return '0 جنيه مصري';
    }
    return amount.toLocaleString('ar-EG', { style: 'currency', currency: 'EGP' });
  };

  // حساب إجمالي المدفوعات الأخرى
  const calculateTotalOtherPayments = () => {
    if (otherPayments.length === 0) return 0;
    return otherPayments.reduce((total, payment) => total + payment.total_amount, 0);
  };

  // حساب إجمالي الأقساط المدفوعة
  const calculateTotalPaidInstallments = () => {
    return installments
      .filter(installment => installment.status === 'paid')
      .reduce((total, installment) => total + installment.amount, 0);
  };

  // حساب إجمالي الأقساط المتبقية
  const calculateTotalRemainingInstallments = () => {
    return installments
      .filter(installment => installment.status !== 'paid')
      .reduce((total, installment) => total + installment.amount, 0);
  };

  // حساب إجمالي المدفوعات
  const calculateTotalPaid = () => {
    return calculateTotalPaidInstallments() + calculateTotalOtherPayments();
  };

  // حساب إجمالي المبلغ المستحق
  const calculateTotalDue = () => {
    if (!financialInfo) return 0;
    return financialInfo.tuition_fee - financialInfo.discount_amount;
  };

  // حساب المبلغ المتبقي
  const calculateRemainingAmount = () => {
    return calculateTotalDue() - calculateTotalPaid();
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center p-8">
        <div className="relative">
          <div className="w-12 h-12 border-4 border-[#21ADE7]/30 border-t-[#21ADE7] rounded-full animate-spin"></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
            <FaFileInvoiceDollar size={16} className="text-[#21ADE7]" />
          </div>
        </div>
        <p className="mr-3 text-gray-600">جاري تحميل البيانات...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-4 bg-[#FD1361]/10 border-r-4 border-[#FD1361] rounded-md">
        <p className="text-[#FD1361]">{error}</p>
        <button
          onClick={loadData}
          className="mt-2 px-4 py-2 bg-[#FD1361] text-white rounded-md hover:bg-[#FD1361]/80 flex items-center"
        >
          <FaSync className="ml-2" /> إعادة المحاولة
        </button>
      </div>
    );
  }

  if (!financialInfo && otherPayments.length === 0) {
    return (
      <div className="p-4 bg-gray-100 border-r-4 border-gray-400 rounded-md">
        <p className="text-gray-600">لا توجد بيانات مالية للطالب (معرف الطالب: {studentId})</p>
        <button
          onClick={loadData}
          className="mt-2 px-4 py-2 bg-[#21ADE7] text-white rounded-md hover:bg-[#21ADE7]/80 flex items-center"
        >
          <FaSync className="ml-2" /> تحديث البيانات
        </button>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg p-4">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-bold text-[#21ADE7]">الإجماليات المالية</h3>
        <button
          onClick={loadData}
          className="p-2 bg-gray-100 text-gray-600 rounded-full hover:bg-gray-200"
          title="تحديث البيانات"
        >
          <FaSync size={14} />
        </button>
      </div>
      <div className="grid grid-cols-1 gap-6">
        {/* إجمالي المبلغ المستحق */}
        <div className="bg-[#21ADE7]/10 p-4 rounded-lg">
          <div className="flex items-center mb-2">
            <FaFileInvoiceDollar size={20} className="text-[#21ADE7] ml-2" />
            <h3 className="text-lg font-bold text-[#21ADE7]">إجمالي المبلغ المستحق</h3>
          </div>
          <p className="text-2xl font-bold text-[#21ADE7] text-center my-4">
            {formatAmount(calculateTotalDue())}
          </p>
        </div>

        {/* إجمالي المدفوعات */}
        <div className="bg-[#0ABB87]/10 p-4 rounded-lg">
          <div className="flex items-center mb-2">
            <FaArrowUp size={20} className="text-[#0ABB87] ml-2" />
            <h3 className="text-lg font-bold text-[#0ABB87]">إجمالي المدفوعات</h3>
          </div>
          <p className="text-2xl font-bold text-[#0ABB87] text-center my-4">
            {formatAmount(calculateTotalPaid())}
          </p>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
            <div className="bg-white p-3 rounded-lg shadow-sm">
              <div className="flex items-center mb-1">
                <FaMoneyBillWave size={16} className="text-[#5578EB] ml-2" />
                <h4 className="text-sm font-medium text-gray-700">الأقساط المدفوعة</h4>
              </div>
              <p className="text-lg font-bold text-[#5578EB] text-center">
                {formatAmount(calculateTotalPaidInstallments())}
              </p>
            </div>

            <div className="bg-white p-3 rounded-lg shadow-sm">
              <div className="flex items-center mb-1">
                <FaMoneyCheckAlt size={16} className="text-[#384AD7] ml-2" />
                <h4 className="text-sm font-medium text-gray-700">المدفوعات الأخرى</h4>
              </div>
              <p className="text-lg font-bold text-[#384AD7] text-center">
                {formatAmount(calculateTotalOtherPayments())}
              </p>
            </div>
          </div>
        </div>

        {/* المبلغ المتبقي */}
        <div className="bg-[#FD1361]/10 p-4 rounded-lg">
          <div className="flex items-center mb-2">
            <FaArrowDown size={20} className="text-[#FD1361] ml-2" />
            <h3 className="text-lg font-bold text-[#FD1361]">المبلغ المتبقي</h3>
          </div>
          <p className="text-2xl font-bold text-[#FD1361] text-center my-4">
            {formatAmount(calculateRemainingAmount())}
          </p>

          {installments.length > 0 && (
            <div className="bg-white p-3 rounded-lg shadow-sm mt-4">
              <div className="flex items-center mb-1">
                <FaEquals size={16} className="text-[#5578EB] ml-2" />
                <h4 className="text-sm font-medium text-gray-700">الأقساط المتبقية</h4>
              </div>
              <p className="text-lg font-bold text-[#5578EB] text-center">
                {formatAmount(calculateTotalRemainingInstallments())}
              </p>
            </div>
          )}
        </div>
      </div>
      <div className="mt-4 text-xs text-gray-400 text-left">
        آخر تحديث: {lastRefresh.toLocaleTimeString()}
      </div>
    </div>
  );
};

export default ViewSummary;
