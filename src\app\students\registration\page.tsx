"use client";

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Layout from '@/components/Layout';
import {
  StepWizard,
  Step1BasicInfo,
  Step2GuardianInfo,
  Step3FinancialInfo,
  Step4Installments,
  Step5OtherPayments,
  Step6Summary,
  Step7HealthInfo,
  Step8AdditionalInfo,
  Step9Attachments
} from '@/components/registration';
import { supabase } from '@/lib/supabase';
import {
  FaUser, FaUserFriends, FaMoneyBillWave, FaCalendarAlt,
  FaMoneyCheckAlt, FaFileInvoiceDollar, FaHeartbeat,
  FaInfoCircle, FaPaperclip, FaUserPlus, FaExclamationTriangle,
  FaCheckCircle, FaHistory
} from 'react-icons/fa';

export default function StudentRegistration() {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);


  // تعريف خطوات التسجيل مع إضافة أيقونات وألوان
  const steps = [
    {
      id: 1,
      title: 'البيانات الأساسية',
      component: <Step1BasicInfo onComplete={() => {}} formData={{}} />,
      isRequired: true,
      icon: <FaUser size={20} />,
      color: '#21ADE7', // أزرق فاتح
    },
    {
      id: 2,
      title: 'ولي الأمر',
      component: <Step2GuardianInfo onComplete={() => {}} formData={{}} />,
      isRequired: true,
      icon: <FaUserFriends size={20} />,
      color: '#5578EB', // أزرق داكن
    },
    {
      id: 3,
      title: 'القسم المالي',
      component: <Step3FinancialInfo onComplete={() => {}} formData={{}} />,
      isRequired: true,
      icon: <FaMoneyBillWave size={20} />,
      color: '#FD1361', // أحمر
    },
    {
      id: 4,
      title: 'الأقساط',
      component: <Step4Installments onComplete={() => {}} formData={{}} />,
      isRequired: false,
      icon: <FaCalendarAlt size={20} />,
      color: '#0ABB87', // أخضر
    },
    {
      id: 5,
      title: 'مدفوعات أخرى',
      component: <Step5OtherPayments onComplete={() => {}} formData={{}} />,
      isRequired: true,
      icon: <FaMoneyCheckAlt size={20} />,
      color: '#384AD7', // أزرق غامق
    },
    {
      id: 6,
      title: 'الإجماليات',
      component: <Step6Summary onComplete={() => {}} formData={{}} />,
      isRequired: true,
      icon: <FaFileInvoiceDollar size={20} />,
      color: '#21ADE7', // أزرق فاتح
    },
    {
      id: 7,
      title: 'معلومات صحية',
      component: <Step7HealthInfo onComplete={() => {}} formData={{}} />,
      isRequired: false,
      icon: <FaHeartbeat size={20} />,
      color: '#FD1361', // أحمر
    },
    {
      id: 8,
      title: 'معلومات إضافية',
      component: <Step8AdditionalInfo onComplete={() => {}} formData={{}} />,
      isRequired: false,
      icon: <FaInfoCircle size={20} />,
      color: '#5578EB', // أزرق داكن
    },
    {
      id: 9,
      title: 'المرفقات (اختياري)',
      component: <Step9Attachments onComplete={() => {}} formData={{}} />,
      isRequired: false,
      icon: <FaPaperclip size={20} />,
      color: '#0ABB87', // أخضر
    },
  ];

  // معالجة إكمال التسجيل
  const handleComplete = async (formData: any) => {
    setIsSubmitting(true);
    setError(null);
    setSuccess(null);



    try {
      // التحقق من وجود طالب بنفس رقم الهوية
      const { data: existingStudent, error: checkError } = await supabase
        .from('students')
        .select('id')
        .eq('id_number', formData.idNumber)
        .maybeSingle();

      if (checkError) {
        console.error('Error checking existing student:', checkError);
      } else if (existingStudent) {
        throw new Error(`يوجد طالب مسجل بالفعل برقم الهوية ${formData.idNumber}`);
      }

      // 1. إدخال بيانات الطالب الأساسية
      const { data: studentData, error: studentError } = await supabase
        .from('students')
        .insert({
          full_name: formData.fullName,
          id_number: formData.idNumber,
          birth_date: formData.birthDate,
          gender: formData.gender,
          religion: formData.religion || null,
          marital_status: formData.maritalStatus || null,
          email: formData.email || null,
          phone: formData.phone || null,
        })
        .select()
        .single();

      if (studentError) {
        console.error('Error inserting student data:', studentError);
        throw new Error('حدث خطأ أثناء حفظ بيانات الطالب: ' + studentError.message);
      }

      const studentId = studentData.id;

      // 2. إدخال بيانات أولياء الأمور
      if (formData.guardians && formData.guardians.length > 0) {
        const guardiansToInsert = formData.guardians.map((guardian: any) => ({
          student_id: studentId,
          full_name: guardian.fullName,
          relationship: guardian.relationship,
          id_number: guardian.idNumber,
          phone: guardian.phone,
          email: guardian.email || null,
          occupation: guardian.occupation || null,
          workplace: guardian.workplace || null,
        }));

        const { error: guardiansError } = await supabase
          .from('guardians')
          .insert(guardiansToInsert);

        if (guardiansError) {
          console.error('Error inserting guardians data:', guardiansError);
          throw new Error('حدث خطأ أثناء حفظ بيانات أولياء الأمور: ' + guardiansError.message);
        }
      }

      // 3. إدخال البيانات المالية
      const { data: financialData, error: financialError } = await supabase
        .from('financial_info')
        .insert({
          student_id: studentId,
          tuition_fee: parseFloat(formData.tuitionFee),
          discount_amount: parseFloat(formData.discountAmount) || 0,
          discount_reason: formData.discountReason || null,
          paid_amount: parseFloat(formData.paidAmount),
          payment_method: formData.paymentMethod,
          installments_count: formData.paymentMethod === 'installments' ? parseInt(formData.installmentsCount) : null,
        })
        .select()
        .single();

      if (financialError) {
        console.error('Error inserting financial data:', financialError);
        throw new Error('حدث خطأ أثناء حفظ البيانات المالية: ' + financialError.message);
      }

      // 4. إدخال الأقساط (إذا كانت طريقة الدفع بالتقسيط)
      if (formData.paymentMethod === 'installments' && formData.installments && formData.installments.length > 0) {
        const installmentsToInsert = formData.installments.map((installment: any) => ({
          financial_info_id: financialData.id,
          due_date: installment.dueDate,
          amount: parseFloat(installment.amount),
          paid: parseFloat(installment.paid) || 0,
          discount: parseFloat(installment.discount) || 0,
          status: installment.status || 'pending',
        }));

        const { error: installmentsError } = await supabase
          .from('installments')
          .insert(installmentsToInsert);

        if (installmentsError) {
          console.error('Error inserting installments data:', installmentsError);
          throw new Error('حدث خطأ أثناء حفظ بيانات الأقساط: ' + installmentsError.message);
        }
      }

      // 5. إدخال المدفوعات الأخرى
      const { error: otherPaymentsError } = await supabase
        .from('other_payments')
        .insert({
          student_id: studentId,
          file_opening_fee: parseFloat(formData.fileOpeningFee),
          books_fee: parseFloat(formData.booksFee),
          uniform_count: parseInt(formData.uniformCount) || 0,
          uniform_total: parseFloat(formData.uniformTotal) || 0,
          transportation_fee: parseFloat(formData.transportationFee),
          total_amount: parseFloat(formData.totalAmount),
        });

      if (otherPaymentsError) {
        console.error('Error inserting other payments data:', otherPaymentsError);
        throw new Error('حدث خطأ أثناء حفظ بيانات المدفوعات الأخرى: ' + otherPaymentsError.message);
      }

      // 6. إدخال المعلومات الصحية (اختياري)
      if (formData.generalHealth || formData.chronicDiseases || formData.drugAllergies) {
        const { error: healthInfoError } = await supabase
          .from('health_info')
          .insert({
            student_id: studentId,
            general_health: formData.generalHealth || null,
            chronic_diseases: formData.chronicDiseases || null,
            drug_allergies: formData.drugAllergies || null,
            doctor_name: formData.doctorName || null,
            doctor_phone: formData.doctorPhone || null,
            insurance_company: formData.insuranceCompany || null,
            insurance_policy_number: formData.insurancePolicyNumber || null,
          });

        if (healthInfoError) {
          console.error('Error inserting health info data:', healthInfoError);
          throw new Error('حدث خطأ أثناء حفظ المعلومات الصحية: ' + healthInfoError.message);
        }
      }

      // 7. إدخال المعلومات الإضافية (اختياري)
      if (formData.hobbies || formData.achievements || formData.specialNeeds || formData.additionalNotes) {
        const { error: additionalInfoError } = await supabase
          .from('additional_info')
          .insert({
            student_id: studentId,
            hobbies: formData.hobbies || null,
            achievements: formData.achievements || null,
            special_needs: formData.specialNeeds || null,
            transportation_method: formData.transportationMethod || null,
            additional_notes: formData.additionalNotes || null,
          });

        if (additionalInfoError) {
          console.error('Error inserting additional info data:', additionalInfoError);
          throw new Error('حدث خطأ أثناء حفظ المعلومات الإضافية: ' + additionalInfoError.message);
        }
      }

      // 8. إدخال المرفقات (اختياري تمامًا)
      // تحقق من وجود أي مرفقات قبل محاولة الإدخال لتوفير مساحة التخزين
      if (formData.photoUrl || formData.birthCertificateUrl || formData.idCardUrl || formData.academicRecordUrl || formData.healthDocumentsUrl) {
        try {
          const { error: attachmentsError } = await supabase
            .from('attachments')
            .insert({
              student_id: studentId,
              photo_url: formData.photoUrl || null,
              birth_certificate_url: formData.birthCertificateUrl || null,
              id_card_url: formData.idCardUrl || null,
              academic_record_url: formData.academicRecordUrl || null,
              health_documents_url: formData.healthDocumentsUrl || null,
            });

          if (attachmentsError) {
            // فقط تسجيل الخطأ دون إيقاف العملية
            console.error('Error inserting attachments data:', attachmentsError);
            // لا نقوم برمي الخطأ هنا لأن المرفقات اختيارية
          }
        } catch (attachmentError) {
          // تسجيل الخطأ فقط دون إيقاف العملية
          console.error('Exception in attachments handling:', attachmentError);
        }
      } else {
        console.log('No attachments provided - skipping attachments step to save storage space');
      }

      // 9. إدخال السجل الأكاديمي
      const { error: academicRecordError } = await supabase
        .from('academic_records')
        .insert({
          student_id: studentId,
          grade_id: parseInt(formData.gradeId),
          class_id: parseInt(formData.classId),
          academic_year: new Date().getFullYear().toString(),
          registration_date: new Date().toISOString().split('T')[0],
        });

      if (academicRecordError) {
        console.error('Error inserting academic record data:', academicRecordError);
        throw new Error('حدث خطأ أثناء حفظ السجل الأكاديمي: ' + academicRecordError.message);
      }



      // تم التسجيل بنجاح
      setSuccess('تم تسجيل الطالب بنجاح!');

      // الانتقال إلى صفحة قائمة الطلاب بعد 2 ثانية
      setTimeout(() => {
        router.push('/students/list');
      }, 2000);
    } catch (error: any) {
      console.error('Error submitting form:', error);

      // عرض رسالة الخطأ للمستخدم
      if (error.message) {
        setError(error.message);
      } else if (error.code) {
        // رسائل خطأ Supabase
        switch (error.code) {
          case '23505':
            setError('هناك تعارض في البيانات. قد يكون هناك سجل بنفس المعرف موجود بالفعل.');
            break;
          case '23503':
            setError('خطأ في العلاقات بين الجداول. تأكد من صحة البيانات المدخلة.');
            break;
          case '23502':
            setError('بعض الحقول الإلزامية فارغة. يرجى التأكد من إدخال جميع البيانات المطلوبة.');
            break;
          default:
            setError('حدث خطأ أثناء تسجيل الطالب. يرجى المحاولة مرة أخرى. (رمز الخطأ: ' + error.code + ')');
        }
      } else {
        setError('حدث خطأ غير معروف أثناء تسجيل الطالب. يرجى المحاولة مرة أخرى.');
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Layout>
      <div className="p-6">
        <div className="mb-6 bg-gradient-to-l from-[#21ADE7]/10 to-transparent p-4 rounded-lg border-r-4 border-[#21ADE7]">
          <div className="flex items-center">
            <div className="w-12 h-12 rounded-full bg-[#21ADE7] flex items-center justify-center text-white mr-4">
              <FaUserPlus size={24} />
            </div>
            <div>
              <h1 className="text-2xl font-bold" style={{ color: '#21ADE7' }}>تسجيل الطلاب</h1>
              <p className="text-gray-600 mt-1">
                قم بإدخال بيانات الطالب الجديد من خلال الخطوات التالية
              </p>
            </div>
          </div>
        </div>

        {error && (
          <div className="mb-6 p-4 bg-[#FD1361]/10 border-r-4 border-[#FD1361] rounded-md shadow-md">
            <div className="flex items-center">
              <div className="w-10 h-10 rounded-full bg-[#FD1361] flex items-center justify-center text-white mr-3">
                <FaExclamationTriangle size={20} />
              </div>
              <p className="text-[#FD1361] font-medium">{error}</p>
            </div>
          </div>
        )}

        {success && (
          <div className="mb-6 p-4 bg-[#0ABB87]/10 border-r-4 border-[#0ABB87] rounded-md shadow-md">
            <div className="flex items-center">
              <div className="w-10 h-10 rounded-full bg-[#0ABB87] flex items-center justify-center text-white mr-3">
                <FaCheckCircle size={20} />
              </div>
              <p className="text-[#0ABB87] font-medium">{success}</p>
            </div>
          </div>
        )}



        {isSubmitting ? (
          <div className="bg-white rounded-lg shadow-lg p-8 border-r-4 border-[#21ADE7] text-center">
            <div className="relative mx-auto mb-6">
              <div className="w-16 h-16 border-4 border-[#21ADE7]/30 border-t-[#21ADE7] rounded-full animate-spin"></div>
              <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                <FaUser size={20} className="text-[#21ADE7]" />
              </div>
            </div>
            <h3 className="text-xl font-bold text-[#21ADE7] mb-2">جاري معالجة البيانات</h3>
            <p className="text-gray-600">يرجى الانتظار حتى يتم حفظ بيانات الطالب...</p>
          </div>
        ) : (
          <StepWizard
            steps={steps}
            onComplete={handleComplete}
            initialData={{}}
          />
        )}
      </div>
    </Layout>
  );
}
