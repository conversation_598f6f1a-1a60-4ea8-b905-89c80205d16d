"use client";

import React, { useEffect } from 'react';
import { useFormik } from 'formik';
import * as Yup from 'yup';

interface Step7Props {
  onComplete: (data: any, isValid: boolean) => void;
  formData: Record<string, any>;
}

const Step7HealthInfo: React.FC<Step7Props> = ({ onComplete, formData }) => {
  // التحقق من صحة البيانات باستخدام Yup
  const validationSchema = Yup.object({
    generalHealth: Yup.string(),
    chronicDiseases: Yup.string(),
    drugAllergies: Yup.string(),
    doctorName: Yup.string(),
    doctorPhone: Yup.string(),
    insuranceCompany: Yup.string(),
    insurancePolicyNumber: Yup.string(),
  });

  // إعداد نموذج Formik
  const formik = useFormik({
    initialValues: {
      generalHealth: formData.generalHealth || '',
      chronicDiseases: formData.chronicDiseases || '',
      drugAllergies: formData.drugAllergies || '',
      doctorName: formData.doctorName || '',
      doctorPhone: formData.doctorPhone || '',
      insuranceCompany: formData.insuranceCompany || '',
      insurancePolicyNumber: formData.insurancePolicyNumber || '',
    },
    validationSchema,
    onSubmit: (values) => {
      console.log('Form submitted with values:', values);
    },
  });

  // تحديث البيانات عند تغييرها
  useEffect(() => {
    onComplete(formik.values, true); // هذه الخطوة اختيارية، لذلك دائمًا صالحة
  }, [formik.values, onComplete]);

  return (
    <div className="bg-white rounded-lg p-6">
      <h2 className="text-xl font-bold text-primary mb-6 border-r-4 border-primary pr-3">
        المعلومات الصحية
      </h2>
      
      <div className="bg-yellow-50 border-r-4 border-yellow-400 p-4 mb-6">
        <p className="text-yellow-700">
          هذه المعلومات اختيارية ولكنها مهمة في حالات الطوارئ. يرجى تقديم أكبر قدر ممكن من المعلومات الدقيقة.
        </p>
      </div>
      
      <form className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* الحالة الصحية العامة */}
          <div className="md:col-span-2">
            <label htmlFor="generalHealth" className="block text-gray-700 font-medium mb-2">
              الحالة الصحية العامة
            </label>
            <textarea
              id="generalHealth"
              name="generalHealth"
              rows={3}
              className="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              value={formik.values.generalHealth}
              placeholder="وصف الحالة الصحية العامة للطالب"
            ></textarea>
          </div>

          {/* الأمراض المزمنة */}
          <div>
            <label htmlFor="chronicDiseases" className="block text-gray-700 font-medium mb-2">
              الأمراض المزمنة
            </label>
            <textarea
              id="chronicDiseases"
              name="chronicDiseases"
              rows={3}
              className="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              value={formik.values.chronicDiseases}
              placeholder="أي أمراض مزمنة يعاني منها الطالب (مثل السكري، الربو، إلخ)"
            ></textarea>
          </div>

          {/* حساسية الأدوية */}
          <div>
            <label htmlFor="drugAllergies" className="block text-gray-700 font-medium mb-2">
              حساسية الأدوية
            </label>
            <textarea
              id="drugAllergies"
              name="drugAllergies"
              rows={3}
              className="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              value={formik.values.drugAllergies}
              placeholder="أي حساسية تجاه أدوية معينة"
            ></textarea>
          </div>

          {/* اسم الطبيب المعالج */}
          <div>
            <label htmlFor="doctorName" className="block text-gray-700 font-medium mb-2">
              اسم الطبيب المعالج
            </label>
            <input
              type="text"
              id="doctorName"
              name="doctorName"
              className="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              value={formik.values.doctorName}
              placeholder="اسم الطبيب المعالج"
            />
          </div>

          {/* رقم الطبيب */}
          <div>
            <label htmlFor="doctorPhone" className="block text-gray-700 font-medium mb-2">
              رقم هاتف الطبيب
            </label>
            <input
              type="tel"
              id="doctorPhone"
              name="doctorPhone"
              className="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              value={formik.values.doctorPhone}
              placeholder="رقم هاتف الطبيب المعالج"
            />
          </div>

          {/* شركة التأمين الصحي */}
          <div>
            <label htmlFor="insuranceCompany" className="block text-gray-700 font-medium mb-2">
              شركة التأمين الصحي
            </label>
            <input
              type="text"
              id="insuranceCompany"
              name="insuranceCompany"
              className="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              value={formik.values.insuranceCompany}
              placeholder="اسم شركة التأمين الصحي"
            />
          </div>

          {/* رقم بوليصة التأمين */}
          <div>
            <label htmlFor="insurancePolicyNumber" className="block text-gray-700 font-medium mb-2">
              رقم بوليصة التأمين
            </label>
            <input
              type="text"
              id="insurancePolicyNumber"
              name="insurancePolicyNumber"
              className="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              value={formik.values.insurancePolicyNumber}
              placeholder="رقم بوليصة التأمين الصحي"
            />
          </div>
        </div>
      </form>
      
      <div className="mt-6 p-4 bg-gray-50 rounded-lg border border-gray-200">
        <h3 className="text-lg font-semibold text-primary mb-2">ملاحظات هامة</h3>
        <ul className="list-disc list-inside space-y-1 text-gray-600">
          <li>جميع المعلومات الصحية سرية ولن يتم مشاركتها مع أي طرف ثالث.</li>
          <li>سيتم استخدام هذه المعلومات فقط في حالات الطوارئ أو عند الحاجة الطبية.</li>
          <li>يرجى إبلاغ المدرسة فورًا بأي تغييرات في الحالة الصحية للطالب.</li>
          <li>في حالة وجود حالة صحية خاصة، يرجى تقديم تقرير طبي مفصل.</li>
        </ul>
      </div>
    </div>
  );
};

export default Step7HealthInfo;
