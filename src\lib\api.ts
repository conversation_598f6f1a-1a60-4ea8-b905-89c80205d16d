import { supabase } from './supabase';
import { Student, AcademicRecord, Document, Grade, Attendance, Transfer, HealthRecord } from '../types/database.types';

// Funciones para estudiantes
export async function getStudents() {
  const { data, error } = await supabase
    .from('students')
    .select('*');
  
  if (error) {
    console.error('Error fetching students:', error);
    return [];
  }
  
  return data as Student[];
}

export async function getStudentById(id: string) {
  const { data, error } = await supabase
    .from('students')
    .select('*')
    .eq('student_id', id)
    .single();
  
  if (error) {
    console.error(`Error fetching student with ID ${id}:`, error);
    return null;
  }
  
  return data as Student;
}

// Funciones para registros académicos
export async function getAcademicRecordsByStudentId(studentId: string) {
  const { data, error } = await supabase
    .from('academic_records')
    .select('*')
    .eq('student_id', studentId);
  
  if (error) {
    console.error(`Error fetching academic records for student ${studentId}:`, error);
    return [];
  }
  
  return data as AcademicRecord[];
}

// Funciones para documentos
export async function getDocumentsByStudentId(studentId: string) {
  const { data, error } = await supabase
    .from('documents')
    .select('*')
    .eq('student_id', studentId);
  
  if (error) {
    console.error(`Error fetching documents for student ${studentId}:`, error);
    return [];
  }
  
  return data as Document[];
}

// استرجاع المراحل الدراسية
export async function getGrades() {
  try {
    const result = await db.query('SELECT * FROM grades ORDER BY id');
    return result.rows;
  } catch (error) {
    throw error;
  }
}

// Funciones para asistencia
export async function getAttendanceByStudentId(studentId: string) {
  const { data, error } = await supabase
    .from('attendance')
    .select('*')
    .eq('student_id', studentId);
  
  if (error) {
    console.error(`Error fetching attendance for student ${studentId}:`, error);
    return [];
  }
  
  return data as Attendance[];
}

// Funciones para transferencias
export async function getTransfersByStudentId(studentId: string) {
  const { data, error } = await supabase
    .from('transfers')
    .select('*')
    .eq('student_id', studentId);
  
  if (error) {
    console.error(`Error fetching transfers for student ${studentId}:`, error);
    return [];
  }
  
  return data as Transfer[];
}

// Funciones para registros de salud
export async function getHealthRecordByStudentId(studentId: string) {
  const { data, error } = await supabase
    .from('health_records')
    .select('*')
    .eq('student_id', studentId)
    .single();
  
  if (error) {
    console.error(`Error fetching health record for student ${studentId}:`, error);
    return null;
  }
  
  return data as HealthRecord;
}
