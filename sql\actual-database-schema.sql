-- ملف SQL لإنشاء جداول قاعدة البيانات في Supabase بناءً على الهيكل الحالي

-- إن<PERSON>اء جدول المراحل الدراسية
CREATE TABLE IF NOT EXISTS school_grades (
  id SERIAL PRIMARY KEY,
  name TEXT NOT NULL,
  level TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- إنشاء جدول الفصول الدراسية
CREATE TABLE IF NOT EXISTS school_classes (
  id SERIAL PRIMARY KEY,
  name TEXT NOT NULL,
  grade_id INTEGER REFERENCES school_grades(id),
  capacity INTEGER,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- إن<PERSON>اء جدول الطلاب
CREATE TABLE IF NOT EXISTS students (
  id SERIAL PRIMARY KEY,
  full_name TEXT NOT NULL,
  id_number TEXT NOT NULL,
  birth_date DATE NOT NULL,
  gender TEXT NOT NULL,
  religion TEXT,
  marital_status TEXT,
  email TEXT,
  phone TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- إنشاء جدول أولياء الأمور
CREATE TABLE IF NOT EXISTS guardians (
  id SERIAL PRIMARY KEY,
  student_id INTEGER REFERENCES students(id),
  full_name TEXT NOT NULL,
  relationship TEXT NOT NULL,
  id_number TEXT NOT NULL,
  phone TEXT NOT NULL,
  email TEXT,
  occupation TEXT,
  workplace TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- إنشاء جدول المعلومات المالية
CREATE TABLE IF NOT EXISTS financial_info (
  id SERIAL PRIMARY KEY,
  student_id INTEGER REFERENCES students(id),
  tuition_fee NUMERIC NOT NULL,
  discount_amount NUMERIC,
  discount_reason TEXT,
  paid_amount NUMERIC NOT NULL,
  payment_method TEXT NOT NULL,
  installments_count INTEGER,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- إنشاء جدول الأقساط
CREATE TABLE IF NOT EXISTS installments (
  id SERIAL PRIMARY KEY,
  financial_info_id INTEGER REFERENCES financial_info(id),
  due_date DATE NOT NULL,
  amount NUMERIC NOT NULL,
  paid NUMERIC,
  discount NUMERIC,
  remaining NUMERIC,
  status TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  payment_date DATE
);

-- إنشاء جدول المدفوعات الأخرى
CREATE TABLE IF NOT EXISTS other_payments (
  id SERIAL PRIMARY KEY,
  student_id INTEGER REFERENCES students(id),
  description TEXT NOT NULL,
  amount NUMERIC NOT NULL,
  payment_date DATE,
  is_paid BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- إنشاء جدول المعلومات الصحية
CREATE TABLE IF NOT EXISTS health_info (
  id SERIAL PRIMARY KEY,
  student_id INTEGER REFERENCES students(id),
  blood_type TEXT,
  has_allergies BOOLEAN DEFAULT FALSE,
  allergies_details TEXT,
  has_chronic_diseases BOOLEAN DEFAULT FALSE,
  chronic_diseases_details TEXT,
  emergency_contact_name TEXT,
  emergency_contact_phone TEXT,
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- إنشاء جدول المعلومات الإضافية
CREATE TABLE IF NOT EXISTS additional_info (
  id SERIAL PRIMARY KEY,
  student_id INTEGER REFERENCES students(id),
  previous_school TEXT,
  transfer_reason TEXT,
  hobbies TEXT,
  talents TEXT,
  behavioral_notes TEXT,
  academic_notes TEXT,
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- إنشاء جدول المرفقات
CREATE TABLE IF NOT EXISTS attachments (
  id SERIAL PRIMARY KEY,
  student_id INTEGER REFERENCES students(id),
  photo_url TEXT,
  birth_certificate_url TEXT,
  id_card_url TEXT,
  previous_school_certificate_url TEXT,
  other_documents_urls JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- إنشاء جدول السجلات الأكاديمية
CREATE TABLE IF NOT EXISTS academic_records (
  id SERIAL PRIMARY KEY,
  student_id INTEGER REFERENCES students(id),
  grade_id INTEGER REFERENCES school_grades(id),
  class_id INTEGER REFERENCES school_classes(id),
  academic_year TEXT,
  enrollment_date DATE,
  status TEXT,
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- إنشاء الفهارس لتحسين الأداء
-- فهارس جدول الطلاب
CREATE INDEX IF NOT EXISTS idx_students_full_name ON students (full_name);
CREATE INDEX IF NOT EXISTS idx_students_id_number ON students (id_number);

-- فهارس جدول أولياء الأمور
CREATE INDEX IF NOT EXISTS idx_guardians_student_id ON guardians (student_id);
CREATE INDEX IF NOT EXISTS idx_guardians_full_name ON guardians (full_name);
CREATE INDEX IF NOT EXISTS idx_guardians_phone ON guardians (phone);

-- فهارس جدول المعلومات المالية
CREATE INDEX IF NOT EXISTS idx_financial_info_student_id ON financial_info (student_id);
CREATE INDEX IF NOT EXISTS idx_financial_info_payment_method ON financial_info (payment_method);

-- فهارس جدول الأقساط
CREATE INDEX IF NOT EXISTS idx_installments_financial_info_id ON installments (financial_info_id);
CREATE INDEX IF NOT EXISTS idx_installments_due_date ON installments (due_date);
CREATE INDEX IF NOT EXISTS idx_installments_status ON installments (status);

-- فهارس جدول المدفوعات الأخرى
CREATE INDEX IF NOT EXISTS idx_other_payments_student_id ON other_payments (student_id);

-- إنشاء دالة لتحديث حقل updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- إنشاء مشغلات لتحديث حقل updated_at تلقائيًا
-- مشغل لجدول الطلاب
CREATE TRIGGER update_students_updated_at
BEFORE UPDATE ON students
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- مشغل لجدول أولياء الأمور
CREATE TRIGGER update_guardians_updated_at
BEFORE UPDATE ON guardians
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- إنشاء دالة لتحديث حالة الأقساط تلقائيًا
CREATE OR REPLACE FUNCTION update_installment_status()
RETURNS TRIGGER AS $$
BEGIN
    -- تحديث حالة القسط بناءً على المبلغ المدفوع والخصم
    IF NEW.paid + COALESCE(NEW.discount, 0) >= NEW.amount THEN
        NEW.status = 'paid';
        NEW.remaining = 0;
    ELSIF NEW.paid > 0 THEN
        NEW.status = 'partially_paid';
        NEW.remaining = NEW.amount - NEW.paid - COALESCE(NEW.discount, 0);
    ELSIF NEW.due_date < CURRENT_DATE THEN
        NEW.status = 'overdue';
        NEW.remaining = NEW.amount - COALESCE(NEW.paid, 0) - COALESCE(NEW.discount, 0);
    ELSE
        NEW.status = 'pending';
        NEW.remaining = NEW.amount - COALESCE(NEW.paid, 0) - COALESCE(NEW.discount, 0);
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- إنشاء مشغل لتحديث حالة الأقساط تلقائيًا
CREATE TRIGGER update_installment_status_trigger
BEFORE INSERT OR UPDATE ON installments
FOR EACH ROW
EXECUTE FUNCTION update_installment_status();

-- إنشاء دالة لتحديث إجمالي المبلغ المدفوع في المعلومات المالية
CREATE OR REPLACE FUNCTION update_financial_info_paid_amount()
RETURNS TRIGGER AS $$
DECLARE
    total_paid NUMERIC;
BEGIN
    -- حساب إجمالي المبلغ المدفوع من الأقساط
    SELECT COALESCE(SUM(paid), 0) INTO total_paid
    FROM installments
    WHERE financial_info_id = NEW.financial_info_id;
    
    -- تحديث المبلغ المدفوع في المعلومات المالية
    UPDATE financial_info
    SET paid_amount = total_paid
    WHERE id = NEW.financial_info_id;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- إنشاء مشغل لتحديث إجمالي المبلغ المدفوع في المعلومات المالية
CREATE TRIGGER update_financial_info_paid_amount_trigger
AFTER INSERT OR UPDATE ON installments
FOR EACH ROW
EXECUTE FUNCTION update_financial_info_paid_amount();
