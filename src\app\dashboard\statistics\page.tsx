"use client";

import React, { useEffect, useState } from 'react';
import { supabase } from '@/lib/supabase';
import Layout from '@/components/Layout';
import { <PERSON><PERSON><PERSON>, Pie, Cell, ResponsiveContainer, Legend, Tooltip } from 'recharts';

interface ReligionStats {
  name: string;
  value: number;
  color: string;
}

interface GradeStats {
  grade: string;
  islamCount: number;
  christianityCount: number;
  totalCount: number;
}

const StatisticsPage = () => {
  const [loading, setLoading] = useState(true);
  const [religionStats, setReligionStats] = useState<ReligionStats[]>([]);
  const [gradeReligionStats, setGradeReligionStats] = useState<GradeStats[]>([]);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchStatistics = async () => {
      try {
        setLoading(true);
        
        // الحصول على إحصائيات الديانة للطلاب
        const { data: religionData, error: religionError } = await supabase
          .from('students')
          .select('religion')
          .not('religion', 'is', null);
          
        if (religionError) throw religionError;
        
        // حساب عدد الطلاب لكل ديانة
        const religionCounts: Record<string, number> = {};
        religionData?.forEach(student => {
          const religion = student.religion || 'غير محدد';
          religionCounts[religion] = (religionCounts[religion] || 0) + 1;
        });
        
        // تحويل البيانات إلى تنسيق مناسب للرسم البياني
        const religionStatsData: ReligionStats[] = [
          { 
            name: 'الإسلام', 
            value: religionCounts['islam'] || 0, 
            color: '#0ABB87' // لون أخضر
          },
          { 
            name: 'المسيحية', 
            value: religionCounts['christianity'] || 0, 
            color: '#5578EB' // لون أزرق
          },
          { 
            name: 'غير محدد', 
            value: religionCounts['غير محدد'] || 0, 
            color: '#384AD7' // لون أزرق داكن
          }
        ];
        
        setReligionStats(religionStatsData);
        
        // الحصول على إحصائيات الديانة حسب المرحلة الدراسية
        const { data: studentsData, error: studentsError } = await supabase
          .from('students')
          .select(`
            id,
            religion,
            student_grades (
              grade_id,
              grades (
                id,
                name
              )
            )
          `);
          
        if (studentsError) throw studentsError;
        
        // تجميع البيانات حسب المرحلة الدراسية
        const gradeStats: Record<string, GradeStats> = {};
        
        studentsData?.forEach(student => {
          if (student.student_grades && student.student_grades.length > 0) {
            const gradeName = student.student_grades[0]?.grades?.name || 'غير محدد';
            
            if (!gradeStats[gradeName]) {
              gradeStats[gradeName] = {
                grade: gradeName,
                islamCount: 0,
                christianityCount: 0,
                totalCount: 0
              };
            }
            
            gradeStats[gradeName].totalCount++;
            
            if (student.religion === 'islam') {
              gradeStats[gradeName].islamCount++;
            } else if (student.religion === 'christianity') {
              gradeStats[gradeName].christianityCount++;
            }
          }
        });
        
        setGradeReligionStats(Object.values(gradeStats));
        
      } catch (err: any) {
        console.error('Error fetching statistics:', err);
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchStatistics();
  }, []);

  const renderCustomizedLabel = ({ cx, cy, midAngle, innerRadius, outerRadius, percent, index, name, value }: any) => {
    const RADIAN = Math.PI / 180;
    const radius = 25 + innerRadius + (outerRadius - innerRadius);
    const x = cx + radius * Math.cos(-midAngle * RADIAN);
    const y = cy + radius * Math.sin(-midAngle * RADIAN);

    return (
      <text x={x} y={y} fill={religionStats[index].color} textAnchor={x > cx ? 'start' : 'end'} dominantBaseline="central">
        {`${name}: ${value} (${(percent * 100).toFixed(0)}%)`}
      </text>
    );
  };

  return (
    <Layout>
      <div className="container mx-auto px-4 py-8">
        <h1 className="text-2xl font-bold text-primary mb-8 border-r-4 border-primary pr-3">
          إحصائيات الطلاب
        </h1>

        {loading ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
          </div>
        ) : error ? (
          <div className="bg-danger/10 text-danger p-4 rounded-md">
            <p>حدث خطأ أثناء تحميل البيانات: {error}</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* إحصائيات الديانة */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <h2 className="text-xl font-bold text-primary mb-4">توزيع الطلاب حسب الديانة</h2>
              <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={religionStats}
                      cx="50%"
                      cy="50%"
                      labelLine={true}
                      label={renderCustomizedLabel}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                    >
                      {religionStats.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip formatter={(value) => [`${value} طالب`, 'العدد']} />
                    <Legend />
                  </PieChart>
                </ResponsiveContainer>
              </div>
              <div className="mt-4">
                <p className="text-lg font-bold">إجمالي عدد الطلاب: {religionStats.reduce((sum, item) => sum + item.value, 0)}</p>
              </div>
            </div>

            {/* إحصائيات الديانة حسب المرحلة الدراسية */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <h2 className="text-xl font-bold text-primary mb-4">توزيع الطلاب حسب الديانة والمرحلة الدراسية</h2>
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        المرحلة الدراسية
                      </th>
                      <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        المسلمون
                      </th>
                      <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        المسيحيون
                      </th>
                      <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        الإجمالي
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {gradeReligionStats.map((stat) => (
                      <tr key={stat.grade}>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                          {stat.grade}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {stat.islamCount} ({((stat.islamCount / stat.totalCount) * 100).toFixed(1)}%)
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {stat.christianityCount} ({((stat.christianityCount / stat.totalCount) * 100).toFixed(1)}%)
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {stat.totalCount}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        )}
      </div>
    </Layout>
  );
};

export default StatisticsPage;
