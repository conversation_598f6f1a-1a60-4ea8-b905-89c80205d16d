# دليل تطوير صفحة تسجيل الطلاب

## نظرة عامة
صفحة تسجيل الطلاب هي صفحة متعددة الخطوات تتيح إدخال بيانات الطالب بشكل منظم ومقسم. تتكون من 9 خطوات مع إمكانية التنقل بينها والتحقق من البيانات المدخلة.

## المكتبات المطلوبة
- **React**: لبناء واجهة المستخدم
- **Formik**: لإدارة النماذج والتحقق من البيانات
- **Yup**: للتحقق من صحة البيانات
- **react-step-builder**: لإدارة الخطوات المتعددة
- **Tailwind CSS**: للتصميم والتنسيق
- **react-to-print**: لطباعة النموذج
- **jspdf**: لتصدير البيانات إلى PDF
- **Supabase**: لتخزين البيانات

## هيكل الجداول في قاعدة البيانات

### 1. جدول الطلاب (students)
```sql
CREATE TABLE students (
  id SERIAL PRIMARY KEY,
  full_name TEXT NOT NULL,
  id_number TEXT NOT NULL UNIQUE,
  birth_date DATE NOT NULL,
  gender TEXT NOT NULL,
  religion TEXT,
  marital_status TEXT,
  email TEXT,
  phone TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

### 2. جدول أولياء الأمور (guardians)
```sql
CREATE TABLE guardians (
  id SERIAL PRIMARY KEY,
  student_id INTEGER REFERENCES students(id) ON DELETE CASCADE,
  full_name TEXT NOT NULL,
  relationship TEXT NOT NULL,
  id_number TEXT NOT NULL,
  phone TEXT NOT NULL,
  email TEXT,
  occupation TEXT,
  workplace TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

### 3. جدول المعلومات المالية (financial_info)
```sql
CREATE TABLE financial_info (
  id SERIAL PRIMARY KEY,
  student_id INTEGER REFERENCES students(id) ON DELETE CASCADE,
  tuition_fee DECIMAL(10, 2) NOT NULL,
  discount_amount DECIMAL(10, 2) DEFAULT 0,
  discount_reason TEXT,
  paid_amount DECIMAL(10, 2) NOT NULL,
  payment_method TEXT NOT NULL,
  installments_count INTEGER,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

### 4. جدول الأقساط (installments)
```sql
CREATE TABLE installments (
  id SERIAL PRIMARY KEY,
  financial_info_id INTEGER REFERENCES financial_info(id) ON DELETE CASCADE,
  due_date DATE NOT NULL,
  amount DECIMAL(10, 2) NOT NULL,
  paid DECIMAL(10, 2) DEFAULT 0,
  discount DECIMAL(10, 2) DEFAULT 0,
  remaining DECIMAL(10, 2) GENERATED ALWAYS AS (amount - paid - discount) STORED,
  status TEXT DEFAULT 'pending',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

### 5. جدول المدفوعات الأخرى (other_payments)
```sql
CREATE TABLE other_payments (
  id SERIAL PRIMARY KEY,
  student_id INTEGER REFERENCES students(id) ON DELETE CASCADE,
  file_opening_fee DECIMAL(10, 2) NOT NULL,
  books_fee DECIMAL(10, 2) NOT NULL,
  uniform_count INTEGER NOT NULL,
  uniform_total DECIMAL(10, 2) NOT NULL,
  transportation_fee DECIMAL(10, 2) NOT NULL,
  total_amount DECIMAL(10, 2) NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

### 6. جدول المعلومات الصحية (health_info)
```sql
CREATE TABLE health_info (
  id SERIAL PRIMARY KEY,
  student_id INTEGER REFERENCES students(id) ON DELETE CASCADE,
  general_health TEXT,
  chronic_diseases TEXT,
  drug_allergies TEXT,
  doctor_name TEXT,
  doctor_phone TEXT,
  insurance_company TEXT,
  insurance_policy_number TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

### 7. جدول المعلومات الإضافية (additional_info)
```sql
CREATE TABLE additional_info (
  id SERIAL PRIMARY KEY,
  student_id INTEGER REFERENCES students(id) ON DELETE CASCADE,
  hobbies TEXT,
  achievements TEXT,
  special_needs TEXT,
  transportation_method TEXT,
  additional_notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

### 8. جدول المرفقات (attachments)
```sql
CREATE TABLE attachments (
  id SERIAL PRIMARY KEY,
  student_id INTEGER REFERENCES students(id) ON DELETE CASCADE,
  photo_url TEXT,
  birth_certificate_url TEXT,
  id_card_url TEXT,
  academic_record_url TEXT,
  health_documents_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

### 9. جدول السجل الأكاديمي (academic_records)
```sql
CREATE TABLE academic_records (
  id SERIAL PRIMARY KEY,
  student_id INTEGER REFERENCES students(id) ON DELETE CASCADE,
  grade_id INTEGER REFERENCES grades(id),
  class_id INTEGER REFERENCES classes(id),
  academic_year TEXT NOT NULL,
  registration_date DATE NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

## تفاصيل الخطوات

### الخطوة 1: بيانات الطالب الأساسية
- **الحقول الإلزامية**: الاسم الكامل، رقم الهوية، تاريخ الميلاد، الجنس
- **الحقول الاختيارية**: الديانة، الحالة الاجتماعية، البريد الإلكتروني، رقم الهاتف
- **التحقق**: التأكد من إدخال جميع الحقول الإلزامية وصحة تنسيقها

### الخطوة 2: بيانات ولي الأمر
- **الحقول الإلزامية**: اسم ولي الأمر، العلاقة بالطالب، رقم الهوية، رقم الهاتف
- **الحقول الاختيارية**: البريد الإلكتروني، المهنة، مكان العمل
- **ميزة إضافية**: إمكانية إضافة أكثر من ولي أمر

### الخطوة 3: القسم المالي
- **الحقول الإلزامية**: قيمة الرسوم، القيمة المقدمة، طريقة الدفع
- **الحقول الاختيارية**: قيمة الخصم، سبب الخصم
- **الحقول الشرطية**: عدد الأقساط (إلزامي إذا تم اختيار التقسيط)
- **العمليات الحسابية**: حساب إجمالي الرسوم بعد الخصم وقيمة القسط

### الخطوة 4: جدول الأقساط
- **عرض ديناميكي**: بناءً على عدد الأقساط المحدد في الخطوة السابقة
- **الحقول لكل قسط**: تاريخ الاستحقاق، المبلغ المستحق، المدفوع، الخصم، المتبقي
- **التصميم**: جدول ملون مع رأس أزرق وصفوف متناوبة

### الخطوة 5: المدفوعات الأخرى
- **الحقول الثابتة**: رسوم فتح الملف، رسوم الكتب، الزي المدرسي، النقل المدرسي
- **العمليات الحسابية**: حساب إجمالي الزي المدرسي وإجمالي المدفوعات الأخرى

### الخطوة 6: الإجماليات
- **عرض تلقائي**: إجمالي الرسوم بعد الخصم، إجمالي المدفوعات الأخرى، السجل المالي الكلي

### الخطوة 7: معلومات صحية
- **الحقول الاختيارية**: الحالة الصحية، الأمراض المزمنة، حساسية الأدوية، معلومات الطبيب، معلومات التأمين

### الخطوة 8: معلومات إضافية
- **الحقول الاختيارية**: الهوايات، الإنجازات، الاحتياجات الخاصة، طريقة الوصول، ملاحظات

### الخطوة 9: المرفقات
- **الحقول الاختيارية**: صورة شخصية، شهادة الميلاد، بطاقة الهوية، السجل الأكاديمي، مستندات صحية

## تصميم الجداول
- استخدام Tailwind CSS لتصميم جداول ملونة ومنسقة
- رأس الجدول بلون أزرق (primary) ونص أبيض
- صفوف متناوبة بألوان فاتحة للقراءة السهلة
- أزرار الإجراءات بألوان مميزة (تعديل: أزرق، حذف: أحمر، عرض: أخضر)

## وظائف CRUD
- **إنشاء**: حفظ بيانات الطالب الجديد في قاعدة البيانات
- **قراءة**: عرض بيانات الطالب المسجلة مسبقًا
- **تحديث**: تعديل بيانات الطالب الموجودة
- **حذف**: إزالة سجل الطالب من قاعدة البيانات

## ميزات إضافية
- **الطباعة**: إمكانية طباعة النموذج الكامل
- **التصدير**: تصدير البيانات بتنسيق PDF أو CSV
- **الحفظ المؤقت**: حفظ البيانات مؤقتًا أثناء الانتقال بين الخطوات
- **التحقق من الصحة**: التحقق من صحة البيانات قبل الانتقال للخطوة التالية

## ملاحظات هامة
- التأكد من تنفيذ جميع العمليات الحسابية بدقة
- التحقق من صحة البيانات في كل خطوة قبل الانتقال
- تأمين البيانات في Supabase باستخدام سياسات الأمان
- جعل الواجهة سهلة الاستخدام ومتجاوبة مع جميع أحجام الشاشات
