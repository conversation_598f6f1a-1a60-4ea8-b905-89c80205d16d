"use client";

import React, { useState, ReactNode, useRef, useEffect } from 'react';
import {
  FaUser, FaUser<PERSON>riends, FaMoneyBillWave, FaCalendarAlt,
  FaMoneyCheckAlt, FaFileInvoiceDollar, FaHeartbeat,
  FaInfoCircle, FaPaperclip, FaSave, FaExclamationTriangle
} from 'react-icons/fa';


interface Step {
  id: number;
  title: string;
  component: ReactNode;
  icon?: ReactNode; // أيقونة اختيارية
  color?: string; // لون اختياري
}

// واجهة للمكون الذي يحتوي على دالة حفظ
interface StepComponentWithSave {
  saveData?: () => Promise<boolean>;
  hasUnsavedChanges?: () => boolean;
}

interface StepViewerProps {
  steps: Step[];
  studentId: string | number;
}

// دالة للحصول على الأيقونة الافتراضية لكل خطوة
const getDefaultStepIcon = (stepId: number): ReactNode => {
  switch (stepId) {
    case 1: return <FaUser size={20} />;
    case 2: return <FaUserFriends size={20} />;
    case 3: return <FaMoneyBillWave size={20} />;
    case 4: return <FaCalendarAlt size={20} />;
    case 5: return <FaMoneyCheckAlt size={20} />;
    case 6: return <FaFileInvoiceDollar size={20} />;
    case 7: return <FaHeartbeat size={20} />;
    case 8: return <FaInfoCircle size={20} />;
    case 9: return <FaPaperclip size={20} />;
    default: return <FaInfoCircle size={20} />;
  }
};

// دالة للحصول على اللون الافتراضي لكل خطوة
const getDefaultStepColor = (stepId: number): string => {
  switch (stepId) {
    case 1: return '#21ADE7'; // أزرق فاتح
    case 2: return '#5578EB'; // أزرق داكن
    case 3: return '#FD1361'; // أحمر
    case 4: return '#0ABB87'; // أخضر
    case 5: return '#384AD7'; // أزرق غامق
    case 6: return '#21ADE7'; // أزرق فاتح
    case 7: return '#FD1361'; // أحمر
    case 8: return '#5578EB'; // أزرق داكن
    case 9: return '#0ABB87'; // أخضر
    default: return '#21ADE7'; // أزرق فاتح
  }
};

const StepViewer: React.FC<StepViewerProps> = ({ steps, studentId }) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [isSaving, setIsSaving] = useState(false);
  const [saveMessage, setSaveMessage] = useState<{ text: string; type: 'success' | 'error' | 'info' } | null>(null);
  const currentComponentRef = useRef<StepComponentWithSave | null>(null);

  // تنظيف رسالة الحفظ بعد 3 ثواني
  useEffect(() => {
    if (saveMessage) {
      const timer = setTimeout(() => {
        setSaveMessage(null);
      }, 3000);

      return () => clearTimeout(timer);
    }
  }, [saveMessage]);

  // حفظ البيانات الحالية قبل الانتقال
  const saveCurrentStep = async (): Promise<boolean> => {
    if (!currentComponentRef.current || !currentComponentRef.current.saveData) {
      return true; // إذا لم يكن هناك دالة حفظ، نعتبر أن الحفظ تم بنجاح
    }

    try {
      setIsSaving(true);
      const success = await currentComponentRef.current.saveData();

      if (success) {
        setSaveMessage({ text: 'تم حفظ التغييرات بنجاح', type: 'success' });
      } else {
        setSaveMessage({ text: 'فشل في حفظ التغييرات', type: 'error' });
        return false;
      }

      return success;
    } catch (error) {
      console.error('Error saving data:', error);
      setSaveMessage({ text: 'حدث خطأ أثناء حفظ البيانات', type: 'error' });
      return false;
    } finally {
      setIsSaving(false);
    }
  };

  // التحقق من وجود تغييرات غير محفوظة
  const hasUnsavedChanges = (): boolean => {
    return !!(currentComponentRef.current &&
              currentComponentRef.current.hasUnsavedChanges &&
              currentComponentRef.current.hasUnsavedChanges());
  };

  // الانتقال إلى الخطوة التالية مع حفظ البيانات الحالية
  const handleNext = async () => {
    if (currentStep < steps.length - 1) {
      if (hasUnsavedChanges()) {
        const confirmed = window.confirm('هناك تغييرات غير محفوظة. هل تريد حفظها قبل الانتقال؟');
        if (confirmed) {
          const saved = await saveCurrentStep();
          if (!saved) return; // إذا فشل الحفظ، لا ننتقل
        }
      }
      setCurrentStep(currentStep + 1);
    }
  };

  // الانتقال إلى الخطوة السابقة مع حفظ البيانات الحالية
  const handlePrevious = async () => {
    if (currentStep > 0) {
      if (hasUnsavedChanges()) {
        const confirmed = window.confirm('هناك تغييرات غير محفوظة. هل تريد حفظها قبل الانتقال؟');
        if (confirmed) {
          const saved = await saveCurrentStep();
          if (!saved) return; // إذا فشل الحفظ، لا ننتقل
        }
      }
      setCurrentStep(currentStep - 1);
    }
  };

  // النقر على خطوة محددة مع حفظ البيانات الحالية
  const handleStepClick = async (index: number) => {
    if (index === currentStep) return; // لا داعي للتنفيذ إذا كانت نفس الخطوة الحالية

    if (hasUnsavedChanges()) {
      const confirmed = window.confirm('هناك تغييرات غير محفوظة. هل تريد حفظها قبل الانتقال؟');
      if (confirmed) {
        const saved = await saveCurrentStep();
        if (!saved) return; // إذا فشل الحفظ، لا ننتقل
      }
    }

    setCurrentStep(index);
  };

  const renderStepIndicators = () => {
    return (
      <div className="flex justify-between items-center mb-8 px-4 overflow-x-auto py-2">
        {steps.map((step, index) => {
          // الحصول على الأيقونة واللون لكل خطوة
          const stepIcon = step.icon || getDefaultStepIcon(step.id);
          const stepColor = step.color || getDefaultStepColor(step.id);

          // تحديد لون الخلفية بناءً على حالة الخطوة
          const bgColor = index === currentStep
            ? stepColor // الخطوة الحالية
            : 'rgba(209, 213, 219, 0.8)'; // خطوة غير مكتملة

          return (
            <div
              key={step.id}
              className="flex flex-col items-center cursor-pointer mx-1"
              onClick={() => handleStepClick(index)}
            >
              <div
                className="w-12 h-12 rounded-full flex items-center justify-center text-white font-bold shadow-md hover:shadow-lg hover:opacity-90 transition-all duration-200 clickable"
                style={{ backgroundColor: bgColor }}
              >
                {stepIcon}
              </div>
              <div className="flex flex-col items-center">
                <span
                  className={`text-xs mt-2 text-center max-w-[80px] font-medium
                    ${index === currentStep ? 'font-bold' : ''}`}
                  style={{ color: index === currentStep ? stepColor : '#6B7280' }}
                >
                  {step.title}
                </span>
              </div>
            </div>
          );
        })}
      </div>
    );
  };

  const renderProgressBar = () => {
    const progress = ((currentStep + 1) / steps.length) * 100;
    const currentStepColor = steps[currentStep]?.color || getDefaultStepColor(steps[currentStep]?.id);

    return (
      <div className="w-full bg-gray-200 rounded-full h-3 mb-6 shadow-inner">
        <div
          className="h-3 rounded-full transition-all duration-300 ease-in-out"
          style={{
            width: `${progress}%`,
            background: `linear-gradient(to right, #0ABB87, ${currentStepColor})`
          }}
        ></div>
      </div>
    );
  };

  // استخدام نوع أكثر تحديدًا للمكون وتخزين المرجع
  const currentStepComponent = React.cloneElement(
    steps[currentStep].component as React.ReactElement<{
      studentId: string | number;
      ref: React.RefObject<StepComponentWithSave>;
    }>,
    {
      studentId,
      ref: currentComponentRef
    }
  );

  // عرض رسالة الحفظ
  const renderSaveMessage = () => {
    if (!saveMessage) return null;

    const bgColor = saveMessage.type === 'success'
      ? 'bg-[#0ABB87]/10 border-[#0ABB87]'
      : saveMessage.type === 'error'
        ? 'bg-[#FD1361]/10 border-[#FD1361]'
        : 'bg-[#5578EB]/10 border-[#5578EB]';

    const textColor = saveMessage.type === 'success'
      ? 'text-[#0ABB87]'
      : saveMessage.type === 'error'
        ? 'text-[#FD1361]'
        : 'text-[#5578EB]';

    const icon = saveMessage.type === 'success'
      ? <FaSave size={16} className="ml-2" />
      : saveMessage.type === 'error'
        ? <FaExclamationTriangle size={16} className="ml-2" />
        : null;

    return (
      <div className={`mb-4 p-3 ${bgColor} border-r-4 rounded-md flex items-center`}>
        {icon}
        <p className={textColor}>{saveMessage.text}</p>
      </div>
    );
  };

  return (
    <div className="bg-white rounded-lg shadow-lg p-6 border-r-4"
      style={{ borderColor: steps[currentStep]?.color || getDefaultStepColor(steps[currentStep]?.id) }}>
      {renderProgressBar()}
      {renderStepIndicators()}

      {/* رسالة الحفظ */}
      {renderSaveMessage()}

      <div className="mb-6 p-4 rounded-lg"
        style={{ backgroundColor: `${steps[currentStep]?.color || getDefaultStepColor(steps[currentStep]?.id)}10` }}>
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center">
            <div className="w-10 h-10 rounded-full flex items-center justify-center text-white mr-3"
              style={{ backgroundColor: steps[currentStep]?.color || getDefaultStepColor(steps[currentStep]?.id) }}>
              {steps[currentStep]?.icon || getDefaultStepIcon(steps[currentStep]?.id)}
            </div>
            <h2 className="text-xl font-bold"
              style={{ color: steps[currentStep]?.color || getDefaultStepColor(steps[currentStep]?.id) }}>
              {steps[currentStep]?.title}
            </h2>
          </div>

          {/* زر الحفظ */}
          <button
            onClick={saveCurrentStep}
            disabled={isSaving || !hasUnsavedChanges()}
            className={`${
              isSaving || !hasUnsavedChanges() ? 'bg-gray-300 cursor-not-allowed' : 'bg-[#0ABB87] hover:bg-[#0ABB87]/90 cursor-pointer'
            } text-white px-3 py-1 rounded-md flex items-center transition-colors duration-200`}
          >
            {isSaving ? (
              <>
                <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin ml-2"></div>
                جاري الحفظ...
              </>
            ) : (
              <>
                <FaSave className="ml-2" />
                حفظ التغييرات
              </>
            )}
          </button>
        </div>
        {currentStepComponent}
      </div>
      <div className="flex justify-between mt-8">
        <button
          onClick={handlePrevious}
          disabled={currentStep === 0}
          className={`px-4 py-2 rounded-md shadow-sm ${
            currentStep === 0
              ? 'bg-gray-300 cursor-not-allowed'
              : 'hover:opacity-90 text-white font-medium transition-all duration-200 clickable'
          }`}
          style={{
            backgroundColor: currentStep === 0
              ? '#D1D5DB'
              : `${steps[Math.max(0, currentStep-1)]?.color || getDefaultStepColor(steps[Math.max(0, currentStep-1)]?.id)}`
          }}
        >
          السابق
        </button>

        <button
          onClick={handleNext}
          disabled={currentStep === steps.length - 1}
          className={`px-4 py-2 rounded-md shadow-sm ${
            currentStep === steps.length - 1
              ? 'bg-gray-300 cursor-not-allowed'
              : 'hover:opacity-90 text-white font-medium transition-all duration-200 clickable'
          }`}
          style={{
            backgroundColor: currentStep === steps.length - 1
              ? '#D1D5DB'
              : `${steps[Math.min(steps.length-1, currentStep+1)]?.color || getDefaultStepColor(steps[Math.min(steps.length-1, currentStep+1)]?.id)}`
          }}
        >
          التالي
        </button>
      </div>
    </div>
  );
};

export default StepViewer;
