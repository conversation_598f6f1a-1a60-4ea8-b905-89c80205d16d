import React, { ReactNode } from 'react';
import Sidebar from './Sidebar';

interface LayoutProps {
  children: ReactNode;
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  return (
    <div className="flex h-screen bg-[#f0f9ff] overflow-hidden" dir="rtl">
      <Sidebar />
      <main className="flex-1 overflow-auto p-6 border-r-4 border-[#21ADE7]">
        {children}
      </main>
    </div>
  );
};

export default Layout;
