import { supabase } from './supabase';

// واجهة بيانات الطالب
export interface StudentData {
  full_name: string;
  id_number: string;
  birth_date: string;
  gender: string;
  religion?: string;
  marital_status?: string;
  email?: string;
  phone?: string;
}

// واجهة بيانات ولي الأمر
export interface GuardianData {
  full_name: string;
  relationship: string;
  id_number: string;
  phone: string;
  email?: string;
  occupation?: string;
  workplace?: string;
}

// واجهة البيانات المالية
export interface FinancialData {
  tuition_fee: number;
  discount_amount?: number;
  discount_reason?: string;
  paid_amount: number;
  payment_method: string;
  installments_count?: number;
}

// واجهة بيانات الأقساط
export interface InstallmentData {
  due_date: string;
  amount: number;
}

// واجهة المعلومات الصحية
export interface HealthData {
  blood_type?: string;
  has_allergies?: boolean;
  allergies_details?: string;
  has_chronic_diseases?: boolean;
  chronic_diseases_details?: string;
  emergency_contact_name?: string;
  emergency_contact_phone?: string;
  notes?: string;
}

// واجهة المعلومات الإضافية
export interface AdditionalData {
  hobbies?: string;
  achievements?: string;
  special_needs?: string;
  transportation_method?: string;
  additional_notes?: string;
}

// واجهة المرفقات
export interface AttachmentData {
  photo_url?: string;
  birth_certificate_url?: string;
  id_card_url?: string;
  academic_record_url?: string;
  health_documents_url?: string;
}

// واجهة السجل الأكاديمي
export interface AcademicData {
  grade_id: number;
  class_id: number;
  academic_year: string;
}

// واجهة بيانات الطالب الكاملة
export interface CompleteStudentData {
  student: StudentData;
  guardian?: GuardianData;
  financial?: FinancialData;
  installments?: InstallmentData[];
  health?: HealthData;
  additional?: AdditionalData;
  attachments?: AttachmentData;
  academic?: AcademicData;
}

/**
 * التحقق من وجود طالب بنفس رقم الهوية
 * @param idNumber رقم هوية الطالب
 * @returns بيانات الطالب إذا كان موجودًا، وإلا null
 */
export const checkStudentExists = async (idNumber: string) => {
  const { data, error } = await supabase
    .from('students')
    .select('*')
    .eq('id_number', idNumber)
    .single();

  if (error && error.code !== 'PGRST116') {
    console.error('Error checking student existence:', error);
    throw new Error('حدث خطأ أثناء التحقق من وجود الطالب');
  }

  return data;
};

/**
 * إضافة طالب جديد إلى قاعدة البيانات
 * @param studentData بيانات الطالب
 * @returns بيانات الطالب المضاف
 */
export const addStudent = async (studentData: StudentData) => {
  // التحقق من وجود الطالب
  const existingStudent = await checkStudentExists(studentData.id_number);
  if (existingStudent) {
    throw new Error(`يوجد طالب مسجل بالفعل برقم الهوية ${studentData.id_number}`);
  }

  // إضافة الطالب
  const { data, error } = await supabase
    .from('students')
    .insert({
      ...studentData,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    })
    .select()
    .single();

  if (error) {
    console.error('Error adding student:', error);
    throw new Error('حدث خطأ أثناء إضافة الطالب: ' + error.message);
  }

  return data;
};

/**
 * تحديث بيانات طالب موجود
 * @param studentId معرف الطالب
 * @param studentData بيانات الطالب المحدثة
 * @returns بيانات الطالب المحدثة
 */
export const updateStudent = async (studentId: number, studentData: Partial<StudentData>) => {
  const { data, error } = await supabase
    .from('students')
    .update({
      ...studentData,
      updated_at: new Date().toISOString(),
    })
    .eq('id', studentId)
    .select()
    .single();

  if (error) {
    console.error('Error updating student:', error);
    throw new Error('حدث خطأ أثناء تحديث بيانات الطالب: ' + error.message);
  }

  return data;
};

/**
 * إضافة طالب كامل مع جميع البيانات المرتبطة
 * @param completeData بيانات الطالب الكاملة
 * @returns معرف الطالب المضاف
 */
export const addCompleteStudent = async (completeData: CompleteStudentData) => {
  // بدء معاملة
  try {
    // 1. إضافة بيانات الطالب الأساسية
    const student = await addStudent(completeData.student);
    const studentId = student.id;

    // 2. إضافة بيانات ولي الأمر (إذا وجدت)
    if (completeData.guardian) {
      const { error: guardianError } = await supabase
        .from('guardians')
        .insert({
          student_id: studentId,
          ...completeData.guardian,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        });

      if (guardianError) {
        throw new Error('حدث خطأ أثناء إضافة بيانات ولي الأمر: ' + guardianError.message);
      }
    }

    // 3. إضافة البيانات المالية (إذا وجدت)
    if (completeData.financial) {
      const { data: financialData, error: financialError } = await supabase
        .from('financial_info')
        .insert({
          student_id: studentId,
          ...completeData.financial,
          created_at: new Date().toISOString(),
        })
        .select()
        .single();

      if (financialError) {
        throw new Error('حدث خطأ أثناء إضافة البيانات المالية: ' + financialError.message);
      }

      // 4. إضافة الأقساط (إذا وجدت)
      if (completeData.installments && completeData.installments.length > 0 && financialData) {
        const installmentsWithFinancialId = completeData.installments.map(installment => ({
          financial_info_id: financialData.id,
          ...installment,
          status: 'pending',
          created_at: new Date().toISOString(),
        }));

        const { error: installmentsError } = await supabase
          .from('installments')
          .insert(installmentsWithFinancialId);

        if (installmentsError) {
          throw new Error('حدث خطأ أثناء إضافة الأقساط: ' + installmentsError.message);
        }
      }
    }

    // 5. إضافة المعلومات الصحية (إذا وجدت)
    if (completeData.health) {
      const { error: healthError } = await supabase
        .from('health_info')
        .insert({
          student_id: studentId,
          ...completeData.health,
          created_at: new Date().toISOString(),
        });

      if (healthError) {
        throw new Error('حدث خطأ أثناء إضافة المعلومات الصحية: ' + healthError.message);
      }
    }

    // 6. إضافة المعلومات الإضافية (إذا وجدت)
    if (completeData.additional) {
      const { error: additionalError } = await supabase
        .from('additional_info')
        .insert({
          student_id: studentId,
          ...completeData.additional,
          created_at: new Date().toISOString(),
        });

      if (additionalError) {
        throw new Error('حدث خطأ أثناء إضافة المعلومات الإضافية: ' + additionalError.message);
      }
    }

    // 7. إضافة المرفقات (إذا وجدت)
    if (completeData.attachments) {
      const { error: attachmentsError } = await supabase
        .from('attachments')
        .insert({
          student_id: studentId,
          ...completeData.attachments,
          created_at: new Date().toISOString(),
        });

      if (attachmentsError) {
        throw new Error('حدث خطأ أثناء إضافة المرفقات: ' + attachmentsError.message);
      }
    }

    // 8. إضافة السجل الأكاديمي (إذا وجد)
    if (completeData.academic) {
      const { error: academicError } = await supabase
        .from('academic_records')
        .insert({
          student_id: studentId,
          ...completeData.academic,
          enrollment_date: new Date().toISOString().split('T')[0],
          status: 'active',
          created_at: new Date().toISOString(),
        });

      if (academicError) {
        throw new Error('حدث خطأ أثناء إضافة السجل الأكاديمي: ' + academicError.message);
      }
    }

    return studentId;
  } catch (error) {
    console.error('Error adding complete student:', error);
    throw error;
  }
};
