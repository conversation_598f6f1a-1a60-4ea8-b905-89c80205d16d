"use client";

import React, { useState } from 'react';
import { supabase } from '@/lib/supabase';

interface ClassSelectorProps {
  value: string;
  gradeId: string;
  onChange: (e: React.ChangeEvent<HTMLSelectElement>) => void;
  onBlur: (e: React.FocusEvent<HTMLElement>) => void;
  error?: string;
  touched?: boolean | any;
  disabled?: boolean;
}

interface Class {
  id: number;
  name: string;
  grade_id: number;
  capacity: number;
}

const ClassSelector: React.FC<ClassSelectorProps> = ({
  value,
  gradeId,
  onChange,
  onBlur,
  error,
  touched,
  disabled
}) => {
  const [classes, setClasses] = useState<Class[]>([]);
  const [loading, setLoading] = useState(false);

  // جلب الفصول بناءً على المرحلة الدراسية المختارة
  React.useEffect(() => {
    const fetchClassesByGrade = async () => {
      if (!gradeId) {
        setClasses([]);
        return;
      }

      setLoading(true);
      try {
        console.log('Fetching classes for grade:', gradeId);

        // استخدام جدول school_classes
        const { data, error } = await supabase
          .from('school_classes')
          .select('id, name, grade_id, capacity')
          .eq('grade_id', gradeId)
          .order('name');

        if (error) {
          console.error('Error fetching from school_classes:', error);
          throw new Error('لا يمكن الوصول إلى جدول الفصول');
        }

        if (data && data.length > 0) {
          console.log('Classes data from school_classes:', data);
          setClasses(data);
          setLoading(false);
          return;
        }

        console.error('لا توجد فصول دراسية للمرحلة المحددة في قاعدة البيانات');
        // لا نستخدم بيانات افتراضية، بل نعرض رسالة خطأ للمستخدم
        setClasses([]);
        throw new Error('لا توجد فصول دراسية للمرحلة المحددة. يرجى التحقق من قاعدة البيانات أو الاتصال بمسؤول النظام.');
      } catch (error: any) {
        console.error('Error in fetchClassesByGrade:', error.message || error);
        // لا نستخدم بيانات افتراضية، بل نعرض رسالة خطأ للمستخدم
        setClasses([]);
        throw new Error('حدث خطأ أثناء جلب الفصول الدراسية. يرجى التحقق من الاتصال بقاعدة البيانات أو الاتصال بمسؤول النظام.');
      } finally {
        setLoading(false);
      }
    };

    fetchClassesByGrade();
  }, [gradeId]);

  return (
    <div>
      <label htmlFor="classId" className="block text-gray-700 font-medium mb-2">
        الفصل <span className="text-danger">*</span>
      </label>
      <select
        id="classId"
        name="classId"
        className={`w-full p-3 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary
          ${touched && error ? 'border-danger' : 'border-gray-300'}`}
        onChange={onChange}
        onBlur={onBlur}
        value={value}
        disabled={disabled || loading || !gradeId}
      >
        <option value="">اختر الفصل</option>
        {classes.map((classItem) => (
          <option key={classItem.id} value={classItem.id}>
            {classItem.name}
          </option>
        ))}
      </select>
      {touched && error && (
        <p className="mt-1 text-danger text-sm">{error}</p>
      )}
      {loading && (
        <p className="mt-1 text-gray-500 text-sm">جاري تحميل الفصول...</p>
      )}
      {!loading && classes.length === 0 && gradeId && (
        <p className="mt-1 text-gray-500 text-sm">لا توجد فصول متاحة لهذه المرحلة</p>
      )}
    </div>
  );
};

export default ClassSelector;
export type { Class };
