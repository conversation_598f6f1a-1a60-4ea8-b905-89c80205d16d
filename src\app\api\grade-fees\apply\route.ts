import { NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';

export async function POST(request: Request) {
  try {
    const { gradeId, academicYear } = await request.json();

    if (!gradeId || !academicYear) {
      return NextResponse.json(
        { error: 'معرف المرحلة والسنة الدراسية مطلوبان' },
        { status: 400 }
      );
    }

    // الحصول على رسوم المرحلة
    const { data: gradeFee, error: gradeFeeError } = await supabase
      .from('grade_fees')
      .select('*')
      .eq('grade_id', gradeId)
      .eq('academic_year', academicYear)
      .single();

    if (gradeFeeError || !gradeFee) {
      return NextResponse.json(
        { error: 'لم يتم العثور على رسوم لهذه المرحلة والسنة الدراسية' },
        { status: 404 }
      );
    }

    // الحصول على جميع الطلاب في هذه المرحلة والسنة الدراسية
    const { data: students, error: studentsError } = await supabase
      .from('students')
      .select(`
        id,
        full_name,
        academic_records!inner (
          grade_id,
          academic_year
        )
      `)
      .eq('academic_records.grade_id', gradeId)
      .eq('academic_records.academic_year', academicYear);

    if (studentsError) {
      console.error('Error fetching students:', studentsError);
      return NextResponse.json(
        { error: 'حدث خطأ أثناء جلب بيانات الطلاب' },
        { status: 500 }
      );
    }

    if (!students || students.length === 0) {
      return NextResponse.json(
        { message: 'لا يوجد طلاب في هذه المرحلة والسنة الدراسية' },
        { status: 200 }
      );
    }

    let updatedCount = 0;
    let createdCount = 0;
    const errors: string[] = [];

    // تطبيق الرسوم على كل طالب
    for (const student of students) {
      try {
        // التحقق من وجود سجل مالي للطالب
        const { data: existingFinancial, error: checkError } = await supabase
          .from('financial_info')
          .select('id, tuition_fee, discount_amount, paid_amount, installments_count, payment_method')
          .eq('student_id', student.id)
          .single();

        if (checkError && checkError.code !== 'PGRST116') {
          errors.push(`خطأ في التحقق من البيانات المالية للطالب ${student.full_name}: ${checkError.message}`);
          continue;
        }

        if (existingFinancial) {
          // تحديث السجل الموجود
          const { error: updateError } = await supabase
            .from('financial_info')
            .update({
              tuition_fee: gradeFee.tuition_fee,
              updated_at: new Date().toISOString()
            })
            .eq('id', existingFinancial.id);

          if (updateError) {
            errors.push(`خطأ في تحديث البيانات المالية للطالب ${student.full_name}: ${updateError.message}`);
            continue;
          }

          // إعادة حساب الأقساط إذا كان الطالب يدفع بالأقساط
          if (existingFinancial.payment_method === 'installments' && existingFinancial.installments_count > 0) {
            const netAmount = gradeFee.tuition_fee - (existingFinancial.discount_amount || 0) - (existingFinancial.paid_amount || 0);

            if (netAmount > 0) {
              const installmentAmount = netAmount / existingFinancial.installments_count;

              const { error: installmentError } = await supabase
                .from('installments')
                .update({
                  amount: installmentAmount,
                  remaining: installmentAmount,
                  updated_at: new Date().toISOString()
                })
                .eq('financial_info_id', existingFinancial.id)
                .eq('status', 'pending');

              if (installmentError) {
                errors.push(`خطأ في تحديث الأقساط للطالب ${student.full_name}: ${installmentError.message}`);
              }
            }
          }

          updatedCount++;
        } else {
          // إنشاء سجل مالي جديد
          const { error: createError } = await supabase
            .from('financial_info')
            .insert({
              student_id: student.id,
              tuition_fee: gradeFee.tuition_fee,
              discount_amount: 0,
              paid_amount: 0,
              payment_method: 'cash',
              installments_count: 0
            });

          if (createError) {
            errors.push(`خطأ في إنشاء البيانات المالية للطالب ${student.full_name}: ${createError.message}`);
            continue;
          }

          createdCount++;
        }
      } catch (error: any) {
        errors.push(`خطأ عام للطالب ${student.full_name}: ${error.message}`);
      }
    }

    // إنشاء سجل في تاريخ التحديثات
    await supabase
      .from('grade_fees_history')
      .insert({
        grade_fee_id: gradeFee.id,
        new_tuition_fee: gradeFee.tuition_fee,
        reason: `تطبيق الرسوم على ${updatedCount + createdCount} طالب في المرحلة`
      });

    return NextResponse.json({
      success: true,
      message: `تم تطبيق الرسوم بنجاح`,
      details: {
        totalStudents: students.length,
        updatedCount,
        createdCount,
        errorsCount: errors.length,
        errors: errors.length > 0 ? errors : undefined
      }
    });

  } catch (error: any) {
    console.error('Error applying grade fees:', error);
    return NextResponse.json(
      { error: 'حدث خطأ أثناء تطبيق الرسوم: ' + error.message },
      { status: 500 }
    );
  }
}
