// أنواع البيانات المستخدمة في الجداول

// نوع بيانات الطالب
export type Student = {
  id: number;
  full_name: string;
  id_number: string;
  birth_date: string;
  gender: string;
  religion?: string;
  marital_status?: string;
  email?: string;
  phone?: string;
  created_at: string;
  updated_at: string;
};

// نوع بيانات المرحلة الدراسية
export type Grade = {
  id: number;
  name: string;
  level: string;
  created_at: string;
};

// نوع بيانات الفصل الدراسي
export type Class = {
  id: number;
  name: string;
  grade_id: number;
  capacity: number;
  created_at: string;
  grade_name?: string; // اسم المرحلة الدراسية (للعرض فقط)
};

// نوع بيانات ولي الأمر
export type Guardian = {
  id: number;
  student_id: number;
  full_name: string;
  relationship: string;
  id_number: string;
  phone: string;
  email?: string;
  occupation?: string;
  workplace?: string;
  created_at: string;
};

// نوع بيانات المعلومات المالية
export type FinancialInfo = {
  id: number;
  student_id: number;
  tuition_fee: number;
  discount_amount: number;
  discount_reason?: string;
  paid_amount: number;
  payment_method: string;
  installments_count?: number;
  created_at: string;
};

// نوع بيانات الأقساط
export type Installment = {
  id: number;
  financial_info_id: number;
  amount: number;
  due_date: string;
  status: 'pending' | 'paid' | 'overdue';
  payment_date?: string;
  // الحقول الإضافية
  paid?: number;
  discount?: number;
  remaining?: number;
  created_at: string;
};

// نوع بيانات المدفوعات الأخرى
export type OtherPayment = {
  id: number;
  student_id: number;
  file_opening_fee: number;
  books_fee: number;
  uniform_count: number;
  uniform_total: number;
  transportation_fee: number;
  total_amount: number;
  created_at: string;
};

// نوع بيانات المعلومات الصحية
export type HealthInfo = {
  id: number;
  student_id: number;
  general_health?: string;
  chronic_diseases?: string;
  drug_allergies?: string;
  doctor_name?: string;
  doctor_phone?: string;
  insurance_company?: string;
  insurance_policy_number?: string;
  created_at: string;
};

// نوع بيانات المعلومات الإضافية
export type AdditionalInfo = {
  id: number;
  student_id: number;
  hobbies?: string;
  achievements?: string;
  special_needs?: string;
  transportation_method?: string;
  additional_notes?: string;
  created_at: string;
};

// نوع بيانات المرفقات
export type Attachment = {
  id: number;
  student_id: number;
  photo_url?: string;
  birth_certificate_url?: string;
  id_card_url?: string;
  academic_record_url?: string;
  health_documents_url?: string;
  created_at: string;
};

// نوع بيانات السجل الأكاديمي
export type AcademicRecord = {
  id: number;
  student_id: number;
  grade_id: number;
  class_id: number;
  academic_year: string;
  registration_date: string;
  created_at: string;
  grade_name?: string; // اسم المرحلة الدراسية (للعرض فقط)
  class_name?: string; // اسم الفصل الدراسي (للعرض فقط)
};

// نوع بيانات الحضور والغياب
export type Attendance = {
  id: number;
  student_id: number;
  date: string;
  status: 'present' | 'absent' | 'late' | 'excused';
  notes?: string;
  created_at: string;
};

// نوع بيانات التحويلات
export type Transfer = {
  id: number;
  student_id: number;
  from_grade_id: number;
  to_grade_id: number;
  from_class_id?: number;
  to_class_id?: number;
  transfer_date: string;
  reason?: string;
  created_at: string;
  from_grade_name?: string; // اسم المرحلة الدراسية السابقة (للعرض فقط)
  to_grade_name?: string; // اسم المرحلة الدراسية الجديدة (للعرض فقط)
  from_class_name?: string; // اسم الفصل الدراسي السابق (للعرض فقط)
  to_class_name?: string; // اسم الفصل الدراسي الجديد (للعرض فقط)
};
