import { NextRequest, NextResponse } from 'next/server';
import { GoogleSpreadsheet } from 'google-spreadsheet';
import { createJWT } from '@/lib/googleSheetsClient';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const spreadsheetId = searchParams.get('spreadsheetId');
    const sheetIndex = parseInt(searchParams.get('sheetIndex') || '0');

    if (!spreadsheetId) {
      return NextResponse.json(
        { error: 'معرف جدول البيانات مطلوب' },
        { status: 400 }
      );
    }

    // إنشاء JWT للمصادقة
    const jwt = createJWT();

    // الاتصال بجدول البيانات
    const doc = new GoogleSpreadsheet(spreadsheetId, jwt);
    await doc.loadInfo();

    // التحقق من وجود الورقة المطلوبة
    if (!doc.sheetsByIndex[sheetIndex]) {
      return NextResponse.json(
        { error: `الورقة رقم ${sheetIndex} غير موجودة في جدول البيانات` },
        { status: 404 }
      );
    }

    // الحصول على الورقة
    const sheet = doc.sheetsByIndex[sheetIndex];
    await sheet.loadHeaderRow();

    // قراءة جميع الصفوف
    const rows = await sheet.getRows();

    if (rows.length === 0) {
      return NextResponse.json(
        { error: 'لا توجد بيانات في جدول البيانات' },
        { status: 404 }
      );
    }

    // تحويل البيانات إلى تنسيق أكثر سهولة للاستخدام
    const headers = sheet.headerValues;
    const data = rows.map(row => {
      const rowData: Record<string, any> = {};
      headers.forEach(header => {
        rowData[header] = row.get(header);
      });
      return rowData;
    });

    return NextResponse.json({
      success: true,
      data: data,
      message: `تم استرجاع ${data.length} سجل من جدول البيانات بنجاح`,
      sheetInfo: {
        title: doc.title,
        sheetTitle: sheet.title,
        rowCount: rows.length,
        columnCount: headers.length
      }
    });

  } catch (error) {
    console.error('Error accessing Google Sheets:', error);
    
    if (error instanceof Error) {
      if (error.message.includes('Unable to parse')) {
        return NextResponse.json(
          { error: 'معرف جدول البيانات غير صحيح' },
          { status: 400 }
        );
      }
      
      if (error.message.includes('not found')) {
        return NextResponse.json(
          { error: 'جدول البيانات غير موجود أو لا يمكن الوصول إليه' },
          { status: 404 }
        );
      }
      
      if (error.message.includes('permission')) {
        return NextResponse.json(
          { error: 'ليس لديك صلاحية للوصول إلى جدول البيانات هذا' },
          { status: 403 }
        );
      }
    }

    return NextResponse.json(
      { error: 'حدث خطأ أثناء الوصول إلى جدول البيانات' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { spreadsheetId, sheetIndex = 0, data } = body;

    if (!spreadsheetId) {
      return NextResponse.json(
        { error: 'معرف جدول البيانات مطلوب' },
        { status: 400 }
      );
    }

    if (!data || !Array.isArray(data) || data.length === 0) {
      return NextResponse.json(
        { error: 'البيانات مطلوبة ويجب أن تكون مصفوفة غير فارغة' },
        { status: 400 }
      );
    }

    // إنشاء JWT للمصادقة
    const jwt = createJWT();

    // الاتصال بجدول البيانات
    const doc = new GoogleSpreadsheet(spreadsheetId, jwt);
    await doc.loadInfo();

    // التحقق من وجود الورقة المطلوبة
    if (!doc.sheetsByIndex[sheetIndex]) {
      return NextResponse.json(
        { error: `الورقة رقم ${sheetIndex} غير موجودة في جدول البيانات` },
        { status: 404 }
      );
    }

    // الحصول على الورقة
    const sheet = doc.sheetsByIndex[sheetIndex];

    // إضافة البيانات
    await sheet.addRows(data);

    return NextResponse.json({
      success: true,
      message: `تم إضافة ${data.length} سجل إلى جدول البيانات بنجاح`,
      sheetInfo: {
        title: doc.title,
        sheetTitle: sheet.title,
        addedRows: data.length
      }
    });

  } catch (error) {
    console.error('Error adding data to Google Sheets:', error);
    
    return NextResponse.json(
      { error: 'حدث خطأ أثناء إضافة البيانات إلى جدول البيانات' },
      { status: 500 }
    );
  }
}
