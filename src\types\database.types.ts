export interface Student {
  student_id: string;
  full_name: string;
  date_of_birth: string;
  gender: string;
  nationality: string;
  address: string;
  phone_number: string;
  email: string;
  parent_guardian_info: string;
  registration_date: string;
  status: string;
}

export interface AcademicRecord {
  record_id: string;
  student_id: string;
  academic_year: string;
  grade_level: string;
  class_section: string;
  second_language: string;
}

export interface Document {
  document_id: string;
  student_id: string;
  document_type: string;
  document_path: string;
  upload_date: string;
  verified: boolean;
}

export interface Grade {
  grade_id: string;
  student_id: string;
  subject_id: string;
  term: string;
  year: string;
  score: number;
  assessment_type: string;
}

export interface Attendance {
  attendance_id: string;
  student_id: string;
  date: string;
  status: string;
  reason: string | null;
}

export interface Transfer {
  transfer_id: string;
  student_id: string;
  request_date: string;
  transfer_type: string;
  destination: string;
  status: string;
  notes: string | null;
}

export interface HealthRecord {
  health_record_id: string;
  student_id: string;
  blood_type: string;
  allergies: string | null;
  chronic_conditions: string | null;
  emergency_contact: string;
  last_checkup_date: string;
}

export interface Subject {
  subject_id: string;
  subject_name: string;
  grade_level: string;
  description: string | null;
}
