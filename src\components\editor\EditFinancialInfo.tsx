"use client";

import React, { useState, useEffect } from 'react';
import { supabase } from '@/lib/supabase';
import { FinancialInfo } from '@/types/table.types';
import { FaMoneyBillWave, FaEdit } from 'react-icons/fa';
import EditModal from '@/components/ui/EditModal';


interface EditFinancialInfoProps {
  studentId: string | number;
  financialInfo: FinancialInfo | null;
  onUpdate?: () => void;
}

const EditFinancialInfo: React.FC<EditFinancialInfoProps> = ({
  studentId,
  financialInfo,
  onUpdate
}) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [formData, setFormData] = useState<Partial<FinancialInfo>>({});
  const [errors, setErrors] = useState<Record<string, string>>({});

  // تم إزالة استخدام سياق التزامن المالي

  // تحديث بيانات النموذج عند تغيير المعلومات المالية
  useEffect(() => {
    if (financialInfo) {
      setFormData({
        total_fees: financialInfo.total_fees,
        discount_percentage: financialInfo.discount_percentage,
        discount_amount: financialInfo.discount_amount,
        final_fees: financialInfo.final_fees,
        payment_method: financialInfo.payment_method,
        notes: financialInfo.notes || '',
      });
    } else {
      // إذا لم تكن هناك معلومات مالية، قم بتعيين القيم الافتراضية
      setFormData({
        total_fees: 0,
        discount_percentage: 0,
        discount_amount: 0,
        final_fees: 0,
        payment_method: 'cash',
        notes: '',
      });
    }
  }, [financialInfo]);

  // فتح النافذة المنبثقة
  const handleOpenModal = () => {
    setIsModalOpen(true);
  };

  // إغلاق النافذة المنبثقة
  const handleCloseModal = () => {
    setIsModalOpen(false);
    setErrors({});
  };

  // تحديث بيانات النموذج
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;

    // تحويل القيم العددية
    if (['total_fees', 'discount_percentage', 'discount_amount'].includes(name)) {
      const numValue = parseFloat(value) || 0;

      setFormData(prev => {
        const newData = { ...prev, [name]: numValue };

        // حساب المبلغ النهائي تلقائيًا
        if (name === 'total_fees' || name === 'discount_amount') {
          newData.final_fees = Math.max(0, (newData.total_fees || 0) - (newData.discount_amount || 0));
        } else if (name === 'discount_percentage') {
          const totalFees = newData.total_fees || 0;
          newData.discount_amount = Math.round(totalFees * (numValue / 100));
          newData.final_fees = Math.max(0, totalFees - newData.discount_amount);
        }

        return newData;
      });
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));
    }

    // إزالة الخطأ عند تعديل الحقل
    if (errors[name]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
  };

  // التحقق من صحة البيانات
  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if ((formData.total_fees || 0) < 0) {
      newErrors.total_fees = 'يجب أن تكون الرسوم الإجمالية قيمة موجبة';
    }

    if ((formData.discount_percentage || 0) < 0 || (formData.discount_percentage || 0) > 100) {
      newErrors.discount_percentage = 'يجب أن تكون نسبة الخصم بين 0 و 100';
    }

    if ((formData.discount_amount || 0) < 0) {
      newErrors.discount_amount = 'يجب أن يكون مبلغ الخصم قيمة موجبة';
    }

    if ((formData.discount_amount || 0) > (formData.total_fees || 0)) {
      newErrors.discount_amount = 'لا يمكن أن يكون مبلغ الخصم أكبر من الرسوم الإجمالية';
    }

    if (!formData.payment_method) {
      newErrors.payment_method = 'طريقة الدفع مطلوبة';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // حفظ التغييرات
  const handleSave = async () => {
    if (!validateForm()) return;

    try {
      setIsSaving(true);

      if (financialInfo) {
        // تحديث المعلومات المالية الموجودة
        const { data, error } = await supabase
          .from('financial_info')
          .update({
            total_fees: formData.total_fees,
            discount_percentage: formData.discount_percentage,
            discount_amount: formData.discount_amount,
            final_fees: formData.final_fees,
            payment_method: formData.payment_method,
            notes: formData.notes || null,
            updated_at: new Date().toISOString(),
          })
          .eq('id', financialInfo.id)
          .select()
          .single();

        if (error) {
          throw new Error(error.message);
        }

        console.log('تم تحديث المعلومات المالية:', data);
      } else {
        // إنشاء معلومات مالية جديدة
        const { data, error } = await supabase
          .from('financial_info')
          .insert({
            student_id: studentId,
            total_fees: formData.total_fees,
            discount_percentage: formData.discount_percentage,
            discount_amount: formData.discount_amount,
            final_fees: formData.final_fees,
            payment_method: formData.payment_method,
            notes: formData.notes || null,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
          })
          .select()
          .single();

        if (error) {
          throw new Error(error.message);
        }

        console.log('تم إنشاء معلومات مالية جديدة:', data);
      }

      // تحديث البيانات في الواجهة
      await refreshFinancialInfo(studentId);

      // استدعاء دالة التحديث إذا كانت موجودة
      if (onUpdate) {
        onUpdate();
      }

      // إغلاق النافذة المنبثقة
      handleCloseModal();
    } catch (err: any) {
      console.error('خطأ في حفظ المعلومات المالية:', err);
      alert(`حدث خطأ أثناء حفظ البيانات: ${err.message}`);
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <>
      {/* زر التعديل */}
      <button
        onClick={handleOpenModal}
        className="bg-[#5578EB] hover:bg-[#5578EB]/90 text-white px-3 py-1 rounded-md flex items-center transition-colors duration-200 text-sm"
        title="تعديل المعلومات المالية"
      >
        <FaEdit className="ml-1" />
        تعديل
      </button>

      {/* نافذة التعديل المنبثقة */}
      <EditModal
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        onSave={handleSave}
        title="تعديل المعلومات المالية"
        isSaving={isSaving}
        color="#0ABB87"
        icon={<FaMoneyBillWave size={18} />}
      >
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* الرسوم الإجمالية */}
          <div>
            <label className="block text-gray-700 mb-1">الرسوم الإجمالية <span className="text-red-500">*</span></label>
            <input
              type="number"
              name="total_fees"
              value={formData.total_fees || 0}
              onChange={handleChange}
              className={`w-full p-2 border rounded-md ${errors.total_fees ? 'border-red-500' : 'border-gray-300'}`}
              min="0"
              step="0.01"
              dir="rtl"
            />
            {errors.total_fees && <p className="text-red-500 text-sm mt-1">{errors.total_fees}</p>}
          </div>

          {/* نسبة الخصم */}
          <div>
            <label className="block text-gray-700 mb-1">نسبة الخصم (%)</label>
            <input
              type="number"
              name="discount_percentage"
              value={formData.discount_percentage || 0}
              onChange={handleChange}
              className={`w-full p-2 border rounded-md ${errors.discount_percentage ? 'border-red-500' : 'border-gray-300'}`}
              min="0"
              max="100"
              step="0.01"
              dir="rtl"
            />
            {errors.discount_percentage && <p className="text-red-500 text-sm mt-1">{errors.discount_percentage}</p>}
          </div>

          {/* مبلغ الخصم */}
          <div>
            <label className="block text-gray-700 mb-1">مبلغ الخصم</label>
            <input
              type="number"
              name="discount_amount"
              value={formData.discount_amount || 0}
              onChange={handleChange}
              className={`w-full p-2 border rounded-md ${errors.discount_amount ? 'border-red-500' : 'border-gray-300'}`}
              min="0"
              step="0.01"
              dir="rtl"
            />
            {errors.discount_amount && <p className="text-red-500 text-sm mt-1">{errors.discount_amount}</p>}
          </div>

          {/* المبلغ النهائي */}
          <div>
            <label className="block text-gray-700 mb-1">المبلغ النهائي</label>
            <input
              type="number"
              name="final_fees"
              value={formData.final_fees || 0}
              readOnly
              className="w-full p-2 border border-gray-300 rounded-md bg-gray-100"
              dir="rtl"
            />
            <p className="text-gray-500 text-xs mt-1">يتم حسابه تلقائيًا</p>
          </div>

          {/* طريقة الدفع */}
          <div>
            <label className="block text-gray-700 mb-1">طريقة الدفع <span className="text-red-500">*</span></label>
            <select
              name="payment_method"
              value={formData.payment_method || ''}
              onChange={handleChange}
              className={`w-full p-2 border rounded-md ${errors.payment_method ? 'border-red-500' : 'border-gray-300'}`}
              dir="rtl"
            >
              <option value="">اختر طريقة الدفع</option>
              <option value="cash">نقدًا</option>
              <option value="bank_transfer">تحويل بنكي</option>
              <option value="credit_card">بطاقة ائتمان</option>
              <option value="installments">أقساط</option>
            </select>
            {errors.payment_method && <p className="text-red-500 text-sm mt-1">{errors.payment_method}</p>}
          </div>

          {/* ملاحظات */}
          <div className="col-span-2">
            <label className="block text-gray-700 mb-1">ملاحظات</label>
            <textarea
              name="notes"
              value={formData.notes || ''}
              onChange={handleChange}
              className="w-full p-2 border border-gray-300 rounded-md"
              rows={3}
              dir="rtl"
              placeholder="أدخل أي ملاحظات إضافية هنا"
            ></textarea>
          </div>
        </div>
      </EditModal>
    </>
  );
};

export default EditFinancialInfo;
