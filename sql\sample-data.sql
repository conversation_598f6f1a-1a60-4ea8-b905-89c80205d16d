-- ملف SQL لإدخال بيانات نموذجية في قاعدة البيانات

-- إدخال بيانات المراحل الدراسية (إذا لم تكن موجودة بالفعل)
INSERT INTO school_grades (name, level)
VALUES 
('الصف الأول الابتدائي', 'ابتدائي'),
('الصف الثاني الابتدائي', 'ابتدائي'),
('الصف الثالث الابتدائي', 'ابتدائي'),
('الصف الرابع الابتدائي', 'ابتدائي'),
('الصف الخامس الابتدائي', 'ابتدائي'),
('الصف السادس الابتدائي', 'ابتدائي'),
('الصف الأول الإعدادي', 'إعدادي'),
('الصف الثاني الإعدادي', 'إعدادي'),
('الصف الثالث الإعدادي', 'إعدادي'),
('الصف الأول الثانوي', 'ثانوي'),
('الصف الثاني الثانوي', 'ثانوي'),
('الصف الثالث الثانوي', 'ثانوي')
ON CONFLICT (id) DO NOTHING;

-- إدخال بيانات الفصول الدراسية (إذا لم تكن موجودة بالفعل)
INSERT INTO school_classes (name, grade_id, capacity)
VALUES 
('1A', 1, 30),
('1B', 1, 30),
('1C', 1, 30),
('1D', 1, 30),
('2A', 2, 30),
('2B', 2, 30),
('2C', 2, 30),
('2D', 2, 30),
('2E', 2, 30),
('3A', 3, 30),
('3B', 3, 30),
('3C', 3, 30),
('4A', 4, 30),
('4B', 4, 30),
('5A', 5, 30),
('5B', 5, 30),
('6A', 6, 30),
('6B', 6, 30),
('7A', 7, 30),
('7B', 7, 30),
('8A', 8, 30),
('8B', 8, 30),
('9A', 9, 30),
('9B', 9, 30)
ON CONFLICT (id) DO NOTHING;

-- إدخال بيانات الطلاب
INSERT INTO students (full_name, id_number, birth_date, gender, religion, marital_status, email, phone)
VALUES 
('أحمد محمد علي', '30101010101010', '2015-01-15', 'male', 'islam', 'single', '<EMAIL>', '01012345678'),
('فاطمة أحمد محمود', '30102020202020', '2015-02-20', 'female', 'islam', 'single', '<EMAIL>', '01023456789'),
('محمود إبراهيم السيد', '30103030303030', '2015-03-10', 'male', 'islam', 'single', '<EMAIL>', '01034567890'),
('سارة خالد عبدالله', '30104040404040', '2015-04-05', 'female', 'islam', 'single', '<EMAIL>', '01045678901'),
('عمر حسن محمد', '30105050505050', '2015-05-25', 'male', 'islam', 'single', '<EMAIL>', '01056789012'),
('نور محمد أحمد', '30106060606060', '2015-06-15', 'female', 'islam', 'single', '<EMAIL>', '01067890123'),
('كريم عبدالرحمن محمود', '30107070707070', '2015-07-20', 'male', 'islam', 'single', '<EMAIL>', '01078901234'),
('هدى سمير علي', '30108080808080', '2015-08-10', 'female', 'islam', 'single', '<EMAIL>', '01089012345'),
('يوسف طارق محمد', '30109090909090', '2015-09-05', 'male', 'islam', 'single', '<EMAIL>', '01090123456'),
('منة الله أحمد إبراهيم', '30110101010101', '2015-10-15', 'female', 'islam', 'single', '<EMAIL>', '01001234567')
ON CONFLICT (id_number) DO NOTHING;

-- إدخال بيانات أولياء الأمور
INSERT INTO guardians (student_id, full_name, relationship, id_number, phone, email, occupation, workplace)
VALUES 
(
  (SELECT id FROM students WHERE id_number = '30101010101010'),
  'محمد علي إبراهيم',
  'father',
  '28001010101010',
  '01112345678',
  '<EMAIL>',
  'مهندس',
  'شركة المقاولون العرب'
),
(
  (SELECT id FROM students WHERE id_number = '30101010101010'),
  'سميرة أحمد محمود',
  'mother',
  '28501010101010',
  '01123456789',
  '<EMAIL>',
  'مدرسة',
  'مدرسة النصر الابتدائية'
),
(
  (SELECT id FROM students WHERE id_number = '30102020202020'),
  'أحمد محمود السيد',
  'father',
  '28002020202020',
  '01134567890',
  '<EMAIL>',
  'طبيب',
  'مستشفى المواساة'
),
(
  (SELECT id FROM students WHERE id_number = '30103030303030'),
  'إبراهيم السيد علي',
  'father',
  '28003030303030',
  '01145678901',
  '<EMAIL>',
  'محاسب',
  'بنك مصر'
),
(
  (SELECT id FROM students WHERE id_number = '30104040404040'),
  'خالد عبدالله محمد',
  'father',
  '28004040404040',
  '01156789012',
  '<EMAIL>',
  'مدير مبيعات',
  'شركة الأهرام للإلكترونيات'
)
ON CONFLICT (id) DO NOTHING;

-- إدخال بيانات المعلومات المالية
INSERT INTO financial_info (student_id, tuition_fee, discount_amount, discount_reason, paid_amount, payment_method, installments_count)
VALUES 
(
  (SELECT id FROM students WHERE id_number = '30101010101010'),
  5000,
  500,
  'خصم الأخوة',
  1000,
  'installments',
  2
),
(
  (SELECT id FROM students WHERE id_number = '30102020202020'),
  5000,
  0,
  NULL,
  5000,
  'cash',
  0
),
(
  (SELECT id FROM students WHERE id_number = '30103030303030'),
  5000,
  1000,
  'خصم المتفوقين',
  1000,
  'installments',
  3
),
(
  (SELECT id FROM students WHERE id_number = '30104040404040'),
  5000,
  250,
  'خصم الأخوة',
  1250,
  'installments',
  3
),
(
  (SELECT id FROM students WHERE id_number = '30105050505050'),
  5000,
  0,
  NULL,
  2500,
  'installments',
  2
)
ON CONFLICT (id) DO NOTHING;

-- إدخال بيانات الأقساط
INSERT INTO installments (financial_info_id, due_date, amount, paid, discount, status)
VALUES 
(
  (SELECT id FROM financial_info WHERE student_id = (SELECT id FROM students WHERE id_number = '30101010101010')),
  '2025-06-15',
  2250,
  1000,
  0,
  'partially_paid'
),
(
  (SELECT id FROM financial_info WHERE student_id = (SELECT id FROM students WHERE id_number = '30101010101010')),
  '2025-07-15',
  2250,
  0,
  0,
  'pending'
),
(
  (SELECT id FROM financial_info WHERE student_id = (SELECT id FROM students WHERE id_number = '30103030303030')),
  '2025-06-15',
  1333.33,
  1000,
  0,
  'partially_paid'
),
(
  (SELECT id FROM financial_info WHERE student_id = (SELECT id FROM students WHERE id_number = '30103030303030')),
  '2025-07-15',
  1333.33,
  0,
  0,
  'pending'
),
(
  (SELECT id FROM financial_info WHERE student_id = (SELECT id FROM students WHERE id_number = '30103030303030')),
  '2025-08-15',
  1333.34,
  0,
  0,
  'pending'
),
(
  (SELECT id FROM financial_info WHERE student_id = (SELECT id FROM students WHERE id_number = '30104040404040')),
  '2025-06-15',
  1250,
  1250,
  0,
  'paid'
),
(
  (SELECT id FROM financial_info WHERE student_id = (SELECT id FROM students WHERE id_number = '30104040404040')),
  '2025-07-15',
  1250,
  0,
  0,
  'pending'
),
(
  (SELECT id FROM financial_info WHERE student_id = (SELECT id FROM students WHERE id_number = '30104040404040')),
  '2025-08-15',
  1250,
  0,
  0,
  'pending'
),
(
  (SELECT id FROM financial_info WHERE student_id = (SELECT id FROM students WHERE id_number = '30105050505050')),
  '2025-06-15',
  2500,
  2500,
  0,
  'paid'
),
(
  (SELECT id FROM financial_info WHERE student_id = (SELECT id FROM students WHERE id_number = '30105050505050')),
  '2025-07-15',
  2500,
  0,
  0,
  'pending'
)
ON CONFLICT (id) DO NOTHING;

-- إدخال بيانات المدفوعات الأخرى
INSERT INTO other_payments (student_id, description, amount, payment_date, is_paid)
VALUES 
(
  (SELECT id FROM students WHERE id_number = '30101010101010'),
  'رسوم فتح الملف',
  300,
  '2025-05-15',
  TRUE
),
(
  (SELECT id FROM students WHERE id_number = '30101010101010'),
  'رسوم الكتب',
  500,
  '2025-05-15',
  TRUE
),
(
  (SELECT id FROM students WHERE id_number = '30101010101010'),
  'الزي المدرسي',
  600,
  '2025-05-15',
  TRUE
),
(
  (SELECT id FROM students WHERE id_number = '30102020202020'),
  'رسوم فتح الملف',
  300,
  '2025-05-15',
  TRUE
),
(
  (SELECT id FROM students WHERE id_number = '30102020202020'),
  'رسوم الكتب',
  500,
  '2025-05-15',
  TRUE
),
(
  (SELECT id FROM students WHERE id_number = '30103030303030'),
  'رسوم فتح الملف',
  300,
  '2025-05-15',
  TRUE
),
(
  (SELECT id FROM students WHERE id_number = '30103030303030'),
  'رسوم الكتب',
  500,
  '2025-05-15',
  TRUE
),
(
  (SELECT id FROM students WHERE id_number = '30103030303030'),
  'النقل المدرسي',
  1000,
  '2025-05-15',
  FALSE
)
ON CONFLICT (id) DO NOTHING;

-- إدخال بيانات المعلومات الصحية
INSERT INTO health_info (student_id, blood_type, has_allergies, allergies_details, has_chronic_diseases, chronic_diseases_details, emergency_contact_name, emergency_contact_phone)
VALUES 
(
  (SELECT id FROM students WHERE id_number = '30101010101010'),
  'A+',
  TRUE,
  'حساسية من البنسلين',
  FALSE,
  NULL,
  'محمد علي إبراهيم',
  '01112345678'
),
(
  (SELECT id FROM students WHERE id_number = '30102020202020'),
  'O+',
  FALSE,
  NULL,
  FALSE,
  NULL,
  'أحمد محمود السيد',
  '01134567890'
),
(
  (SELECT id FROM students WHERE id_number = '30103030303030'),
  'B+',
  FALSE,
  NULL,
  TRUE,
  'ربو خفيف',
  'إبراهيم السيد علي',
  '01145678901'
)
ON CONFLICT (id) DO NOTHING;

-- إدخال بيانات المعلومات الإضافية
INSERT INTO additional_info (student_id, previous_school, transfer_reason, hobbies, talents, behavioral_notes, academic_notes)
VALUES 
(
  (SELECT id FROM students WHERE id_number = '30101010101010'),
  'مدرسة النصر الابتدائية',
  'انتقال السكن',
  'القراءة، كرة القدم',
  'الرسم، الخط العربي',
  'طالب مهذب ومتعاون',
  'متفوق في مادة الرياضيات'
),
(
  (SELECT id FROM students WHERE id_number = '30102020202020'),
  'مدرسة الأمل الابتدائية',
  'قرب المدرسة من المنزل',
  'السباحة، الشطرنج',
  'العزف على البيانو',
  'طالبة هادئة ومجتهدة',
  'متفوقة في اللغة الإنجليزية'
),
(
  (SELECT id FROM students WHERE id_number = '30103030303030'),
  'مدرسة المستقبل الابتدائية',
  'مستوى تعليمي أفضل',
  'كرة السلة، القراءة',
  'البرمجة',
  'طالب نشيط ومتحمس',
  'يحتاج إلى تحسين مستواه في اللغة العربية'
)
ON CONFLICT (id) DO NOTHING;

-- إدخال بيانات المرفقات
INSERT INTO attachments (student_id, photo_url, birth_certificate_url, id_card_url, previous_school_certificate_url)
VALUES 
(
  (SELECT id FROM students WHERE id_number = '30101010101010'),
  'https://example.com/photos/student1.jpg',
  'https://example.com/documents/birth_certificate1.pdf',
  'https://example.com/documents/id_card1.pdf',
  'https://example.com/documents/previous_school_certificate1.pdf'
),
(
  (SELECT id FROM students WHERE id_number = '30102020202020'),
  'https://example.com/photos/student2.jpg',
  'https://example.com/documents/birth_certificate2.pdf',
  'https://example.com/documents/id_card2.pdf',
  'https://example.com/documents/previous_school_certificate2.pdf'
),
(
  (SELECT id FROM students WHERE id_number = '30103030303030'),
  'https://example.com/photos/student3.jpg',
  'https://example.com/documents/birth_certificate3.pdf',
  'https://example.com/documents/id_card3.pdf',
  'https://example.com/documents/previous_school_certificate3.pdf'
)
ON CONFLICT (id) DO NOTHING;

-- إدخال بيانات السجلات الأكاديمية
INSERT INTO academic_records (student_id, grade_id, class_id, academic_year, enrollment_date, status)
VALUES 
(
  (SELECT id FROM students WHERE id_number = '30101010101010'),
  1,
  1,
  '2025-2026',
  '2025-05-15',
  'active'
),
(
  (SELECT id FROM students WHERE id_number = '30102020202020'),
  1,
  2,
  '2025-2026',
  '2025-05-15',
  'active'
),
(
  (SELECT id FROM students WHERE id_number = '30103030303030'),
  1,
  3,
  '2025-2026',
  '2025-05-15',
  'active'
),
(
  (SELECT id FROM students WHERE id_number = '30104040404040'),
  1,
  4,
  '2025-2026',
  '2025-05-15',
  'active'
),
(
  (SELECT id FROM students WHERE id_number = '30105050505050'),
  2,
  5,
  '2025-2026',
  '2025-05-15',
  'active'
)
ON CONFLICT (id) DO NOTHING;
