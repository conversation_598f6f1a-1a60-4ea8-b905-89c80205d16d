# شرح هيكل قاعدة البيانات

هذا المستند يشرح هيكل قاعدة البيانات المستخدمة في نظام إدارة المدرسة. تم تقسيم ملف `supabase-schema.sql` إلى أجزاء منطقية لتسهيل فهمه وتنفيذه.

## الجداول الرئيسية

### 1. جدول المراحل الدراسية (grades)

يحتوي على معلومات المراحل الدراسية المختلفة:

```sql
CREATE TABLE IF NOT EXISTS grades (
  id SERIAL PRIMARY KEY,
  name TEXT NOT NULL,
  level TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

**الحقول:**
- `id`: المفتاح الرئيسي للجدول (يتزايد تلقائياً)
- `name`: اسم المرحلة الدراسية (مثل: الصف الأول الابتدائي)
- `level`: مستوى المرحلة (ابتدائي، إعدادي، إلخ)
- `created_at`: تاريخ إنشاء السجل

### 2. جدول الفصول الدراسية (classes)

يحتوي على معلومات الفصول الدراسية لكل مرحلة:

```sql
CREATE TABLE IF NOT EXISTS classes (
  id SERIAL PRIMARY KEY,
  name TEXT NOT NULL,
  grade_id INTEGER REFERENCES grades(id) ON DELETE CASCADE,
  capacity INTEGER DEFAULT 30,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

**الحقول:**
- `id`: المفتاح الرئيسي للجدول
- `name`: اسم الفصل (مثل: 1A، 2B، إلخ)
- `grade_id`: مفتاح خارجي يربط الفصل بالمرحلة الدراسية
- `capacity`: السعة القصوى للفصل (الافتراضي: 30 طالب)
- `created_at`: تاريخ إنشاء السجل

**العلاقات:**
- كل فصل ينتمي إلى مرحلة دراسية واحدة
- عند حذف مرحلة دراسية، يتم حذف جميع الفصول المرتبطة بها تلقائياً (CASCADE)

## بيانات الإعداد الأولي

### 1. بيانات المراحل الدراسية

تم إدخال 9 مراحل دراسية:
- 6 مراحل ابتدائية (من الصف الأول إلى السادس)
- 3 مراحل إعدادية (من الصف الأول إلى الثالث)

### 2. بيانات الفصول الدراسية

تم إدخال فصول دراسية لكل مرحلة:
- الصفوف الابتدائية: تحتوي على فصول متعددة (مثل 1A، 1B، إلخ)
- الصفوف الإعدادية: تحتوي على فصول متعددة (مثل 1A PRE، 1B PRE، إلخ)

## ملاحظات هامة

1. تم استخدام `ON CONFLICT (id) DO NOTHING` في إدخال بيانات المراحل الدراسية لتجنب تكرار البيانات عند تنفيذ السكريبت أكثر من مرة.

2. جميع الفصول الدراسية تم تعيين سعتها الافتراضية إلى 30 طالب.

3. يمكن توسيع هذا الهيكل لاحقاً لإضافة جداول إضافية مثل جدول الطلاب وجدول المعلمين وجدول المواد الدراسية.