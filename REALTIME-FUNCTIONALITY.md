# دليل وظائف الوقت الحقيقي (Real-Time) في نظام إدارة المدرسة

## مقدمة

تم تحديث نظام إدارة المدرسة ليدعم وظائف الوقت الحقيقي (Real-Time) باستخدام Supabase. هذا يعني أن أي تغييرات تحدث في قاعدة البيانات ستظهر فوراً في واجهة المستخدم دون الحاجة إلى إعادة تحميل الصفحة أو تحديثها يدوياً.

## كيف تعمل وظائف الوقت الحقيقي؟

عندما يتم إجراء تغيير في قاعدة البيانات (إضافة، تعديل، أو حذف)، يقوم Supabase بإرسال إشعار إلى التطبيق. يقوم التطبيق بدوره بتحديث البيانات المعروضة في واجهة المستخدم تلقائياً.

## الميزات المدعومة بالوقت الحقيقي

تم تفعيل وظائف الوقت الحقيقي في الأجزاء التالية من النظام:

1. **قائمة الطلاب**: عند إضافة طالب جديد أو تعديل أو حذف طالب موجود، ستظهر التغييرات فوراً في قائمة الطلاب.

2. **عرض بيانات الطالب**: عند عرض بيانات طالب معين، أي تغييرات تحدث على بياناته ستظهر فوراً دون الحاجة إلى إعادة تحميل الصفحة.

3. **البيانات المالية**: تحديثات الأقساط والمدفوعات والمعلومات المالية الأخرى تظهر فوراً في جميع الشاشات ذات الصلة.

4. **بيانات ولي الأمر**: أي تغييرات في بيانات أولياء الأمور تظهر فوراً.

5. **المعلومات الصحية والإضافية**: تحديثات المعلومات الصحية والإضافية تظهر فوراً.

6. **المرفقات**: إضافة أو حذف المرفقات يظهر فوراً.

## فوائد وظائف الوقت الحقيقي

1. **تجربة مستخدم أفضل**: لا حاجة لإعادة تحميل الصفحة لرؤية التغييرات الجديدة.

2. **بيانات محدثة دائماً**: ضمان أن جميع المستخدمين يرون أحدث البيانات في جميع الأوقات.

3. **تعاون أفضل**: عندما يعمل عدة مستخدمين على نفس البيانات، يمكنهم رؤية تغييرات بعضهم البعض فوراً.

4. **أداء أفضل**: تقليل عدد طلبات قاعدة البيانات اللازمة لتحديث البيانات.

## كيفية استخدام وظائف الوقت الحقيقي

لا تحتاج إلى القيام بأي إجراء خاص لاستخدام وظائف الوقت الحقيقي. النظام يعمل تلقائياً كما يلي:

1. عند فتح صفحة قائمة الطلاب، سيتم تحميل البيانات الأولية ثم سيبدأ النظام في الاستماع للتغييرات.

2. عند فتح صفحة عرض بيانات طالب معين، سيتم تحميل بيانات الطالب ثم سيبدأ النظام في الاستماع للتغييرات على بيانات هذا الطالب.

3. عند إجراء تغيير (مثل تعديل بيانات طالب أو إضافة قسط جديد)، سيتم تحديث البيانات في قاعدة البيانات وسيتم إرسال إشعار إلى جميع المستخدمين الذين يشاهدون هذه البيانات.

## ملاحظات فنية للمطورين

تم تنفيذ وظائف الوقت الحقيقي باستخدام:

1. **Supabase Realtime**: خدمة الوقت الحقيقي المدمجة في Supabase.

2. **React Hooks**: تم إنشاء hooks مخصصة للتعامل مع اشتراكات الوقت الحقيقي.

3. **Context API**: تم استخدام React Context لمشاركة بيانات الوقت الحقيقي بين المكونات المختلفة.

المكونات الرئيسية:

- `useSupabaseRealtime`: Hook مخصص للاشتراك في تغييرات جدول معين.
- `RealtimeContext`: Context لمشاركة بيانات الوقت الحقيقي بين المكونات.
- `RealtimeProvider`: Provider يقوم بإعداد اشتراكات الوقت الحقيقي وإدارتها.

## استكشاف الأخطاء وإصلاحها

إذا لم تظهر التغييرات في الوقت الحقيقي:

1. **تأكد من اتصال الإنترنت**: يتطلب الوقت الحقيقي اتصالاً مستقراً بالإنترنت.

2. **تحقق من وحدة تحكم المتصفح**: قد تظهر أخطاء في وحدة تحكم المتصفح توضح سبب المشكلة.

3. **أعد تحميل الصفحة**: في بعض الحالات النادرة، قد تحتاج إلى إعادة تحميل الصفحة لإعادة إنشاء اتصال الوقت الحقيقي.

4. **تحقق من إعدادات Supabase**: تأكد من تفعيل خدمة Realtime في مشروع Supabase الخاص بك.

## الخلاصة

وظائف الوقت الحقيقي تجعل نظام إدارة المدرسة أكثر تفاعلية وفعالية. يمكن للمستخدمين الآن رؤية التغييرات فور حدوثها، مما يحسن تجربة المستخدم ويضمن أن جميع المستخدمين يعملون دائماً مع أحدث البيانات.
