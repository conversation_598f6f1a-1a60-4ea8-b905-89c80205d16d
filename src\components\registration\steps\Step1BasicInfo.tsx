"use client";

import React, { useEffect, useState } from 'react';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import ImportStudentData from '../ImportStudentData';
import ImportedDataPreview from '../ImportedDataPreview';
import { FaFileDownload, FaFileExport, FaFileCsv, FaFileExcel, FaFileCode } from 'react-icons/fa';

interface Step1Props {
  onComplete: (data: any, isValid: boolean) => void;
  formData: Record<string, any>;
}

const Step1BasicInfo: React.FC<Step1Props> = ({ onComplete, formData }) => {
  // حالة لتتبع ما إذا كانت البيانات قد تم استيرادها
  const [isImported, setIsImported] = useState(false);
  // حالة لتخزين البيانات المستوردة
  const [importedData, setImportedData] = useState<Record<string, any> | null>(null);

  // التحقق من صحة البيانات باستخدام Yup
  const validationSchema = Yup.object({
    fullName: Yup.string().required('الاسم الكامل مطلوب'),
    idNumber: Yup.string().required('رقم الهوية مطلوب'),
    birthDate: Yup.date().required('تاريخ الميلاد مطلوب').max(new Date(), 'تاريخ الميلاد يجب أن يكون في الماضي'),
    gender: Yup.string().required('الجنس مطلوب').oneOf(['male', 'female'], 'اختر الجنس'),
    religion: Yup.string(),
    maritalStatus: Yup.string(),
    email: Yup.string().email('البريد الإلكتروني غير صالح'),
    phone: Yup.string(),
  });

  // إعداد نموذج Formik
  const formik = useFormik({
    initialValues: {
      fullName: formData.fullName || '',
      idNumber: formData.idNumber || '',
      birthDate: formData.birthDate || '',
      gender: formData.gender || '',
      religion: formData.religion || '',
      maritalStatus: formData.maritalStatus || '',
      email: formData.email || '',
      phone: formData.phone || '',
    },
    validationSchema,
    onSubmit: (values) => {
      console.log('Form submitted with values:', values);
    },
  });

  // معالجة استيراد البيانات
  const handleImport = (data: Record<string, any>) => {
    // تحديث قيم النموذج بالبيانات المستوردة
    if (data) {
      Object.keys(data).forEach(key => {
        if (formik.values.hasOwnProperty(key) && data[key]) {
          formik.setFieldValue(key, data[key]);
        }
      });
      setImportedData(data);
      setIsImported(true);
    }
  };

  // تحميل نموذج فارغ
  const handleExportTemplate = () => {
    try {
      // تحميل النموذج الثابت من المجلد العام
      const link = document.createElement('a');
      link.href = '/student-template-utf8.csv';
      link.download = 'نموذج_بيانات_الطالب.csv';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } catch (error) {
      console.error('Error downloading template:', error);
    }
  };

  // تصدير بيانات الطالب إلى CSV
  const handleExportToCSV = () => {
    try {
      // التحقق من وجود بيانات
      if (!formik.values.fullName || !formik.values.idNumber) {
        alert('يرجى إدخال البيانات الأساسية (الاسم الكامل ورقم الهوية) قبل التصدير');
        return;
      }

      // تحويل البيانات إلى تنسيق CSV
      const headers = [
        'الاسم الكامل',
        'رقم الهوية',
        'تاريخ الميلاد',
        'الجنس',
        'الديانة',
        'الحالة الاجتماعية',
        'البريد الإلكتروني',
        'رقم الهاتف'
      ];

      // تحويل قيم الجنس والديانة إلى العربية
      const genderMapping: Record<string, string> = {
        'male': 'ذكر',
        'female': 'أنثى',
      };

      const religionMapping: Record<string, string> = {
        'islam': 'الإسلام',
        'christianity': 'المسيحية',
      };

      // إنشاء صف البيانات
      const dataRow = [
        formik.values.fullName || '',
        formik.values.idNumber || '',
        formik.values.birthDate || '',
        genderMapping[formik.values.gender] || formik.values.gender || '',
        religionMapping[formik.values.religion] || formik.values.religion || '',
        formik.values.maritalStatus || '',
        formik.values.email || '',
        formik.values.phone || '',
      ];

      // إنشاء محتوى CSV
      const csvContent = headers.join(',') + '\n' + dataRow.join(',');

      // إنشاء ملف للتنزيل
      const blob = new Blob(['\uFEFF' + csvContent], { type: 'text/csv;charset=utf-8;' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `بيانات_الطالب_${formik.values.fullName}.csv`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } catch (error) {
      console.error('Error exporting to CSV:', error);
      alert('حدث خطأ أثناء تصدير البيانات إلى CSV');
    }
  };

  // تصدير بيانات الطالب إلى Excel
  const handleExportToExcel = () => {
    try {
      // التحقق من وجود بيانات
      if (!formik.values.fullName || !formik.values.idNumber) {
        alert('يرجى إدخال البيانات الأساسية (الاسم الكامل ورقم الهوية) قبل التصدير');
        return;
      }

      // تحويل قيم الجنس والديانة إلى العربية
      const genderMapping: Record<string, string> = {
        'male': 'ذكر',
        'female': 'أنثى',
      };

      const religionMapping: Record<string, string> = {
        'islam': 'الإسلام',
        'christianity': 'المسيحية',
      };

      // إنشاء كائن البيانات
      const data = {
        'الاسم الكامل': formik.values.fullName || '',
        'رقم الهوية': formik.values.idNumber || '',
        'تاريخ الميلاد': formik.values.birthDate || '',
        'الجنس': genderMapping[formik.values.gender] || formik.values.gender || '',
        'الديانة': religionMapping[formik.values.religion] || formik.values.religion || '',
        'الحالة الاجتماعية': formik.values.maritalStatus || '',
        'البريد الإلكتروني': formik.values.email || '',
        'رقم الهاتف': formik.values.phone || '',
      };

      // تحويل البيانات إلى ملف Excel
      const worksheet = XLSX.utils.json_to_sheet([data]);
      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, 'بيانات_الطالب');

      // تصدير الملف
      XLSX.writeFile(workbook, `بيانات_الطالب_${formik.values.fullName}.xlsx`);
    } catch (error) {
      console.error('Error exporting to Excel:', error);
      alert('حدث خطأ أثناء تصدير البيانات إلى Excel');
    }
  };

  // تصدير بيانات الطالب إلى JSON
  const handleExportToJSON = () => {
    try {
      // التحقق من وجود بيانات
      if (!formik.values.fullName || !formik.values.idNumber) {
        alert('يرجى إدخال البيانات الأساسية (الاسم الكامل ورقم الهوية) قبل التصدير');
        return;
      }

      // تحويل قيم الجنس والديانة إلى العربية
      const genderMapping: Record<string, string> = {
        'male': 'ذكر',
        'female': 'أنثى',
      };

      const religionMapping: Record<string, string> = {
        'islam': 'الإسلام',
        'christianity': 'المسيحية',
      };

      // إنشاء كائن البيانات
      const data = {
        'الاسم الكامل': formik.values.fullName || '',
        'رقم الهوية': formik.values.idNumber || '',
        'تاريخ الميلاد': formik.values.birthDate || '',
        'الجنس': genderMapping[formik.values.gender] || formik.values.gender || '',
        'الديانة': religionMapping[formik.values.religion] || formik.values.religion || '',
        'الحالة الاجتماعية': formik.values.maritalStatus || '',
        'البريد الإلكتروني': formik.values.email || '',
        'رقم الهاتف': formik.values.phone || '',
      };

      // تحويل البيانات إلى نص JSON
      const jsonString = JSON.stringify(data, null, 2);

      // إنشاء ملف للتنزيل
      const blob = new Blob([jsonString], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `بيانات_الطالب_${formik.values.fullName}.json`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } catch (error) {
      console.error('Error exporting to JSON:', error);
      alert('حدث خطأ أثناء تصدير البيانات إلى JSON');
    }
  };

  // تحديث البيانات عند تغييرها
  useEffect(() => {
    const isValid = formik.isValid && (formik.dirty || isImported);

    // استخدام مرجع ثابت لدالة onComplete لتجنب الحلقات اللانهائية
    const timer = setTimeout(() => {
      onComplete(formik.values, isValid);
    }, 100);

    return () => clearTimeout(timer);
    // إزالة onComplete من مصفوفة التبعيات لتجنب الحلقات اللانهائية
  }, [formik.values, formik.isValid, formik.dirty, isImported]);

  return (
    <div className="bg-white rounded-lg p-6">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-bold text-primary border-r-4 border-primary pr-3">
          بيانات الطالب الأساسية
        </h2>

        <div className="flex flex-col gap-2">
          <div className="flex gap-2">
            <button
              type="button"
              onClick={handleExportTemplate}
              className="flex items-center justify-center gap-2 bg-[#0ABB87] hover:bg-[#09a77a] text-white py-2 px-4 rounded-md transition-colors duration-300"
            >
              <FaFileDownload size={16} />
              <span>تنزيل نموذج CSV</span>
            </button>
          </div>

          <div className="flex gap-2">
            <div className="relative group">
              <button
                type="button"
                className="flex items-center justify-center gap-2 bg-[#21ADE7] hover:bg-[#1D9BCE] text-white py-2 px-4 rounded-md transition-colors duration-300"
              >
                <FaFileExport size={16} />
                <span>تصدير البيانات</span>
              </button>

              <div className="absolute left-0 mt-2 w-48 bg-white rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-10">
                <button
                  type="button"
                  onClick={handleExportToCSV}
                  className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                >
                  <FaFileCsv className="ml-2 text-[#217346]" />
                  <span>تصدير إلى CSV</span>
                </button>
                <button
                  type="button"
                  onClick={handleExportToExcel}
                  className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                >
                  <FaFileExcel className="ml-2 text-[#217346]" />
                  <span>تصدير إلى Excel</span>
                </button>
                <button
                  type="button"
                  onClick={handleExportToJSON}
                  className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                >
                  <FaFileCode className="ml-2 text-[#F0DB4F]" />
                  <span>تصدير إلى JSON</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* مكون استيراد البيانات */}
      <ImportStudentData
        onImport={handleImport}
        onSaveToDatabase={(result) => {
          console.log('تم حفظ البيانات في قاعدة البيانات:', result);
          // يمكن إضافة المزيد من الإجراءات هنا بعد حفظ البيانات
        }}
      />

      {/* عرض البيانات المستوردة */}
      <ImportedDataPreview data={importedData} isVisible={isImported} />

      <form className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* الاسم الكامل */}
          <div>
            <label htmlFor="fullName" className="block text-gray-700 font-medium mb-2">
              الاسم الكامل <span className="text-danger">*</span>
            </label>
            <input
              type="text"
              id="fullName"
              name="fullName"
              className={`w-full p-3 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary
                ${formik.touched.fullName && formik.errors.fullName ? 'border-danger' : 'border-gray-300'}`}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              value={formik.values.fullName}
              placeholder="أدخل الاسم الكامل"
            />
            {formik.touched.fullName && formik.errors.fullName && (
              <p className="mt-1 text-danger text-sm">{formik.errors.fullName}</p>
            )}
          </div>

          {/* رقم الهوية */}
          <div>
            <label htmlFor="idNumber" className="block text-gray-700 font-medium mb-2">
              رقم الهوية <span className="text-danger">*</span>
            </label>
            <input
              type="text"
              id="idNumber"
              name="idNumber"
              className={`w-full p-3 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary
                ${formik.touched.idNumber && formik.errors.idNumber ? 'border-danger' : 'border-gray-300'}`}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              value={formik.values.idNumber}
              placeholder="أدخل رقم الهوية"
            />
            {formik.touched.idNumber && formik.errors.idNumber && (
              <p className="mt-1 text-danger text-sm">{formik.errors.idNumber}</p>
            )}
          </div>

          {/* تاريخ الميلاد */}
          <div>
            <label htmlFor="birthDate" className="block text-gray-700 font-medium mb-2">
              تاريخ الميلاد <span className="text-danger">*</span>
            </label>
            <input
              type="date"
              id="birthDate"
              name="birthDate"
              className={`w-full p-3 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary
                ${formik.touched.birthDate && formik.errors.birthDate ? 'border-danger' : 'border-gray-300'}`}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              value={formik.values.birthDate}
            />
            {formik.touched.birthDate && formik.errors.birthDate && (
              <p className="mt-1 text-danger text-sm">{formik.errors.birthDate}</p>
            )}
          </div>

          {/* الجنس */}
          <div>
            <label htmlFor="gender" className="block text-gray-700 font-medium mb-2">
              الجنس <span className="text-danger">*</span>
            </label>
            <select
              id="gender"
              name="gender"
              className={`w-full p-3 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary
                ${formik.touched.gender && formik.errors.gender ? 'border-danger' : 'border-gray-300'}`}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              value={formik.values.gender}
            >
              <option value="">اختر الجنس</option>
              <option value="male">ذكر</option>
              <option value="female">أنثى</option>
            </select>
            {formik.touched.gender && formik.errors.gender && (
              <p className="mt-1 text-danger text-sm">{formik.errors.gender}</p>
            )}
          </div>

          {/* الديانة */}
          <div>
            <label htmlFor="religion" className="block text-gray-700 font-medium mb-2">
              الديانة
            </label>
            <select
              id="religion"
              name="religion"
              className="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              value={formik.values.religion}
            >
              <option value="">اختر الديانة</option>
              <option value="islam">الإسلام</option>
              <option value="christianity">المسيحية</option>
            </select>
          </div>

          {/* الحالة الاجتماعية */}
          <div>
            <label htmlFor="maritalStatus" className="block text-gray-700 font-medium mb-2">
              الحالة الاجتماعية
            </label>
            <select
              id="maritalStatus"
              name="maritalStatus"
              className="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              value={formik.values.maritalStatus}
            >
              <option value="">اختر الحالة الاجتماعية</option>
              <option value="single">أعزب</option>
              <option value="married">متزوج</option>
              <option value="divorced">مطلق</option>
              <option value="widowed">أرمل</option>
            </select>
          </div>

          {/* البريد الإلكتروني */}
          <div>
            <label htmlFor="email" className="block text-gray-700 font-medium mb-2">
              البريد الإلكتروني
            </label>
            <input
              type="email"
              id="email"
              name="email"
              className={`w-full p-3 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary
                ${formik.touched.email && formik.errors.email ? 'border-danger' : 'border-gray-300'}`}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              value={formik.values.email}
              placeholder="أدخل البريد الإلكتروني"
            />
            {formik.touched.email && formik.errors.email && (
              <p className="mt-1 text-danger text-sm">{formik.errors.email}</p>
            )}
          </div>

          {/* رقم الهاتف */}
          <div>
            <label htmlFor="phone" className="block text-gray-700 font-medium mb-2">
              رقم الهاتف
            </label>
            <input
              type="tel"
              id="phone"
              name="phone"
              className="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              value={formik.values.phone}
              placeholder="أدخل رقم الهاتف"
            />
          </div>
        </div>
      </form>

      <div className="mt-4 text-gray-500 text-sm">
        <p>الحقول المميزة بـ <span className="text-danger">*</span> إلزامية</p>
      </div>
    </div>
  );
};

export default Step1BasicInfo;
