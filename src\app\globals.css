@import "tailwindcss";
@import "../styles/cursor.css";

:root {
  --background: #ffffff;
  --foreground: #171717;
  --primary: #21ADE7;
  --secondary: #5578EB;
  --success: #0ABB87;
  --danger: #FD1361;
  --info: #384AD7;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: 'Cairo', Arial, Helvetica, sans-serif;
}

/* Estilos para la dirección RTL */
.rtl {
  direction: rtl;
  text-align: right;
}

/* Estilos para los iconos de Material */
.material-icons {
  font-family: 'Material Icons';
  font-weight: normal;
  font-style: normal;
  font-size: 24px;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  -webkit-font-feature-settings: 'liga';
  -webkit-font-smoothing: antialiased;
}

/* Estilos para el sidebar */
.sidebar-transition {
  transition: width 0.3s ease-in-out;
}

/* Estilos para las animaciones */
@keyframes slideDown {
  from {
    transform: translateY(-10px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.animate-slide-down {
  animation: slideDown 0.3s ease-in-out;
}

/* Estilos para los botones */
.btn-primary {
  background-color: var(--primary);
  color: white;
}

.btn-secondary {
  background-color: var(--secondary);
  color: white;
}

.btn-success {
  background-color: var(--success);
  color: white;
}

.btn-danger {
  background-color: var(--danger);
  color: white;
}

.btn-info {
  background-color: var(--info);
  color: white;
}

/* Estilos para las tarjetas */
.card {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* Estilos para las tablas */
.table-container {
  overflow-x: auto;
}

.table {
  width: 100%;
  border-collapse: collapse;
}

.table th,
.table td {
  padding: 0.75rem;
  border-bottom: 1px solid #e2e8f0;
}

.table th {
  background-color: #f8fafc;
  font-weight: 600;
  text-align: right;
}

/* Estilos para los formularios */
.form-control {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid #e2e8f0;
  border-radius: 0.25rem;
}

.form-label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
}

.form-group {
  margin-bottom: 1rem;
}
