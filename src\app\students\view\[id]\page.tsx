"use client";

import React, { useState, useEffect, useRef } from 'react';
import { useRouter, useParams } from 'next/navigation';
import Layout from "@/components/Layout";
import { supabase } from '@/lib/supabase';
import { Student } from '@/types/table.types';
import {
  FaArrowRight, FaUser, FaUserFriends, FaMoneyBillWave, FaCalendarAlt,
  FaMoneyCheckAlt, FaFileInvoiceDollar, FaHeartbeat, FaInfoCircle, FaPaperclip,
  FaPrint, FaDownload
} from 'react-icons/fa';
import SimplePrintButton from '@/components/print/SimplePrintButton';
import {
  StepViewer,
  ViewBasicInfo,
  ViewGuardianInfo,
  ViewFinancialInfo,
  ViewInstallments,
  ViewOtherPayments,
  ViewSummary,
  ViewHealthInfo,
  ViewAdditionalInfo,
  ViewAttachments
} from '@/components/viewer';
import { useSupabaseRealtime } from '@/hooks/useSupabaseRealtime';

export default function StudentView() {
  const router = useRouter();
  const params = useParams();
  const studentId = params.id as string;

  const [student, setStudent] = useState<Student | null>(null);
  const [guardians, setGuardians] = useState<any[]>([]);
  const [financialInfo, setFinancialInfo] = useState<any>(null);
  const [installments, setInstallments] = useState<any[]>([]);
  const [otherPayments, setOtherPayments] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [loadingTimeout, setLoadingTimeout] = useState<NodeJS.Timeout | null>(null);

  // مرجع للتحكم في التحميل
  const initialLoadRef = useRef(false);
  const contentRef = useRef<HTMLDivElement>(null);

  // إعداد اشتراك الوقت الحقيقي لهذا الطالب المحدد
  useSupabaseRealtime(
    {
      table: 'students',
      filter: 'id',
      filterValue: studentId,
    },
    (payload) => {
      if (payload.eventType === 'UPDATE' || payload.eventType === 'INSERT') {
        setStudent(payload.new as Student);
        setIsLoading(false);
      } else if (payload.eventType === 'DELETE') {
        setStudent(null);
        setError('تم حذف بيانات الطالب');
        setIsLoading(false);
      }
    }
  );

  // جلب بيانات الطالب وجميع البيانات المرتبطة
  useEffect(() => {
    // تنفيذ التحميل مرة واحدة فقط عند تحميل الصفحة
    if (initialLoadRef.current) return;

    // إعداد مؤقت لإظهار حالة التحميل لمدة معينة على الأقل
    const timeout = setTimeout(() => {
      // إذا لم تكتمل عملية التحميل بعد، نستمر في عرض حالة التحميل
      if (!initialLoadRef.current) {
        setIsLoading(true);
      }
    }, 300);

    setLoadingTimeout(timeout);

    async function fetchAllData() {
      try {
        setIsLoading(true);
        setError(null);

        if (!studentId) {
          throw new Error('معرف الطالب غير موجود');
        }

        // جلب بيانات الطالب
        const { data: studentData, error: studentError } = await supabase
          .from('students')
          .select('*')
          .eq('id', studentId)
          .single();

        if (studentError) {
          throw new Error(studentError.message);
        }

        if (!studentData) {
          throw new Error('الطالب غير موجود');
        }

        setStudent(studentData);

        // جلب بيانات الأوصياء
        const { data: guardiansData, error: guardiansError } = await supabase
          .from('guardians')
          .select('*')
          .eq('student_id', studentId);

        if (guardiansError) {
          console.error('Error fetching guardians:', guardiansError);
        } else {
          setGuardians(guardiansData || []);
        }

        // جلب البيانات المالية
        const { data: financialData, error: financialError } = await supabase
          .from('financial_info')
          .select('*')
          .eq('student_id', studentId)
          .single();

        if (financialError && financialError.code !== 'PGRST116') {
          console.error('Error fetching financial info:', financialError);
        } else if (financialData) {
          setFinancialInfo(financialData);

          // جلب الأقساط إذا كانت البيانات المالية موجودة
          const { data: installmentsData, error: installmentsError } = await supabase
            .from('installments')
            .select('*')
            .eq('financial_info_id', financialData.id);

          if (installmentsError) {
            console.error('Error fetching installments:', installmentsError);
          } else {
            setInstallments(installmentsData || []);
          }
        }

        // جلب المدفوعات الأخرى
        const { data: otherPaymentsData, error: otherPaymentsError } = await supabase
          .from('other_payments')
          .select('*')
          .eq('student_id', studentId);

        if (otherPaymentsError) {
          console.error('Error fetching other payments:', otherPaymentsError);
        } else {
          setOtherPayments(otherPaymentsData || []);
        }

        initialLoadRef.current = true;
      } catch (err: any) {
        console.error('Error fetching student data:', err);
        setError(err.message || 'حدث خطأ أثناء جلب بيانات الطالب');
      } finally {
        // تأخير إخفاء حالة التحميل لتجنب الوميض
        setTimeout(() => {
          setIsLoading(false);
        }, 500);
      }
    }

    fetchAllData();

    // تنظيف المؤقت عند إزالة المكون
    return () => {
      if (loadingTimeout) {
        clearTimeout(loadingTimeout);
      }
    };
  }, [studentId]);

  // دالة للطباعة
  const handlePrint = () => {
    // إنشاء نافذة طباعة جديدة
    const printWindow = window.open('', '_blank');
    if (!printWindow) {
      alert('يرجى السماح بالنوافذ المنبثقة لتتمكن من الطباعة');
      return;
    }

    // إنشاء محتوى HTML للطباعة
    const printContent = `
      <!DOCTYPE html>
      <html dir="rtl" lang="ar">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>بيانات الطالب: ${student?.full_name || ''}</title>
        <style>
          @media print {
            @page {
              size: A4;
              margin: 1cm;
            }
          }
          body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            direction: rtl;
          }
          .header {
            text-align: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #21ADE7;
          }
          .header h1 {
            color: #21ADE7;
            margin-bottom: 5px;
          }
          .section {
            margin-bottom: 20px;
            page-break-inside: avoid;
          }
          .section-title {
            background-color: #f0f9ff;
            padding: 8px;
            border-right: 4px solid #21ADE7;
            margin-bottom: 10px;
            font-weight: bold;
            color: #21ADE7;
          }
          .info-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
          }
          .info-item {
            border-bottom: 1px solid #eee;
            padding-bottom: 5px;
          }
          .info-label {
            font-weight: bold;
            color: #666;
            margin-left: 5px;
          }
          .footer {
            margin-top: 30px;
            text-align: center;
            font-size: 12px;
            color: #666;
            border-top: 1px solid #eee;
            padding-top: 10px;
          }
          table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
          }
          table, th, td {
            border: 1px solid #ddd;
          }
          th {
            background-color: #f0f9ff;
            color: #333;
            font-weight: bold;
            text-align: right;
            padding: 8px;
          }
          td {
            padding: 8px;
            text-align: right;
          }
          tr:nth-child(even) {
            background-color: #f9f9f9;
          }
        </style>
      </head>
      <body>
        <div class="header">
          <h1>بيانات الطالب</h1>
          <p>${student?.full_name || ''}</p>
        </div>

        <div class="section">
          <div class="section-title">البيانات الأساسية</div>
          <div class="info-grid">
            <div class="info-item">
              <span class="info-label">الاسم الكامل:</span>
              <span>${student?.full_name || ''}</span>
            </div>
            <div class="info-item">
              <span class="info-label">رقم الهوية:</span>
              <span>${student?.id_number || ''}</span>
            </div>
            <div class="info-item">
              <span class="info-label">تاريخ الميلاد:</span>
              <span>${student?.birth_date ? new Date(student.birth_date).toLocaleDateString('ar-EG') : ''}</span>
            </div>
            <div class="info-item">
              <span class="info-label">الجنس:</span>
              <span>${student?.gender === 'male' ? 'ذكر' : student?.gender === 'female' ? 'أنثى' : student?.gender || ''}</span>
            </div>
            <div class="info-item">
              <span class="info-label">الديانة:</span>
              <span>${student?.religion === 'islam' ? 'الإسلام' : student?.religion === 'christianity' ? 'المسيحية' : student?.religion || 'غير محدد'}</span>
            </div>
            <div class="info-item">
              <span class="info-label">الحالة الاجتماعية:</span>
              <span>${student?.marital_status === 'single' ? 'أعزب' :
                     student?.marital_status === 'married' ? 'متزوج' :
                     student?.marital_status === 'divorced' ? 'مطلق' :
                     student?.marital_status === 'widowed' ? 'أرمل' :
                     student?.marital_status || 'غير محدد'}</span>
            </div>
            <div class="info-item">
              <span class="info-label">البريد الإلكتروني:</span>
              <span>${student?.email || 'غير متوفر'}</span>
            </div>
            <div class="info-item">
              <span class="info-label">رقم الهاتف:</span>
              <span>${student?.phone || 'غير متوفر'}</span>
            </div>
          </div>
        </div>

        <div class="footer">
          <p>تم إنشاء هذا التقرير في ${new Date().toLocaleDateString('ar-EG')} ${new Date().toLocaleTimeString('ar-EG')}</p>
          <p>نظام إدارة المدرسة</p>
        </div>
      </body>
      </html>
    `;

    // كتابة المحتوى في نافذة الطباعة
    printWindow.document.open();
    printWindow.document.write(printContent);
    printWindow.document.close();

    // انتظار تحميل الصفحة ثم طباعتها
    printWindow.onload = () => {
      printWindow.print();
    };
  };

  // تعريف خطوات عرض البيانات
  const steps = [
    {
      id: 1,
      title: 'البيانات الأساسية',
      component: <ViewBasicInfo studentId={studentId} />,
      icon: <FaUser size={20} />,
      color: '#21ADE7', // أزرق فاتح
    },
    {
      id: 2,
      title: 'ولي الأمر',
      component: <ViewGuardianInfo studentId={studentId} />,
      icon: <FaUserFriends size={20} />,
      color: '#5578EB', // أزرق داكن
    },
    {
      id: 3,
      title: 'القسم المالي',
      component: <ViewFinancialInfo studentId={studentId} />,
      icon: <FaMoneyBillWave size={20} />,
      color: '#FD1361', // أحمر
    },
    {
      id: 4,
      title: 'الأقساط',
      component: <ViewInstallments studentId={studentId} />,
      icon: <FaCalendarAlt size={20} />,
      color: '#0ABB87', // أخضر
    },
    {
      id: 5,
      title: 'مدفوعات أخرى',
      component: <ViewOtherPayments studentId={studentId} />,
      icon: <FaMoneyCheckAlt size={20} />,
      color: '#384AD7', // أزرق غامق
    },
    {
      id: 6,
      title: 'الإجماليات',
      component: <ViewSummary studentId={studentId} />,
      icon: <FaFileInvoiceDollar size={20} />,
      color: '#21ADE7', // أزرق فاتح
    },
    {
      id: 7,
      title: 'معلومات صحية',
      component: <ViewHealthInfo studentId={studentId} />,
      icon: <FaHeartbeat size={20} />,
      color: '#FD1361', // أحمر
    },
    {
      id: 8,
      title: 'معلومات إضافية',
      component: <ViewAdditionalInfo studentId={studentId} />,
      icon: <FaInfoCircle size={20} />,
      color: '#5578EB', // أزرق داكن
    },
    {
      id: 9,
      title: 'المرفقات',
      component: <ViewAttachments studentId={studentId} />,
      icon: <FaPaperclip size={20} />,
      color: '#0ABB87', // أخضر
    },
  ];

  return (
    <Layout>
      <div className="p-6">
        <div className="mb-6 bg-gradient-to-l from-[#21ADE7]/10 to-transparent p-4 rounded-lg border-r-4 border-[#21ADE7]">
          <div className="flex justify-between items-center">
            <div className="flex items-center">
              <div className="w-12 h-12 rounded-full bg-[#21ADE7] flex items-center justify-center text-white ml-4">
                <FaUser size={24} />
              </div>
              <div>
                <h1 className="text-2xl font-bold" style={{ color: '#21ADE7' }}>تفاصيل الطالب</h1>
                <p className="text-gray-600 mt-1">
                  {student ? student.full_name : 'عرض البيانات التفصيلية للطالب'}
                </p>
              </div>
            </div>
            <div className="flex gap-2">
              {student && (
                <>
                  <SimplePrintButton
                    student={student}
                    guardians={guardians}
                    financialInfo={financialInfo}
                    installments={installments}
                    otherPayments={otherPayments}
                    className="bg-[#0ABB87] hover:bg-[#0ABB87]/90"
                  />
                </>
              )}
              <button
                onClick={() => router.back()}
                className="bg-gray-200 hover:bg-gray-300 text-gray-700 px-4 py-2 rounded-md flex items-center transition-colors duration-200 clickable"
              >
                <FaArrowRight className="ml-2" />
                العودة
              </button>
            </div>
          </div>
        </div>

        {error && (
          <div className="mb-6 p-4 bg-[#FD1361]/10 border-r-4 border-[#FD1361] rounded-md">
            <p className="text-[#FD1361]">{error}</p>
          </div>
        )}

        <div ref={contentRef}>
          {isLoading ? (
            <div className="bg-white rounded-lg shadow-lg p-8 border-r-4 border-[#21ADE7] text-center">
              <div className="relative mx-auto mb-6">
                <div className="w-16 h-16 border-4 border-[#21ADE7]/30 border-t-[#21ADE7] rounded-full animate-spin"></div>
                <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                  <FaUser size={20} className="text-[#21ADE7]" />
                </div>
              </div>
              <h3 className="text-xl font-bold text-[#21ADE7] mb-2">جاري تحميل البيانات</h3>
              <p className="text-gray-600">يرجى الانتظار...</p>
            </div>
          ) : student ? (
            <StepViewer steps={steps} studentId={studentId} />
          ) : (
            <div className="bg-white rounded-lg shadow-md p-6 border-r-4 border-[#FD1361] text-center">
              <p className="text-[#FD1361]">لم يتم العثور على بيانات الطالب</p>
            </div>
          )}
        </div>
      </div>
    </Layout>
  );
}
