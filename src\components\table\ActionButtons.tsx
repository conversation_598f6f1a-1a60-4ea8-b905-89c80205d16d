import React, { useState } from 'react';
import { FaEye, FaEdit, FaTrash, FaTimes } from 'react-icons/fa';

interface ActionButtonsProps {
  onView?: () => void;
  onEdit?: () => void;
  onDelete?: () => void;
  viewTooltip?: string;
  editTooltip?: string;
  deleteTooltip?: string;
  hideView?: boolean;
  hideEdit?: boolean;
  hideDelete?: boolean;
  itemName?: string; // اسم العنصر للعرض في رسالة التأكيد
}

export const ActionButtons: React.FC<ActionButtonsProps> = ({
  onView,
  onEdit,
  onDelete,
  viewTooltip = 'عرض',
  editTooltip = 'تعديل',
  deleteTooltip = 'حذف',
  hideView = false,
  hideEdit = false,
  hideDelete = false,
  itemName = 'العنصر',
}) => {
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

  // معالجة النقر على زر الحذف
  const handleDeleteClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    setShowDeleteConfirm(true);
  };

  // معالجة تأكيد الحذف
  const handleConfirmDelete = (e: React.MouseEvent) => {
    e.stopPropagation();
    setShowDeleteConfirm(false);
    onDelete && onDelete();
  };

  // معالجة إلغاء الحذف
  const handleCancelDelete = (e: React.MouseEvent) => {
    e.stopPropagation();
    setShowDeleteConfirm(false);
  };

  return (
    <div className="flex items-center justify-center gap-2 relative">
      {!hideView && (
        <button
          onClick={(e) => {
            e.stopPropagation();
            onView && onView();
          }}
          className="p-1.5 rounded-full bg-[#21ADE7]/10 hover:bg-[#21ADE7]/20 transition-colors duration-200 clickable"
          title={viewTooltip}
        >
          <FaEye className="text-[#21ADE7]" size={16} />
        </button>
      )}

      {!hideEdit && (
        <button
          onClick={(e) => {
            e.stopPropagation();
            onEdit && onEdit();
          }}
          className="p-1.5 rounded-full bg-[#5578EB]/10 hover:bg-[#5578EB]/20 transition-colors duration-200 clickable"
          title={editTooltip}
        >
          <FaEdit className="text-[#5578EB]" size={16} />
        </button>
      )}

      {!hideDelete && !showDeleteConfirm && (
        <button
          onClick={handleDeleteClick}
          className="p-1.5 rounded-full bg-[#FD1361]/10 hover:bg-[#FD1361]/20 transition-colors duration-200 clickable"
          title={deleteTooltip}
        >
          <FaTrash className="text-[#FD1361]" size={16} />
        </button>
      )}

      {showDeleteConfirm && (
        <div className="absolute left-0 top-0 bg-white shadow-lg rounded-lg p-2 z-10 w-48 border border-gray-200 -translate-y-full">
          <div className="flex justify-between items-center mb-2 border-b pb-1">
            <span className="text-[#FD1361] text-xs font-bold">تأكيد الحذف</span>
            <button
              onClick={handleCancelDelete}
              className="text-gray-500 hover:text-gray-700 clickable"
            >
              <FaTimes size={12} />
            </button>
          </div>
          <p className="text-xs mb-2">هل أنت متأكد من حذف {itemName}؟</p>
          <div className="flex justify-between gap-1">
            <button
              onClick={handleCancelDelete}
              className="bg-gray-200 hover:bg-gray-300 text-gray-700 px-2 py-1 rounded text-xs clickable"
            >
              إلغاء
            </button>
            <button
              onClick={handleConfirmDelete}
              className="bg-[#FD1361] hover:bg-[#FD1361]/90 text-white px-2 py-1 rounded text-xs clickable"
            >
              تأكيد الحذف
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default ActionButtons;
