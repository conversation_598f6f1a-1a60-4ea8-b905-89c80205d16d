"use client";

import Layout from "@/components/Layout";

export default function Home() {
  return (
    <Layout>
      <div className="p-6">
        <div className="bg-white rounded-lg shadow-md p-6 border-t-4 border-primary">
          <h1 className="text-2xl font-bold text-primary mb-4">مرحباً بك في نظام إدارة المدرسة</h1>
          <p className="text-gray-600">
            نظام شامل لإدارة شؤون الطلبة والنظام المالي للمدرسة
          </p>
          <p className="mt-4 text-gray-500">
            يرجى اختيار إحدى الصفحات من القائمة الجانبية للبدء
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-6">
          <div className="bg-white rounded-lg shadow-md p-6 border-r-4 border-primary">
            <h2 className="text-lg font-semibold text-primary mb-2">شؤون الطلاب</h2>
            <p className="text-gray-600">إدارة بيانات الطلاب والسجلات الأكاديمية</p>
          </div>

          <div className="bg-white rounded-lg shadow-md p-6 border-r-4 border-secondary">
            <h2 className="text-lg font-semibold text-secondary mb-2">النظام المالي</h2>
            <p className="text-gray-600">إدارة الرسوم الدراسية والمدفوعات</p>
          </div>

          <div className="bg-white rounded-lg shadow-md p-6 border-r-4 border-success">
            <h2 className="text-lg font-semibold text-success mb-2">التقارير</h2>
            <p className="text-gray-600">عرض وتصدير التقارير المختلفة</p>
          </div>
        </div>
      </div>
    </Layout>
  );
}
