"use client";

import React from 'react';
import { Icon, IconType, COLORS } from './IconProvider';

// تعريف خصائص البطاقة
export interface CardProps {
  children: React.ReactNode;
  title?: string;
  icon?: IconType;
  iconColor?: keyof typeof COLORS | string;
  headerBg?: keyof typeof COLORS | string;
  bordered?: boolean;
  shadow?: boolean;
  rounded?: boolean;
  className?: string;
  bodyClassName?: string;
  headerClassName?: string;
  footerClassName?: string;
  footer?: React.ReactNode;
  actions?: React.ReactNode;
}

// مكون البطاقة
export const Card: React.FC<CardProps> = ({
  children,
  title,
  icon,
  iconColor = 'PRIMARY',
  headerBg = 'PRIMARY',
  bordered = true,
  shadow = true,
  rounded = true,
  className = '',
  bodyClassName = '',
  headerClassName = '',
  footerClassName = '',
  footer,
  actions,
}) => {
  // تحديد فئات البطاقة
  const cardClasses = `
    bg-white
    ${bordered ? 'border border-gray-200' : ''}
    ${shadow ? 'shadow-md' : ''}
    ${rounded ? 'rounded-lg overflow-hidden' : ''}
    ${className}
  `;

  // تحديد لون خلفية الرأس
  const headerBgColor = COLORS[headerBg as keyof typeof COLORS] || headerBg;

  // تحديد فئات الرأس
  const headerClasses = `
    ${title || icon ? 'p-4 flex items-center justify-between' : ''}
    ${title || icon ? `bg-[${headerBgColor}]/10` : ''}
    ${title || icon ? 'border-b border-gray-200' : ''}
    ${headerClassName}
  `;

  // تحديد فئات الجسم
  const bodyClasses = `
    p-4
    ${bodyClassName}
  `;

  // تحديد فئات التذييل
  const footerClasses = `
    ${footer ? 'p-4 border-t border-gray-200' : ''}
    ${footerClassName}
  `;

  return (
    <div className={cardClasses}>
      {(title || icon || actions) && (
        <div className={headerClasses}>
          <div className="flex items-center">
            {icon && (
              <div className="ml-2">
                <Icon name={icon} color={iconColor} size="MD" />
              </div>
            )}
            {title && (
              <h3 className="text-lg font-semibold">{title}</h3>
            )}
          </div>
          {actions && (
            <div className="flex items-center">
              {actions}
            </div>
          )}
        </div>
      )}
      <div className={bodyClasses}>
        {children}
      </div>
      {footer && (
        <div className={footerClasses}>
          {footer}
        </div>
      )}
    </div>
  );
};

export default Card;
