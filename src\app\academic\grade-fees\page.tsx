"use client";

import { useState, useEffect } from 'react';
import Layout from '@/components/Layout';
import { supabase } from '@/lib/supabase';
import { FaGraduationCap, FaPlus, FaEdit, FaSave, FaTimes, FaHistory, FaMoneyBillWave } from 'react-icons/fa';

interface Grade {
  id: number;
  name: string;
  level: string;
}

interface GradeFee {
  id: number;
  grade_id: number;
  academic_year: string;
  tuition_fee: number;
  is_active: boolean;
  grade_name?: string;
}

export default function GradeFeesPage() {
  const [grades, setGrades] = useState<Grade[]>([]);
  const [gradeFees, setGradeFees] = useState<GradeFee[]>([]);
  const [selectedYear, setSelectedYear] = useState('2024-2025');
  const [isLoading, setIsLoading] = useState(true);
  const [editingFee, setEditingFee] = useState<number | null>(null);
  const [showAddForm, setShowAddForm] = useState(false);
  const [formData, setFormData] = useState({
    grade_id: '',
    academic_year: selectedYear,
    tuition_fee: 0
  });

  // تحميل البيانات
  useEffect(() => {
    loadData();
  }, [selectedYear]);

  const loadData = async () => {
    try {
      setIsLoading(true);

      // تحميل المراحل الدراسية
      const { data: gradesData, error: gradesError } = await supabase
        .from('school_grades')
        .select('*')
        .order('id');

      if (gradesError) throw gradesError;
      setGrades(gradesData || []);

      // تحميل رسوم المراحل للسنة المحددة
      const { data: feesData, error: feesError } = await supabase
        .from('grade_fees')
        .select(`
          *,
          school_grades!grade_id (name)
        `)
        .eq('academic_year', selectedYear)
        .order('grade_id');

      if (feesError) throw feesError;

      const feesWithGradeName = (feesData || []).map(fee => ({
        ...fee,
        grade_name: fee.school_grades?.name || 'غير محدد'
      }));

      setGradeFees(feesWithGradeName);

    } catch (error) {
      console.error('Error loading data:', error);
      alert('حدث خطأ أثناء تحميل البيانات');
    } finally {
      setIsLoading(false);
    }
  };

  // حفظ أو تحديث الرسوم
  const handleSave = async (feeData: any) => {
    try {
      if (editingFee) {
        // تحديث
        const { error } = await supabase
          .from('grade_fees')
          .update({
            tuition_fee: parseFloat(feeData.tuition_fee),
            updated_at: new Date().toISOString()
          })
          .eq('id', editingFee);

        if (error) throw error;
        alert('تم تحديث الرسوم بنجاح! سيتم تطبيق التغييرات على جميع الطلاب في هذه المرحلة.');
      } else {
        // إضافة جديد
        const { error } = await supabase
          .from('grade_fees')
          .insert({
            grade_id: parseInt(feeData.grade_id),
            academic_year: feeData.academic_year,
            tuition_fee: parseFloat(feeData.tuition_fee)
          });

        if (error) throw error;
        alert('تم إضافة الرسوم بنجاح!');
      }

      setEditingFee(null);
      setShowAddForm(false);
      loadData();
    } catch (error: any) {
      console.error('Error saving fee:', error);
      alert('حدث خطأ أثناء حفظ البيانات: ' + error.message);
    }
  };

  // بدء التعديل
  const startEdit = (fee: GradeFee) => {
    setEditingFee(fee.id);
    setFormData({
      grade_id: fee.grade_id.toString(),
      academic_year: fee.academic_year,
      tuition_fee: fee.tuition_fee
    });
  };

  // إلغاء التعديل
  const cancelEdit = () => {
    setEditingFee(null);
    setShowAddForm(false);
    setFormData({
      grade_id: '',
      academic_year: selectedYear,
      tuition_fee: 0
    });
  };

  // تطبيق الرسوم على جميع الطلاب في المرحلة
  const applyFeesToStudents = async (gradeId: number) => {
    if (!confirm('هل أنت متأكد من تطبيق هذه الرسوم على جميع الطلاب في هذه المرحلة؟ سيتم تحديث البيانات المالية لجميع الطلاب.')) {
      return;
    }

    try {
      const response = await fetch('/api/grade-fees/apply', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          gradeId,
          academicYear: selectedYear
        }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'حدث خطأ أثناء تطبيق الرسوم');
      }

      alert(`${result.message}\n\nالتفاصيل:\n- إجمالي الطلاب: ${result.details.totalStudents}\n- تم التحديث: ${result.details.updatedCount}\n- تم الإنشاء: ${result.details.createdCount}\n- الأخطاء: ${result.details.errorsCount}`);

      if (result.details.errors && result.details.errors.length > 0) {
        console.error('Errors during application:', result.details.errors);
      }
    } catch (error: any) {
      console.error('Error applying fees:', error);
      alert('حدث خطأ أثناء تطبيق الرسوم: ' + error.message);
    }
  };

  // تحديث رسوم الطلاب الموجودين
  const updateExistingStudents = async (gradeId: number) => {
    if (!confirm('هل تريد تحديث رسوم جميع الطلاب الموجودين في هذه المرحلة؟ سيتم تطبيق الرسوم الجديدة على جميع الطلاب المسجلين حالياً.')) {
      return;
    }

    try {
      const response = await fetch('/api/grade-fees/update-existing-students', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          gradeId,
          academicYear: selectedYear
        }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'حدث خطأ أثناء تحديث رسوم الطلاب');
      }

      alert(`${result.message}\n\nالتفاصيل:\n- إجمالي الطلاب: ${result.details.totalStudents}\n- تم التحديث: ${result.details.updatedCount}\n- تم الإنشاء: ${result.details.createdCount}\n- الرسوم الجديدة: ${result.details.newTuitionFee} ج\n- الأخطاء: ${result.details.errorsCount}`);

      if (result.details.errors && result.details.errors.length > 0) {
        console.error('Errors during update:', result.details.errors);
      }
    } catch (error: any) {
      console.error('Error updating existing students:', error);
      alert('حدث خطأ أثناء تحديث رسوم الطلاب: ' + error.message);
    }
  };

  if (isLoading) {
    return (
      <Layout>
        <div className="flex justify-center items-center h-64">
          <div className="text-lg">جاري التحميل...</div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="p-6">
        {/* العنوان والتحكم */}
        <div className="mb-6 bg-gradient-to-l from-[#0ABB87]/10 to-transparent p-4 rounded-lg border-r-4 border-[#0ABB87]">
          <div className="flex justify-between items-center">
            <div className="flex items-center">
              <div className="w-12 h-12 rounded-full bg-[#0ABB87] flex items-center justify-center text-white ml-4">
                <FaMoneyBillWave size={24} />
              </div>
              <div>
                <h1 className="text-2xl font-bold" style={{color: '#0ABB87'}}>
                  إدارة الرسوم الدراسية
                </h1>
                <p className="text-gray-600 mt-1">
                  تحديد وإدارة الرسوم الدراسية لكل مرحلة وسنة دراسية
                </p>
              </div>
            </div>
            <button
              onClick={() => setShowAddForm(true)}
              className="bg-[#0ABB87] hover:bg-[#0ABB87]/90 text-white px-4 py-2 rounded-md flex items-center transition-colors duration-200"
            >
              <FaPlus className="ml-2" />
              إضافة رسوم مرحلة
            </button>
          </div>
        </div>

        {/* اختيار السنة الدراسية وأزرار التحكم */}
        <div className="mb-6 bg-white p-4 rounded-lg shadow-md">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
            <div>
              <label className="block text-gray-700 mb-2 font-semibold">السنة الدراسية:</label>
              <select
                value={selectedYear}
                onChange={(e) => setSelectedYear(e.target.value)}
                className="w-full md:w-auto p-2 border border-gray-300 rounded-md"
              >
                <option value="2024-2025">2024-2025</option>
                <option value="2025-2026">2025-2026</option>
                <option value="2026-2027">2026-2027</option>
              </select>
            </div>

            <div className="flex flex-col sm:flex-row gap-2">
              <button
                onClick={() => {
                  if (confirm('هل تريد تحديث رسوم جميع الطلاب الموجودين في جميع المراحل؟ هذا سيطبق الرسوم الحالية على جميع الطلاب المسجلين.')) {
                    gradeFees.forEach(fee => updateExistingStudents(fee.grade_id));
                  }
                }}
                className="bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-md text-sm transition-colors duration-200"
              >
                تحديث جميع الطلاب الموجودين
              </button>
            </div>
          </div>
        </div>

        {/* جدول الرسوم */}
        <div className="bg-white rounded-lg shadow-md overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    المرحلة الدراسية
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    الرسوم الدراسية
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    الإجراءات
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {gradeFees.map((fee) => (
                  <GradeFeeRow
                    key={fee.id}
                    fee={fee}
                    isEditing={editingFee === fee.id}
                    formData={formData}
                    setFormData={setFormData}
                    onSave={handleSave}
                    onEdit={startEdit}
                    onCancel={cancelEdit}
                    onApplyToStudents={applyFeesToStudents}
                  />
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* نموذج إضافة رسوم جديدة */}
        {showAddForm && (
          <AddGradeFeeForm
            grades={grades}
            formData={formData}
            setFormData={setFormData}
            onSave={handleSave}
            onCancel={cancelEdit}
          />
        )}
      </div>
    </Layout>
  );
}

// مكون صف الرسوم
function GradeFeeRow({ fee, isEditing, formData, setFormData, onSave, onEdit, onCancel, onApplyToStudents }: any) {
  if (isEditing) {
    return (
      <tr className="bg-blue-50">
        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
          {fee.grade_name}
        </td>
        <td className="px-6 py-4 whitespace-nowrap">
          <input
            type="number"
            value={formData.tuition_fee}
            onChange={(e) => setFormData({...formData, tuition_fee: parseFloat(e.target.value) || 0})}
            className="w-full p-2 border border-gray-300 rounded text-sm"
            placeholder="أدخل الرسوم الدراسية"
          />
        </td>
        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
          <div className="flex space-x-2">
            <button
              onClick={() => onSave(formData)}
              className="text-green-600 hover:text-green-900"
            >
              <FaSave />
            </button>
            <button
              onClick={onCancel}
              className="text-red-600 hover:text-red-900"
            >
              <FaTimes />
            </button>
          </div>
        </td>
      </tr>
    );
  }

  return (
    <tr>
      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
        {fee.grade_name}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm font-bold text-green-600">
        {fee.tuition_fee.toFixed(2)} ج
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
        <div className="flex space-x-2">
          <button
            onClick={() => onEdit(fee)}
            className="text-blue-600 hover:text-blue-900"
            title="تعديل"
          >
            <FaEdit />
          </button>
          <button
            onClick={() => onApplyToStudents(fee.grade_id)}
            className="text-green-600 hover:text-green-900"
            title="تطبيق على الطلاب الجدد"
          >
            <FaGraduationCap />
          </button>
        </div>
      </td>
    </tr>
  );
}

// مكون نموذج إضافة رسوم جديدة
function AddGradeFeeForm({ grades, formData, setFormData, onSave, onCancel }: any) {
  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
        <div className="mt-3">
          <h3 className="text-lg font-medium text-gray-900 mb-4">إضافة رسوم مرحلة جديدة</h3>

          <div className="grid grid-cols-1 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">المرحلة الدراسية</label>
              <select
                value={formData.grade_id}
                onChange={(e) => setFormData({...formData, grade_id: e.target.value})}
                className="w-full p-3 border border-gray-300 rounded-md"
                required
              >
                <option value="">اختر المرحلة</option>
                {grades.map((grade: Grade) => (
                  <option key={grade.id} value={grade.id}>
                    {grade.name}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">السنة الدراسية</label>
              <input
                type="text"
                value={formData.academic_year}
                onChange={(e) => setFormData({...formData, academic_year: e.target.value})}
                className="w-full p-3 border border-gray-300 rounded-md"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">الرسوم الدراسية (بالجنيه)</label>
              <input
                type="number"
                value={formData.tuition_fee}
                onChange={(e) => setFormData({...formData, tuition_fee: parseFloat(e.target.value) || 0})}
                className="w-full p-3 border border-gray-300 rounded-md text-lg"
                placeholder="أدخل مبلغ الرسوم الدراسية"
                required
              />
            </div>
          </div>

          <div className="mt-6 flex justify-end space-x-3">
            <button
              onClick={onCancel}
              className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400"
            >
              إلغاء
            </button>
            <button
              onClick={() => onSave(formData)}
              className="px-4 py-2 bg-[#0ABB87] text-white rounded-md hover:bg-[#0ABB87]/90"
            >
              حفظ
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
