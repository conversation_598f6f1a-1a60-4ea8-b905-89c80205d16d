import React, { useMemo } from 'react';
import { ColumnDef } from '@tanstack/react-table';
import { DataTable } from '../table/DataTable';
import { ActionButtons } from '../table/ActionButtons';
import { Grade } from '@/types/table.types';

interface GradeTableProps {
  data: Grade[];
  isLoading?: boolean;
  onView?: (grade: Grade) => void;
  onEdit?: (grade: Grade) => void;
  onDelete?: (grade: Grade) => void;
}

export const GradeTable: React.FC<GradeTableProps> = ({
  data,
  isLoading = false,
  onView,
  onEdit,
  onDelete,
}) => {
  // تعريف أعمدة الجدول - استخدام مصفوفة تبعيات ثابتة
  const columns = useMemo<ColumnDef<Grade, any>[]>(
    () => [
      {
        id: 'name',
        header: 'اسم المرحلة',
        accessorKey: 'name',
        cell: ({ row }) => (
          <div className="font-medium text-gray-900">{row.original.name}</div>
        ),
        meta: {
          color: '#21ADE7', // أزرق فاتح
        },
      },
      {
        id: 'level',
        header: 'المستوى',
        accessorKey: 'level',
        meta: {
          color: '#5578EB', // أزرق داكن
        },
      },
      {
        id: 'actions',
        header: 'الإجراءات',
        cell: ({ row }) => (
          <ActionButtons
            onView={onView ? () => onView(row.original) : undefined}
            onEdit={onEdit ? () => onEdit(row.original) : undefined}
            onDelete={onDelete ? () => onDelete(row.original) : undefined}
            itemName={`المرحلة "${row.original.name}"`}
          />
        ),
        meta: {
          color: '#21ADE7', // أزرق فاتح
        },
      },
    ],
    // استخدام مصفوفة تبعيات ثابتة لتجنب مشاكل إعادة التصيير
    // eslint-disable-next-line react-hooks/exhaustive-deps
    []
  );

  return (
    <DataTable
      columns={columns}
      data={data}
      isLoading={isLoading}
      showSearch={true}
      searchPlaceholder="بحث..."
      emptyMessage="لا يوجد مراحل دراسية"
      className="border-r-4 border-[#0ABB87]"
    />
  );
};

export default GradeTable;
