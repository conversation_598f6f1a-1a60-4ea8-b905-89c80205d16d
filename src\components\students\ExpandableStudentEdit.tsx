"use client";

import React, { useState, useEffect } from 'react';
import { supabase } from '@/lib/supabase';
import { Student, Guardian, FinancialInfo, Installment, OtherPayment, AdditionalInfo } from '@/types/table.types';
import { FaArrowRight, FaSave, FaPlus, FaEdit, FaTrash, FaCalendarAlt, FaMoneyBillWave, FaSync, FaGraduationCap, FaStickyNote } from 'react-icons/fa';
import { useRealtimeInstallments } from '@/hooks/useRealtimeInstallments';

interface ExpandableStudentEditProps {
  student: Student;
  onCancel: () => void;
  onSave: () => void;
}

const ExpandableStudentEdit: React.FC<ExpandableStudentEditProps> = ({
  student,
  onCancel,
  onSave,
}) => {
  // حالة البيانات الأساسية للطالب
  const [basicInfo, setBasicInfo] = useState<Partial<Student>>({
    full_name: student.full_name,
    id_number: student.id_number,
    birth_date: student.birth_date,
    gender: student.gender,
    phone: student.phone || '',
    email: student.email || '',
  });

  // حالة بيانات ولي الأمر
  const [guardians, setGuardians] = useState<Partial<Guardian>[]>([]);

  // حالة البيانات المالية
  const [financialInfo, setFinancialInfo] = useState<Partial<FinancialInfo>>({});

  // حالة الأقساط - تم استبدالها بـ useRealtimeInstallments hook

  // حالة المدفوعات الأخرى
  const [otherPayment, setOtherPayment] = useState<Partial<OtherPayment>>({});

  // حالة المعلومات الأكاديمية
  const [academicRecord, setAcademicRecord] = useState<Partial<any>>({});

  // حالة المعلومات الإضافية
  const [additionalInfo, setAdditionalInfo] = useState<Partial<AdditionalInfo>>({});

  // حالة المراحل الدراسية والفصول
  const [grades, setGrades] = useState<{ id: number; name: string; level: string }[]>([]);
  const [classes, setClasses] = useState<{ id: number; name: string; grade_id: number }[]>([]);

  // حالة التحميل والأخطاء
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // حالة عرض قسم البيانات المالية المفصلة
  const [showDetailedFinancial, setShowDetailedFinancial] = useState(false);

  // استخدام hook الأقساط مع التزامن الفوري
  const {
    installments,
    updateInstallment,
    addInstallment,
    deleteInstallment,
    calculateTotals,
    isLoading: isLoadingInstallments,
    syncInstallments
  } = useRealtimeInstallments({
    financialInfoId: financialInfo.id as number || null,
    tuitionFee: parseFloat(financialInfo.tuition_fee?.toString() || '0'),
    discountAmount: parseFloat(financialInfo.discount_amount?.toString() || '0'),
    installmentsCount: parseInt(financialInfo.installments_count?.toString() || '0')
  });

  // جلب بيانات ولي الأمر والبيانات المالية والأكاديمية عند تحميل المكون
  useEffect(() => {
    const fetchStudentData = async () => {
      setIsLoading(true);
      try {
        // جلب المراحل الدراسية
        const { data: gradesData, error: gradesError } = await supabase
          .from('school_grades')
          .select('id, name, level')
          .order('id', { ascending: true });

        if (gradesError) throw gradesError;

        if (gradesData) {
          setGrades(gradesData);
        }

        // جلب الفصول الدراسية
        const { data: classesData, error: classesError } = await supabase
          .from('school_classes')
          .select('id, name, grade_id')
          .order('id', { ascending: true });

        if (classesError) throw classesError;

        if (classesData) {
          setClasses(classesData);
        }

        // جلب السجل الأكاديمي للطالب
        const { data: academicData, error: academicError } = await supabase
          .from('academic_records')
          .select('*')
          .eq('student_id', student.id)
          .order('created_at', { ascending: false })
          .limit(1)
          .single();

        if (academicError && academicError.code !== 'PGRST116') {
          throw academicError;
        }

        if (academicData) {
          setAcademicRecord(academicData);
        } else {
          // إذا لم يكن هناك سجل أكاديمي، أضف نموذج فارغ
          setAcademicRecord({
            student_id: student.id,
            grade_id: null,
            class_id: null,
            academic_year: new Date().getFullYear().toString(),
            registration_date: new Date().toISOString().split('T')[0]
          });
        }

        // جلب بيانات أولياء الأمور
        const { data: guardiansData, error: guardiansError } = await supabase
          .from('guardians')
          .select('*')
          .eq('student_id', student.id);

        if (guardiansError) throw guardiansError;

        // إذا لم يكن هناك أولياء أمور، أضف نموذج فارغ
        if (guardiansData && guardiansData.length > 0) {
          setGuardians(guardiansData);
        } else {
          setGuardians([{
            student_id: student.id,
            full_name: '',
            relationship: '',
            id_number: '',
            phone: '',
            email: '',
            occupation: '',
            workplace: '',
          }]);
        }

        // جلب البيانات المالية
        const { data: financialData, error: financialError } = await supabase
          .from('financial_info')
          .select('*')
          .eq('student_id', student.id)
          .single();

        if (financialError && financialError.code !== 'PGRST116') {
          // PGRST116 هو خطأ "لم يتم العثور على نتائج"
          throw financialError;
        }

        if (financialData) {
          setFinancialInfo(financialData);
          // جلب بيانات الأقساط تتم الآن من خلال useRealtimeInstallments hook
        } else {
          // إذا لم تكن هناك معلومات مالية، أضف نموذج فارغ
          setFinancialInfo({
            student_id: student.id,
            tuition_fee: 0,
            discount_amount: 0,
            discount_reason: '',
            paid_amount: 0,
            payment_method: 'cash',
            installments_count: 0,
          });
        }

        // جلب المدفوعات الأخرى
        const { data: otherPaymentData, error: otherPaymentError } = await supabase
          .from('other_payments')
          .select('*')
          .eq('student_id', student.id)
          .single();

        if (otherPaymentError && otherPaymentError.code !== 'PGRST116') {
          throw otherPaymentError;
        }

        if (otherPaymentData) {
          setOtherPayment(otherPaymentData);
        } else {
          // إذا لم تكن هناك مدفوعات أخرى، أضف نموذج فارغ
          setOtherPayment({
            student_id: student.id,
            file_opening_fee: 0,
            books_fee: 0,
            uniform_count: 0,
            uniform_total: 0,
            transportation_fee: 0,
            total_amount: 0,
          });
        }

        // جلب المعلومات الإضافية
        const { data: additionalInfoData, error: additionalInfoError } = await supabase
          .from('additional_info')
          .select('*')
          .eq('student_id', student.id)
          .single();

        if (additionalInfoError && additionalInfoError.code !== 'PGRST116') {
          throw additionalInfoError;
        }

        if (additionalInfoData) {
          setAdditionalInfo(additionalInfoData);
        } else {
          // إذا لم تكن هناك معلومات إضافية، أضف نموذج فارغ
          setAdditionalInfo({
            student_id: student.id,
            hobbies: '',
            achievements: '',
            special_needs: '',
            transportation_method: '',
            additional_notes: '',
          });
        }
      } catch (error) {
        console.error('Error fetching student data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchStudentData();
  }, [student.id]);

  // تحديث البيانات الأساسية
  const handleBasicInfoChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setBasicInfo(prev => ({ ...prev, [name]: value }));
  };

  // تحديث بيانات ولي الأمر
  const handleGuardianChange = (index: number, field: string, value: string) => {
    const updatedGuardians = [...guardians];
    updatedGuardians[index] = { ...updatedGuardians[index], [field]: value };
    setGuardians(updatedGuardians);
  };

  // إضافة ولي أمر جديد
  const addGuardian = () => {
    setGuardians([...guardians, {
      student_id: student.id,
      full_name: '',
      relationship: '',
      id_number: '',
      phone: '',
      email: '',
      occupation: '',
      workplace: '',
    }]);
  };

  // حذف ولي أمر
  const removeGuardian = (index: number) => {
    if (guardians.length > 1) {
      const updatedGuardians = [...guardians];
      updatedGuardians.splice(index, 1);
      setGuardians(updatedGuardians);
    }
  };

  // تحديث البيانات المالية
  const handleFinancialInfoChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFinancialInfo(prev => ({ ...prev, [name]: value }));

    // لا نقوم بتعديل المبلغ المدفوع تلقائيًا عند تغيير الرسوم أو الخصم
    // المبلغ المدفوع يجب أن يكون قيمة مستقلة يدخلها المستخدم
    if (name === 'tuition_fee' || name === 'discount_amount') {
      // نقوم فقط بتحديث القيمة المدخلة
      setFinancialInfo(prev => ({
        ...prev,
        [name]: value
      }));

      // تحديث الأقساط تلقائيًا يتم الآن من خلال useRealtimeInstallments hook
      // سيتم حساب الأقساط بناءً على (الرسوم - الخصم - المبلغ المدفوع مسبقًا)
    }

    // تحديث الأقساط تلقائيًا عند تغيير عدد الأقساط أو طريقة الدفع
    if (name === 'installments_count' || name === 'payment_method') {
      // إذا كانت طريقة الدفع هي "أقساط" وتم تغييرها للتو، نقوم بتعيين عدد الأقساط إلى 3 افتراضيًا إذا كان صفرًا
      if (name === 'payment_method' && value === 'installments') {
        const currentInstallmentsCount = parseInt(financialInfo.installments_count?.toString() || '0');
        if (currentInstallmentsCount === 0) {
          // تعيين عدد الأقساط إلى 3 افتراضيًا
          setFinancialInfo(prev => ({ ...prev, installments_count: 3 }));

          // نعرض قسم البيانات المالية المفصلة تلقائيًا عند اختيار طريقة الدفع "أقساط"
          setShowDetailedFinancial(true);
          return; // نخرج من الدالة لتجنب تنفيذ الكود أدناه مرتين
        }
      }

      // نعرض قسم البيانات المالية المفصلة تلقائيًا عند تغيير عدد الأقساط
      if (name === 'installments_count' && !showDetailedFinancial) {
        setShowDetailedFinancial(true);
      }
    }
  };

  // دالة مساعدة لإنشاء الأقساط - تم استبدالها بـ useRealtimeInstallments hook

  // تحديث المدفوعات الأخرى
  const handleOtherPaymentChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    const numValue = parseFloat(value) || 0;

    setOtherPayment(prev => ({ ...prev, [name]: numValue }));

    // حساب إجمالي المدفوعات الأخرى تلقائيًا
    if (['file_opening_fee', 'books_fee', 'uniform_total', 'transportation_fee'].includes(name)) {
      const fileOpeningFee = name === 'file_opening_fee' ? numValue : parseFloat(otherPayment.file_opening_fee?.toString() || '0');
      const booksFee = name === 'books_fee' ? numValue : parseFloat(otherPayment.books_fee?.toString() || '0');
      const uniformTotal = name === 'uniform_total' ? numValue : parseFloat(otherPayment.uniform_total?.toString() || '0');
      const transportationFee = name === 'transportation_fee' ? numValue : parseFloat(otherPayment.transportation_fee?.toString() || '0');

      const totalAmount = fileOpeningFee + booksFee + uniformTotal + transportationFee;

      setOtherPayment(prev => ({
        ...prev,
        [name]: numValue,
        total_amount: totalAmount
      }));
    }

    // حساب إجمالي الزي المدرسي تلقائيًا عند تغيير عدد القطع
    if (name === 'uniform_count') {
      const uniformCount = parseInt(value) || 0;
      const uniformTotal = uniformCount * 300; // سعر القطعة الواحدة 300

      setOtherPayment(prev => ({
        ...prev,
        uniform_count: uniformCount,
        uniform_total: uniformTotal,
        total_amount: parseFloat(prev.file_opening_fee?.toString() || '0') +
                     parseFloat(prev.books_fee?.toString() || '0') +
                     uniformTotal +
                     parseFloat(prev.transportation_fee?.toString() || '0')
      }));
    }
  };

  // إضافة قسط جديد - استخدام hook الأقساط
  const handleAddInstallment = () => {
    addInstallment();
  };

  // تحديث بيانات القسط - استخدام hook الأقساط
  const handleInstallmentChange = (index: number, field: string, value: string | number) => {
    if (!installments || installments.length === 0 || !installments[index]) return;

    const installmentId = installments[index].id;
    if (!installmentId) return;

    // تحديث القسط في قاعدة البيانات
    updateInstallment(installmentId, { [field]: value });
  };

  // حذف قسط - استخدام hook الأقساط
  const handleDeleteInstallment = (index: number) => {
    if (!installments || installments.length === 0 || !installments[index]) return;

    const installmentId = installments[index].id;
    if (!installmentId) return;

    deleteInstallment(installmentId);
  };

  // تحديث المعلومات الأكاديمية
  const handleAcademicRecordChange = async (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setAcademicRecord(prev => ({ ...prev, [name]: value }));

    // إذا تم تغيير المرحلة الدراسية، نقوم بإعادة تعيين الفصل وجلب الرسوم الدراسية
    if (name === 'grade_id') {
      setAcademicRecord(prev => ({ ...prev, class_id: null }));

      // جلب الرسوم الدراسية من قاعدة البيانات بناءً على المرحلة الدراسية
      if (value) {
        try {
          const response = await fetch(`/api/grade-fees/get?gradeId=${value}&academicYear=2024-2025`);
          const data = await response.json();

          if (response.ok) {
            setFinancialInfo(prev => ({ ...prev, tuition_fee: data.tuition_fee }));

            // عرض رسالة إذا كانت الرسوم افتراضية
            if (data.message) {
              console.log(data.message);
            }
          } else {
            console.error('خطأ في جلب رسوم المرحلة:', data.error);
          }
        } catch (error) {
          console.error('خطأ في الاتصال بالخادم:', error);
        }
      }
    }
  };

  // تحديث المعلومات الإضافية
  const handleAdditionalInfoChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setAdditionalInfo(prev => ({ ...prev, [name]: value }));
  };

  // التحقق من صحة البيانات
  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    // التحقق من البيانات الأساسية
    if (!basicInfo.full_name) newErrors.full_name = 'الاسم الكامل مطلوب';
    if (!basicInfo.id_number) newErrors.id_number = 'رقم الهوية مطلوب';
    if (!basicInfo.birth_date) newErrors.birth_date = 'تاريخ الميلاد مطلوب';
    if (!basicInfo.gender) newErrors.gender = 'الجنس مطلوب';

    // التحقق من بيانات ولي الأمر الأول على الأقل
    if (guardians.length > 0) {
      if (!guardians[0].full_name) newErrors.guardian_full_name = 'اسم ولي الأمر مطلوب';
      if (!guardians[0].relationship) newErrors.guardian_relationship = 'العلاقة بالطالب مطلوبة';
      if (!guardians[0].id_number) newErrors.guardian_id_number = 'رقم هوية ولي الأمر مطلوب';
      if (!guardians[0].phone) newErrors.guardian_phone = 'رقم هاتف ولي الأمر مطلوب';
    }

    // التحقق من البيانات المالية
    if (financialInfo.tuition_fee === undefined || financialInfo.tuition_fee < 0) {
      newErrors.tuition_fee = 'الرسوم الدراسية يجب أن تكون قيمة موجبة';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // حفظ جميع البيانات
  const handleSaveAll = async () => {
    if (!validateForm()) return;

    setIsLoading(true);
    try {
      // 1. تحديث البيانات الأساسية للطالب
      console.log("بدء حفظ البيانات الأساسية للطالب...");
      const { error: studentError } = await supabase
        .from('students')
        .update({
          full_name: basicInfo.full_name,
          id_number: basicInfo.id_number,
          birth_date: basicInfo.birth_date,
          gender: basicInfo.gender,
          phone: basicInfo.phone || null,
          email: basicInfo.email || null,
          updated_at: new Date().toISOString(),
        })
        .eq('id', student.id);

      if (studentError) {
        console.error("خطأ في تحديث بيانات الطالب:", studentError);
        throw studentError;
      }

      console.log("تم حفظ البيانات الأساسية للطالب بنجاح");

      // 2. تحديث بيانات أولياء الأمور
      console.log("بدء حفظ بيانات أولياء الأمور...");
      try {
        for (const guardian of guardians) {
          if (guardian.id) {
            // تحديث ولي أمر موجود
            console.log(`تحديث بيانات ولي الأمر رقم ${guardian.id}...`);
            const { error: guardianUpdateError } = await supabase
              .from('guardians')
              .update({
                full_name: guardian.full_name,
                relationship: guardian.relationship,
                id_number: guardian.id_number,
                phone: guardian.phone,
                email: guardian.email || null,
                occupation: guardian.occupation || null,
                workplace: guardian.workplace || null,
              })
              .eq('id', guardian.id);

            if (guardianUpdateError) {
              console.error(`خطأ في تحديث بيانات ولي الأمر رقم ${guardian.id}:`, guardianUpdateError);
              throw guardianUpdateError;
            }
          } else {
            // إضافة ولي أمر جديد
            console.log("إضافة ولي أمر جديد...");
            const { error: guardianInsertError } = await supabase
              .from('guardians')
              .insert({
                student_id: student.id,
                full_name: guardian.full_name,
                relationship: guardian.relationship,
                id_number: guardian.id_number,
                phone: guardian.phone,
                email: guardian.email || null,
                occupation: guardian.occupation || null,
                workplace: guardian.workplace || null,
              });

            if (guardianInsertError) {
              console.error("خطأ في إضافة ولي أمر جديد:", guardianInsertError);
              throw guardianInsertError;
            }
          }
        }
        console.log("تم حفظ بيانات أولياء الأمور بنجاح");
      } catch (guardianError) {
        console.error("خطأ في حفظ بيانات أولياء الأمور:", guardianError);
        throw guardianError;
      }

      // 3. تحديث البيانات المالية
      console.log("بدء حفظ البيانات المالية...");
      let financialInfoId = financialInfo.id;

      try {
        // تحويل القيم إلى أرقام
        const tuitionFee = parseFloat(financialInfo.tuition_fee?.toString() || '0');
        const discountAmount = parseFloat(financialInfo.discount_amount?.toString() || '0');
        const paidAmount = parseFloat(financialInfo.paid_amount?.toString() || '0');
        const installmentsCount = parseInt(financialInfo.installments_count?.toString() || '0');

        console.log("القيم المالية المحولة:", {
          tuitionFee,
          discountAmount,
          paidAmount,
          installmentsCount
        });

        if (financialInfo.id) {
          // تحديث معلومات مالية موجودة
          console.log(`تحديث البيانات المالية للطالب رقم ${student.id}...`);
          const { error: financialUpdateError } = await supabase
            .from('financial_info')
            .update({
              tuition_fee: tuitionFee,
              discount_amount: discountAmount,
              discount_reason: financialInfo.discount_reason || null,
              paid_amount: paidAmount,
              payment_method: financialInfo.payment_method,
              installments_count: installmentsCount
            })
            .eq('id', financialInfo.id);

          if (financialUpdateError) {
            console.error("خطأ في تحديث البيانات المالية:", financialUpdateError);
            throw financialUpdateError;
          }

          console.log("تم تحديث البيانات المالية بنجاح");
        } else {
          // إضافة معلومات مالية جديدة
          console.log(`إضافة بيانات مالية جديدة للطالب رقم ${student.id}...`);

          // التحقق من وجود بيانات مالية سابقة للطالب
          const { data: existingFinancialInfo, error: checkError } = await supabase
            .from('financial_info')
            .select('id')
            .eq('student_id', student.id)
            .maybeSingle();

          if (checkError) {
            console.error("خطأ في التحقق من وجود بيانات مالية سابقة:", checkError);
          }

          if (existingFinancialInfo?.id) {
            console.log(`تم العثور على بيانات مالية سابقة برقم ${existingFinancialInfo.id}، سيتم تحديثها...`);

            const { error: updateExistingError } = await supabase
              .from('financial_info')
              .update({
                tuition_fee: tuitionFee,
                discount_amount: discountAmount,
                discount_reason: financialInfo.discount_reason || null,
                paid_amount: paidAmount,
                payment_method: financialInfo.payment_method,
                installments_count: installmentsCount
              })
              .eq('id', existingFinancialInfo.id);

            if (updateExistingError) {
              console.error("خطأ في تحديث البيانات المالية الموجودة:", updateExistingError);
              throw updateExistingError;
            }

            financialInfoId = existingFinancialInfo.id;
            console.log("تم تحديث البيانات المالية الموجودة بنجاح");
          } else {
            // إضافة بيانات مالية جديدة
            const { data: newFinancialInfo, error: financialInsertError } = await supabase
              .from('financial_info')
              .insert({
                student_id: student.id,
                tuition_fee: tuitionFee,
                discount_amount: discountAmount,
                discount_reason: financialInfo.discount_reason || null,
                paid_amount: paidAmount,
                payment_method: financialInfo.payment_method,
                installments_count: installmentsCount,
                created_at: new Date().toISOString()
              })
              .select()
              .single();

            if (financialInsertError) {
              console.error("خطأ في إضافة بيانات مالية جديدة:", financialInsertError);
              throw financialInsertError;
            }

            if (newFinancialInfo) {
              financialInfoId = newFinancialInfo.id;
              console.log(`تم إضافة بيانات مالية جديدة برقم ${financialInfoId}`);
            } else {
              console.log("تم إضافة البيانات المالية ولكن لم يتم استرجاع المعرف");
            }
          }
        }
      } catch (financialError) {
        console.error("خطأ في حفظ البيانات المالية:", financialError);
        throw financialError;
      }

      // 4. تحديث المدفوعات الأخرى
      console.log("بدء حفظ المدفوعات الأخرى...");
      try {
        // تحويل القيم إلى أرقام
        const fileOpeningFee = parseFloat(otherPayment.file_opening_fee?.toString() || '0');
        const booksFee = parseFloat(otherPayment.books_fee?.toString() || '0');
        const uniformCount = parseInt(otherPayment.uniform_count?.toString() || '0');
        const uniformTotal = parseFloat(otherPayment.uniform_total?.toString() || '0');
        const transportationFee = parseFloat(otherPayment.transportation_fee?.toString() || '0');
        const totalAmount = parseFloat(otherPayment.total_amount?.toString() || '0');

        console.log("قيم المدفوعات الأخرى المحولة:", {
          fileOpeningFee,
          booksFee,
          uniformCount,
          uniformTotal,
          transportationFee,
          totalAmount
        });

        if (otherPayment.id) {
          // تحديث مدفوعات أخرى موجودة
          console.log(`تحديث المدفوعات الأخرى للطالب رقم ${student.id}...`);
          const { error: otherPaymentUpdateError } = await supabase
            .from('other_payments')
            .update({
              file_opening_fee: fileOpeningFee,
              books_fee: booksFee,
              uniform_count: uniformCount,
              uniform_total: uniformTotal,
              transportation_fee: transportationFee,
              total_amount: totalAmount,
            })
            .eq('id', otherPayment.id);

          if (otherPaymentUpdateError) {
            console.error("خطأ في تحديث المدفوعات الأخرى:", otherPaymentUpdateError);
            throw otherPaymentUpdateError;
          }

          console.log("تم تحديث المدفوعات الأخرى بنجاح");
        } else {
          // التحقق من وجود مدفوعات أخرى سابقة للطالب
          console.log(`التحقق من وجود مدفوعات أخرى سابقة للطالب رقم ${student.id}...`);
          const { data: existingOtherPayment, error: checkError } = await supabase
            .from('other_payments')
            .select('id')
            .eq('student_id', student.id)
            .maybeSingle();

          if (checkError) {
            console.error("خطأ في التحقق من وجود مدفوعات أخرى سابقة:", checkError);
          }

          if (existingOtherPayment?.id) {
            console.log(`تم العثور على مدفوعات أخرى سابقة برقم ${existingOtherPayment.id}، سيتم تحديثها...`);

            const { error: updateExistingError } = await supabase
              .from('other_payments')
              .update({
                file_opening_fee: fileOpeningFee,
                books_fee: booksFee,
                uniform_count: uniformCount,
                uniform_total: uniformTotal,
                transportation_fee: transportationFee,
                total_amount: totalAmount,
              })
              .eq('id', existingOtherPayment.id);

            if (updateExistingError) {
              console.error("خطأ في تحديث المدفوعات الأخرى الموجودة:", updateExistingError);
              throw updateExistingError;
            }

            console.log("تم تحديث المدفوعات الأخرى الموجودة بنجاح");
          } else {
            // إضافة مدفوعات أخرى جديدة
            console.log(`إضافة مدفوعات أخرى جديدة للطالب رقم ${student.id}...`);
            const { error: otherPaymentInsertError } = await supabase
              .from('other_payments')
              .insert({
                student_id: student.id,
                file_opening_fee: fileOpeningFee,
                books_fee: booksFee,
                uniform_count: uniformCount,
                uniform_total: uniformTotal,
                transportation_fee: transportationFee,
                total_amount: totalAmount,
                created_at: new Date().toISOString(),
              });

            if (otherPaymentInsertError) {
              console.error("خطأ في إضافة مدفوعات أخرى جديدة:", otherPaymentInsertError);
              throw otherPaymentInsertError;
            }

            console.log("تم إضافة المدفوعات الأخرى بنجاح");
          }
        }
      } catch (otherPaymentError) {
        console.error("خطأ في حفظ المدفوعات الأخرى:", otherPaymentError);
        // لا نريد إيقاف العملية إذا فشلت المدفوعات الأخرى
        console.log("تم تجاوز خطأ المدفوعات الأخرى والاستمرار في العملية");
      }

      // 5. تحديث الأقساط
      console.log("بدء تزامن الأقساط...");
      try {
        if (financialInfoId) {
          // تزامن الأقساط مع البيانات المالية المحدثة
          syncInstallments();
          console.log("تم تزامن الأقساط بنجاح");
        } else {
          console.log("لا يمكن تزامن الأقساط: معرف البيانات المالية غير موجود");
        }
      } catch (installmentsError) {
        console.error("خطأ في تزامن الأقساط:", installmentsError);
        // لا نريد إيقاف العملية إذا فشل تزامن الأقساط
        console.log("تم تجاوز خطأ تزامن الأقساط والاستمرار في العملية");
      }

      // 6. تحديث المعلومات الأكاديمية
      console.log("بدء حفظ المعلومات الأكاديمية...");
      try {
        if (academicRecord.grade_id) {
          if (academicRecord.id) {
            // تحديث سجل أكاديمي موجود
            console.log(`تحديث السجل الأكاديمي رقم ${academicRecord.id}...`);
            const { error: academicUpdateError } = await supabase
              .from('academic_records')
              .update({
                grade_id: parseInt(academicRecord.grade_id.toString()),
                class_id: academicRecord.class_id ? parseInt(academicRecord.class_id.toString()) : null,
                academic_year: academicRecord.academic_year,
                registration_date: academicRecord.registration_date,
              })
              .eq('id', academicRecord.id);

            if (academicUpdateError) {
              console.error("خطأ في تحديث السجل الأكاديمي:", academicUpdateError);
              throw academicUpdateError;
            }

            console.log("تم تحديث السجل الأكاديمي بنجاح");
          } else {
            // إضافة سجل أكاديمي جديد
            console.log("إضافة سجل أكاديمي جديد...");
            const { error: academicInsertError } = await supabase
              .from('academic_records')
              .insert({
                student_id: student.id,
                grade_id: parseInt(academicRecord.grade_id.toString()),
                class_id: academicRecord.class_id ? parseInt(academicRecord.class_id.toString()) : null,
                academic_year: academicRecord.academic_year,
                registration_date: academicRecord.registration_date,
                created_at: new Date().toISOString(),
              });

            if (academicInsertError) {
              console.error("خطأ في إضافة سجل أكاديمي جديد:", academicInsertError);
              throw academicInsertError;
            }

            console.log("تم إضافة السجل الأكاديمي بنجاح");
          }
        } else {
          console.log("لا توجد معلومات أكاديمية للحفظ");
        }
      } catch (academicError) {
        console.error("خطأ في حفظ المعلومات الأكاديمية:", academicError);
        // لا نريد إيقاف العملية إذا فشلت المعلومات الأكاديمية
        console.log("تم تجاوز خطأ المعلومات الأكاديمية والاستمرار في العملية");
      }

      // 7. تحديث المعلومات الإضافية
      console.log("بدء حفظ المعلومات الإضافية...");
      try {
        if (additionalInfo.id) {
          // تحديث معلومات إضافية موجودة
          console.log(`تحديث المعلومات الإضافية رقم ${additionalInfo.id}...`);
          const { error: additionalInfoUpdateError } = await supabase
            .from('additional_info')
            .update({
              hobbies: additionalInfo.hobbies || null,
              achievements: additionalInfo.achievements || null,
              special_needs: additionalInfo.special_needs || null,
              transportation_method: additionalInfo.transportation_method || null,
              additional_notes: additionalInfo.additional_notes || null,
            })
            .eq('id', additionalInfo.id);

          if (additionalInfoUpdateError) {
            console.error("خطأ في تحديث المعلومات الإضافية:", additionalInfoUpdateError);
            throw additionalInfoUpdateError;
          }

          console.log("تم تحديث المعلومات الإضافية بنجاح");
        } else {
          // التحقق من وجود معلومات إضافية سابقة للطالب
          console.log(`التحقق من وجود معلومات إضافية سابقة للطالب رقم ${student.id}...`);
          const { data: existingAdditionalInfo, error: checkError } = await supabase
            .from('additional_info')
            .select('id')
            .eq('student_id', student.id)
            .maybeSingle();

          if (checkError) {
            console.error("خطأ في التحقق من وجود معلومات إضافية سابقة:", checkError);
          }

          if (existingAdditionalInfo?.id) {
            console.log(`تم العثور على معلومات إضافية سابقة برقم ${existingAdditionalInfo.id}، سيتم تحديثها...`);

            const { error: updateExistingError } = await supabase
              .from('additional_info')
              .update({
                hobbies: additionalInfo.hobbies || null,
                achievements: additionalInfo.achievements || null,
                special_needs: additionalInfo.special_needs || null,
                transportation_method: additionalInfo.transportation_method || null,
                additional_notes: additionalInfo.additional_notes || null,
              })
              .eq('id', existingAdditionalInfo.id);

            if (updateExistingError) {
              console.error("خطأ في تحديث المعلومات الإضافية الموجودة:", updateExistingError);
              throw updateExistingError;
            }

            console.log("تم تحديث المعلومات الإضافية الموجودة بنجاح");
          } else {
            // إضافة معلومات إضافية جديدة
            console.log(`إضافة معلومات إضافية جديدة للطالب رقم ${student.id}...`);
            const { error: additionalInfoInsertError } = await supabase
              .from('additional_info')
              .insert({
                student_id: student.id,
                hobbies: additionalInfo.hobbies || null,
                achievements: additionalInfo.achievements || null,
                special_needs: additionalInfo.special_needs || null,
                transportation_method: additionalInfo.transportation_method || null,
                additional_notes: additionalInfo.additional_notes || null,
                created_at: new Date().toISOString(),
              });

            if (additionalInfoInsertError) {
              console.error("خطأ في إضافة معلومات إضافية جديدة:", additionalInfoInsertError);
              throw additionalInfoInsertError;
            }

            console.log("تم إضافة المعلومات الإضافية بنجاح");
          }
        }
      } catch (additionalInfoError) {
        console.error("خطأ في حفظ المعلومات الإضافية:", additionalInfoError);
        // لا نريد إيقاف العملية إذا فشلت المعلومات الإضافية
        console.log("تم تجاوز خطأ المعلومات الإضافية والاستمرار في العملية");
      }

      // استدعاء دالة التحديث
      console.log("تم الانتهاء من حفظ جميع البيانات بنجاح، استدعاء دالة التحديث...");
      onSave();

      // عرض رسالة نجاح
      alert("تم حفظ بيانات الطالب بنجاح");
    } catch (error) {
      console.error('Error saving student data:', error);

      // تحليل الخطأ وعرض رسالة مناسبة
      let errorMessage = "حدث خطأ أثناء حفظ البيانات";

      if (error && typeof error === 'object') {
        if ('code' in error) {
          switch (error.code) {
            case '23505':
              errorMessage = "هناك تعارض في البيانات. قد يكون رقم الهوية مستخدم بالفعل.";
              break;
            case '23503':
              errorMessage = "خطأ في العلاقات بين الجداول. تأكد من صحة البيانات المرتبطة.";
              break;
            case '42P01':
              errorMessage = "خطأ في اسم الجدول. يرجى التواصل مع مسؤول النظام.";
              break;
            case '42703':
              errorMessage = "خطأ في اسم العمود. يرجى التواصل مع مسؤول النظام.";
              break;
            default:
              errorMessage = `حدث خطأ: ${JSON.stringify(error)}`;
          }
        } else if ('message' in error) {
          errorMessage = `حدث خطأ: ${error.message}`;
        } else {
          try {
            errorMessage = `حدث خطأ: ${JSON.stringify(error)}`;
          } catch (e) {
            errorMessage = "حدث خطأ غير معروف";
          }
        }
      }

      // عرض رسالة الخطأ
      alert(errorMessage);
    } finally {
      console.log("انتهت عملية الحفظ");
      setIsLoading(false);
    }
  };

  return (
      <div className="p-4">
        <form>
          {/* تقسيم الصفحة إلى أعمدة متساوية */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* البيانات الأساسية - العمود الأول */}
            <div className="bg-white p-4 rounded-lg shadow-sm border-r-4 border-[#21ADE7] h-full">
              <h3 className="text-lg font-semibold mb-4 text-[#21ADE7]">البيانات الأساسية</h3>
            <div className="grid grid-cols-1 gap-4">
              <div>
                <label className="block text-gray-700 mb-2">الاسم الكامل <span className="text-red-500">*</span></label>
                <input
                  className={`w-full p-2 border ${errors.full_name ? 'border-red-500' : 'border-gray-300'} rounded-md focus:ring-[#21ADE7] focus:border-[#21ADE7]`}
                  type="text"
                  name="full_name"
                  value={basicInfo.full_name || ''}
                  onChange={handleBasicInfoChange}
                  required
                />
                {errors.full_name && <p className="text-red-500 text-sm mt-1">{errors.full_name}</p>}
              </div>
              <div>
                <label className="block text-gray-700 mb-2">رقم الهوية <span className="text-red-500">*</span></label>
                <input
                  className={`w-full p-2 border ${errors.id_number ? 'border-red-500' : 'border-gray-300'} rounded-md focus:ring-[#21ADE7] focus:border-[#21ADE7]`}
                  type="text"
                  name="id_number"
                  value={basicInfo.id_number || ''}
                  onChange={handleBasicInfoChange}
                  required
                />
                {errors.id_number && <p className="text-red-500 text-sm mt-1">{errors.id_number}</p>}
              </div>
              <div>
                <label className="block text-gray-700 mb-2">تاريخ الميلاد <span className="text-red-500">*</span></label>
                <input
                  className={`w-full p-2 border ${errors.birth_date ? 'border-red-500' : 'border-gray-300'} rounded-md focus:ring-[#21ADE7] focus:border-[#21ADE7]`}
                  type="date"
                  name="birth_date"
                  value={basicInfo.birth_date || ''}
                  onChange={handleBasicInfoChange}
                  required
                />
                {errors.birth_date && <p className="text-red-500 text-sm mt-1">{errors.birth_date}</p>}
              </div>
              <div>
                <label className="block text-gray-700 mb-2">الجنس <span className="text-red-500">*</span></label>
                <select
                  className={`w-full p-2 border ${errors.gender ? 'border-red-500' : 'border-gray-300'} rounded-md focus:ring-[#21ADE7] focus:border-[#21ADE7]`}
                  name="gender"
                  value={basicInfo.gender || ''}
                  onChange={handleBasicInfoChange}
                  required
                >
                  <option value="male">ذكر</option>
                  <option value="female">أنثى</option>
                </select>
                {errors.gender && <p className="text-red-500 text-sm mt-1">{errors.gender}</p>}
              </div>
              <div>
                <label className="block text-gray-700 mb-2">رقم الهاتف</label>
                <input
                  className="w-full p-2 border border-gray-300 rounded-md focus:ring-[#21ADE7] focus:border-[#21ADE7]"
                  type="tel"
                  name="phone"
                  value={basicInfo.phone || ''}
                  onChange={handleBasicInfoChange}
                />
              </div>
              <div>
                <label className="block text-gray-700 mb-2">البريد الإلكتروني</label>
                <input
                  className="w-full p-2 border border-gray-300 rounded-md focus:ring-[#21ADE7] focus:border-[#21ADE7]"
                  type="email"
                  name="email"
                  value={basicInfo.email || ''}
                  onChange={handleBasicInfoChange}
                />
              </div>
            </div>
          </div>

          {/* بيانات ولي الأمر - العمود الثاني */}
          <div className="bg-white p-4 rounded-lg shadow-sm border-r-4 border-[#5578EB] h-full">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold text-[#5578EB]">بيانات ولي الأمر</h3>
              <button
                type="button"
                className="bg-[#5578EB] text-white px-3 py-1 rounded-md text-sm hover:bg-[#5578EB]/90 transition-colors duration-200"
                onClick={addGuardian}
              >
                إضافة ولي أمر
              </button>
            </div>

          {/* عرض أول ولي أمر فقط في العمود الثاني */}
          {guardians.length > 0 && (
            <div className="grid grid-cols-1 gap-4">
              <div>
                <label className="block text-gray-700 mb-2">الاسم الكامل <span className="text-red-500">*</span></label>
                <input
                  className={`w-full p-2 border ${errors.guardian_full_name ? 'border-red-500' : 'border-gray-300'} rounded-md focus:ring-[#5578EB] focus:border-[#5578EB]`}
                  type="text"
                  value={guardians[0].full_name || ''}
                  onChange={(e) => handleGuardianChange(0, 'full_name', e.target.value)}
                  required
                />
                {errors.guardian_full_name && (
                  <p className="text-red-500 text-sm mt-1">{errors.guardian_full_name}</p>
                )}
              </div>
              <div>
                <label className="block text-gray-700 mb-2">العلاقة بالطالب <span className="text-red-500">*</span></label>
                <select
                  className={`w-full p-2 border ${errors.guardian_relationship ? 'border-red-500' : 'border-gray-300'} rounded-md focus:ring-[#5578EB] focus:border-[#5578EB]`}
                  value={guardians[0].relationship || ''}
                  onChange={(e) => handleGuardianChange(0, 'relationship', e.target.value)}
                  required
                >
                  <option value="">اختر العلاقة</option>
                  <option value="father">الأب</option>
                  <option value="mother">الأم</option>
                  <option value="brother">الأخ</option>
                  <option value="sister">الأخت</option>
                  <option value="grandfather">الجد</option>
                  <option value="grandmother">الجدة</option>
                  <option value="uncle">العم</option>
                  <option value="aunt">العمة</option>
                  <option value="other">أخرى</option>
                </select>
                {errors.guardian_relationship && (
                  <p className="text-red-500 text-sm mt-1">{errors.guardian_relationship}</p>
                )}
              </div>
              <div>
                <label className="block text-gray-700 mb-2">رقم الهوية <span className="text-red-500">*</span></label>
                <input
                  className={`w-full p-2 border ${errors.guardian_id_number ? 'border-red-500' : 'border-gray-300'} rounded-md focus:ring-[#5578EB] focus:border-[#5578EB]`}
                  type="text"
                  value={guardians[0].id_number || ''}
                  onChange={(e) => handleGuardianChange(0, 'id_number', e.target.value)}
                  required
                />
                {errors.guardian_id_number && (
                  <p className="text-red-500 text-sm mt-1">{errors.guardian_id_number}</p>
                )}
              </div>
              <div>
                <label className="block text-gray-700 mb-2">رقم الهاتف <span className="text-red-500">*</span></label>
                <input
                  className={`w-full p-2 border ${errors.guardian_phone ? 'border-red-500' : 'border-gray-300'} rounded-md focus:ring-[#5578EB] focus:border-[#5578EB]`}
                  type="tel"
                  value={guardians[0].phone || ''}
                  onChange={(e) => handleGuardianChange(0, 'phone', e.target.value)}
                  required
                />
                {errors.guardian_phone && (
                  <p className="text-red-500 text-sm mt-1">{errors.guardian_phone}</p>
                )}
              </div>
              <div>
                <label className="block text-gray-700 mb-2">البريد الإلكتروني</label>
                <input
                  className="w-full p-2 border border-gray-300 rounded-md focus:ring-[#5578EB] focus:border-[#5578EB]"
                  type="email"
                  value={guardians[0].email || ''}
                  onChange={(e) => handleGuardianChange(0, 'email', e.target.value)}
                />
              </div>
              <div>
                <label className="block text-gray-700 mb-2">المهنة</label>
                <input
                  className="w-full p-2 border border-gray-300 rounded-md focus:ring-[#5578EB] focus:border-[#5578EB]"
                  type="text"
                  value={guardians[0].occupation || ''}
                  onChange={(e) => handleGuardianChange(0, 'occupation', e.target.value)}
                />
              </div>
              <div>
                <label className="block text-gray-700 mb-2">مكان العمل</label>
                <input
                  className="w-full p-2 border border-gray-300 rounded-md focus:ring-[#5578EB] focus:border-[#5578EB]"
                  type="text"
                  value={guardians[0].workplace || ''}
                  onChange={(e) => handleGuardianChange(0, 'workplace', e.target.value)}
                />
              </div>
            </div>
          )}
          </div>

          {/* البيانات المالية - العمود الثالث */}
          <div className="bg-white p-4 rounded-lg shadow-sm border-r-4 border-[#0ABB87] h-full">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold text-[#0ABB87]">البيانات المالية</h3>
              <button
                type="button"
                className="bg-[#0ABB87] text-white px-3 py-1 rounded-md text-sm hover:bg-[#0ABB87]/90 transition-colors duration-200"
                onClick={() => setShowDetailedFinancial(!showDetailedFinancial)}
              >
                {showDetailedFinancial ? 'عرض مختصر' : 'عرض مفصل'}
              </button>
            </div>
            <div className="grid grid-cols-1 gap-4">
              <div>
                <label className="block text-gray-700 mb-2">الرسوم الدراسية <span className="text-red-500">*</span></label>
                <input
                  className={`w-full p-2 border ${errors.tuition_fee ? 'border-red-500' : 'border-gray-300'} rounded-md focus:ring-[#0ABB87] focus:border-[#0ABB87]`}
                  type="number"
                  name="tuition_fee"
                  value={financialInfo.tuition_fee || 0}
                  onChange={handleFinancialInfoChange}
                  min="0"
                  step="0.01"
                  required
                />
                {errors.tuition_fee && <p className="text-red-500 text-sm mt-1">{errors.tuition_fee}</p>}
              </div>
              <div>
                <label className="block text-gray-700 mb-2">مبلغ الخصم</label>
                <input
                  className="w-full p-2 border border-gray-300 rounded-md focus:ring-[#0ABB87] focus:border-[#0ABB87]"
                  type="number"
                  name="discount_amount"
                  value={financialInfo.discount_amount || 0}
                  onChange={handleFinancialInfoChange}
                  min="0"
                  step="0.01"
                />
              </div>
              <div>
                <label className="block text-gray-700 mb-2">سبب الخصم</label>
                <input
                  className="w-full p-2 border border-gray-300 rounded-md focus:ring-[#0ABB87] focus:border-[#0ABB87]"
                  type="text"
                  name="discount_reason"
                  value={financialInfo.discount_reason || ''}
                  onChange={handleFinancialInfoChange}
                />
              </div>
              <div>
                <label className="block text-gray-700 mb-2">المبلغ المدفوع مسبقاً</label>
                <input
                  className="w-full p-2 border border-gray-300 rounded-md focus:ring-[#0ABB87] focus:border-[#0ABB87]"
                  type="number"
                  name="paid_amount"
                  value={financialInfo.paid_amount || 0}
                  onChange={handleFinancialInfoChange}
                  min="0"
                  step="0.01"
                />
              </div>
              <div>
                <label className="block text-gray-700 mb-2">طريقة الدفع</label>
                <select
                  className="w-full p-2 border border-gray-300 rounded-md focus:ring-[#0ABB87] focus:border-[#0ABB87]"
                  name="payment_method"
                  value={financialInfo.payment_method || 'cash'}
                  onChange={handleFinancialInfoChange}
                >
                  <option value="cash">نقدي</option>
                  <option value="installments">أقساط</option>
                  <option value="bank_transfer">تحويل بنكي</option>
                  <option value="check">شيك</option>
                </select>
              </div>
              <div>
                <label className="block text-gray-700 mb-2">عدد الأقساط</label>
                <input
                  className="w-full p-2 border border-gray-300 rounded-md focus:ring-[#0ABB87] focus:border-[#0ABB87]"
                  type="number"
                  name="installments_count"
                  value={financialInfo.installments_count || 0}
                  onChange={handleFinancialInfoChange}
                  min="0"
                />
              </div>
            </div>
          </div>

        </div>

        {/* قسم المعلومات الأكاديمية */}
        <div className="mt-6 bg-white p-4 rounded-lg shadow-sm border-r-4 border-[#9C27B0]">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-semibold text-[#9C27B0]">المعلومات الأكاديمية</h3>
            <div className="flex items-center">
              <FaGraduationCap className="ml-2 text-[#9C27B0]" size={18} />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-gray-700 mb-2">المرحلة الدراسية <span className="text-red-500">*</span></label>
              <select
                className="w-full p-2 border border-gray-300 rounded-md focus:ring-[#9C27B0] focus:border-[#9C27B0]"
                name="grade_id"
                value={academicRecord.grade_id || ''}
                onChange={handleAcademicRecordChange}
                required
              >
                <option value="">اختر المرحلة الدراسية</option>
                {grades.map(grade => (
                  <option key={grade.id} value={grade.id}>{grade.name}</option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-gray-700 mb-2">الفصل الدراسي</label>
              <select
                className="w-full p-2 border border-gray-300 rounded-md focus:ring-[#9C27B0] focus:border-[#9C27B0]"
                name="class_id"
                value={academicRecord.class_id || ''}
                onChange={handleAcademicRecordChange}
                disabled={!academicRecord.grade_id}
              >
                <option value="">اختر الفصل الدراسي</option>
                {classes
                  .filter(cls => cls.grade_id === parseInt(academicRecord.grade_id?.toString() || '0'))
                  .map(cls => (
                    <option key={cls.id} value={cls.id}>{cls.name}</option>
                  ))
                }
              </select>
            </div>

            <div>
              <label className="block text-gray-700 mb-2">العام الدراسي</label>
              <input
                className="w-full p-2 border border-gray-300 rounded-md focus:ring-[#9C27B0] focus:border-[#9C27B0]"
                type="text"
                name="academic_year"
                value={academicRecord.academic_year || ''}
                onChange={handleAcademicRecordChange}
              />
            </div>

            <div>
              <label className="block text-gray-700 mb-2">تاريخ التسجيل</label>
              <input
                className="w-full p-2 border border-gray-300 rounded-md focus:ring-[#9C27B0] focus:border-[#9C27B0]"
                type="date"
                name="registration_date"
                value={academicRecord.registration_date || ''}
                onChange={handleAcademicRecordChange}
              />
            </div>
          </div>
        </div>

        {/* قسم الملاحظات */}
        <div className="mt-6 bg-white p-4 rounded-lg shadow-sm border-r-4 border-[#9C27B0]">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-semibold text-[#9C27B0]">ملاحظات</h3>
            <div className="w-10 h-10 rounded-full bg-[#9C27B0]/10 flex items-center justify-center text-[#9C27B0]">
              <FaStickyNote size={18} />
            </div>
          </div>
          <div className="grid grid-cols-1 gap-4">
            <div>
              <label className="block text-gray-700 mb-2">ملاحظات عن الطالب</label>
              <textarea
                className="w-full p-2 border border-gray-300 rounded-md focus:ring-[#9C27B0] focus:border-[#9C27B0]"
                name="additional_notes"
                value={additionalInfo.additional_notes || ''}
                onChange={handleAdditionalInfoChange}
                rows={4}
                placeholder="أضف ملاحظات عن الطالب هنا..."
              ></textarea>
            </div>
          </div>
        </div>

        {/* قسم البيانات المالية المفصلة */}
        {showDetailedFinancial && (
          <div className="mt-6 bg-white p-4 rounded-lg shadow-sm border-r-4 border-[#FD1361]">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold text-[#FD1361]">البيانات المالية المفصلة</h3>
              <button
                type="button"
                className="bg-[#FD1361] text-white px-3 py-1 rounded-md text-sm hover:bg-[#FD1361]/90 transition-colors duration-200"
                onClick={() => setShowDetailedFinancial(false)}
              >
                إخفاء
              </button>
            </div>

            {/* المدفوعات الأخرى */}
            <div className="mb-6">
              <h4 className="text-md font-semibold mb-3 border-r-2 border-[#FD1361] pr-2">المدفوعات الأخرى</h4>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-gray-700 mb-2">رسوم فتح الملف</label>
                  <input
                    className="w-full p-2 border border-gray-300 rounded-md focus:ring-[#FD1361] focus:border-[#FD1361]"
                    type="number"
                    name="file_opening_fee"
                    value={otherPayment.file_opening_fee || 0}
                    onChange={handleOtherPaymentChange}
                    min="0"
                    step="0.01"
                  />
                </div>
                <div>
                  <label className="block text-gray-700 mb-2">رسوم الكتب</label>
                  <input
                    className="w-full p-2 border border-gray-300 rounded-md focus:ring-[#FD1361] focus:border-[#FD1361]"
                    type="number"
                    name="books_fee"
                    value={otherPayment.books_fee || 0}
                    onChange={handleOtherPaymentChange}
                    min="0"
                    step="0.01"
                  />
                </div>
                <div>
                  <label className="block text-gray-700 mb-2">عدد قطع الزي المدرسي</label>
                  <input
                    className="w-full p-2 border border-gray-300 rounded-md focus:ring-[#FD1361] focus:border-[#FD1361]"
                    type="number"
                    name="uniform_count"
                    value={otherPayment.uniform_count || 0}
                    onChange={handleOtherPaymentChange}
                    min="0"
                  />
                </div>
                <div>
                  <label className="block text-gray-700 mb-2">إجمالي الزي المدرسي</label>
                  <input
                    className="w-full p-2 border border-gray-300 rounded-md bg-gray-50"
                    type="number"
                    name="uniform_total"
                    value={otherPayment.uniform_total || 0}
                    readOnly
                  />
                </div>
                <div>
                  <label className="block text-gray-700 mb-2">رسوم النقل</label>
                  <input
                    className="w-full p-2 border border-gray-300 rounded-md focus:ring-[#FD1361] focus:border-[#FD1361]"
                    type="number"
                    name="transportation_fee"
                    value={otherPayment.transportation_fee || 0}
                    onChange={handleOtherPaymentChange}
                    min="0"
                    step="0.01"
                  />
                </div>
                <div>
                  <label className="block text-gray-700 mb-2">إجمالي المدفوعات الأخرى</label>
                  <input
                    className="w-full p-2 border border-gray-300 rounded-md bg-gray-50"
                    type="number"
                    name="total_amount"
                    value={otherPayment.total_amount || 0}
                    readOnly
                  />
                </div>
              </div>
            </div>

            {/* الأقساط */}
            <div>
              <div className="flex justify-between items-center mb-3">
                <h4 className="text-md font-semibold border-r-2 border-[#FD1361] pr-2">الأقساط</h4>
                {financialInfo.id && (
                  <button
                    type="button"
                    className="bg-[#FD1361] text-white px-3 py-1 rounded-md text-sm hover:bg-[#FD1361]/90 transition-colors duration-200 flex items-center"
                    onClick={handleAddInstallment}
                  >
                    <FaPlus className="ml-1" size={12} />
                    إضافة قسط
                  </button>
                )}
              </div>

              {!financialInfo.id ? (
                <div className="p-4 bg-gray-100 rounded-md text-center">
                  <p className="text-gray-600">يجب حفظ البيانات المالية أولاً قبل إضافة الأقساط</p>
                </div>
              ) : installments.length === 0 ? (
                <div className="p-4 bg-gray-100 rounded-md text-center">
                  <p className="text-gray-600">لا توجد أقساط مسجلة للطالب</p>
                </div>
              ) : (
                <>
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th scope="col" className="px-3 py-2 text-right text-xs font-medium text-gray-500">
                            المبلغ
                          </th>
                          <th scope="col" className="px-3 py-2 text-right text-xs font-medium text-gray-500">
                            تاريخ الاستحقاق
                          </th>
                          <th scope="col" className="px-3 py-2 text-right text-xs font-medium text-gray-500">
                            المدفوع
                          </th>
                          <th scope="col" className="px-3 py-2 text-right text-xs font-medium text-gray-500">
                            الخصم
                          </th>
                          <th scope="col" className="px-3 py-2 text-right text-xs font-medium text-gray-500">
                            المتبقي
                          </th>
                          <th scope="col" className="px-3 py-2 text-right text-xs font-medium text-gray-500">
                            الحالة
                          </th>
                          <th scope="col" className="px-3 py-2 text-right text-xs font-medium text-gray-500">
                            الإجراءات
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {installments.map((installment, index) => (
                          <tr key={index}>
                            <td className="px-3 py-2 whitespace-nowrap">
                              <input
                                className="w-full p-1 border border-gray-300 rounded-md focus:ring-[#FD1361] focus:border-[#FD1361] text-sm"
                                type="number"
                                value={installment.amount || 0}
                                onChange={(e) => handleInstallmentChange(index, 'amount', e.target.value)}
                                min="0"
                                step="0.01"
                              />
                            </td>
                            <td className="px-3 py-2 whitespace-nowrap">
                              <input
                                className="w-full p-1 border border-gray-300 rounded-md focus:ring-[#FD1361] focus:border-[#FD1361] text-sm"
                                type="date"
                                value={installment.due_date || ''}
                                onChange={(e) => handleInstallmentChange(index, 'due_date', e.target.value)}
                              />
                            </td>
                            <td className="px-3 py-2 whitespace-nowrap">
                              <input
                                className="w-full p-1 border border-gray-300 rounded-md focus:ring-[#FD1361] focus:border-[#FD1361] text-sm"
                                type="number"
                                value={installment.paid || 0}
                                onChange={(e) => handleInstallmentChange(index, 'paid', e.target.value)}
                                min="0"
                                step="0.01"
                              />
                            </td>
                            <td className="px-3 py-2 whitespace-nowrap">
                              <input
                                className="w-full p-1 border border-gray-300 rounded-md focus:ring-[#FD1361] focus:border-[#FD1361] text-sm"
                                type="number"
                                value={installment.discount || 0}
                                onChange={(e) => handleInstallmentChange(index, 'discount', e.target.value)}
                                min="0"
                                step="0.01"
                              />
                            </td>
                            <td className="px-3 py-2 whitespace-nowrap">
                              <input
                                className="w-full p-1 border border-gray-300 rounded-md bg-gray-50 text-sm"
                                type="number"
                                value={Math.max(0, parseFloat(installment.amount?.toString() || '0') - parseFloat(installment.paid?.toString() || '0') - parseFloat(installment.discount?.toString() || '0')).toFixed(2)}
                                readOnly
                              />
                            </td>
                            <td className="px-3 py-2 whitespace-nowrap">
                              <select
                                className="w-full p-1 border border-gray-300 rounded-md focus:ring-[#FD1361] focus:border-[#FD1361] text-sm"
                                value={installment.status || 'pending'}
                                onChange={(e) => handleInstallmentChange(index, 'status', e.target.value)}
                              >
                                <option value="pending">قيد الانتظار</option>
                                <option value="paid">مدفوع</option>
                                <option value="overdue">متأخر</option>
                              </select>
                            </td>
                            <td className="px-3 py-2 whitespace-nowrap text-center">
                              <button
                                type="button"
                                className="text-red-500 hover:text-red-700"
                                onClick={() => handleDeleteInstallment(index)}
                                title="حذف"
                              >
                                <FaTrash size={14} />
                              </button>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                      <tfoot>
                        <tr className="bg-gray-100 font-bold border-t-2 border-gray-300">
                          <td className="px-3 py-3 whitespace-nowrap text-right border-r border-gray-300">
                            <span className="text-[#0ABB87]">{installments.reduce((sum, item) => sum + parseFloat(item.amount?.toString() || '0'), 0).toFixed(2)}</span>
                          </td>
                          <td className="px-3 py-3 whitespace-nowrap text-center border-r border-gray-300">
                            <span className="text-gray-700">الإجمالي</span>
                          </td>
                          <td className="px-3 py-3 whitespace-nowrap text-right border-r border-gray-300">
                            <span className="text-[#5578EB]">{installments.reduce((sum, item) => sum + parseFloat(item.paid?.toString() || '0'), 0).toFixed(2)}</span>
                          </td>
                          <td className="px-3 py-3 whitespace-nowrap text-right border-r border-gray-300">
                            <span className="text-[#21ADE7]">{installments.reduce((sum, item) => sum + parseFloat(item.discount?.toString() || '0'), 0).toFixed(2)}</span>
                          </td>
                          <td className="px-3 py-3 whitespace-nowrap text-right border-r border-gray-300">
                            <span className="text-[#FD1361] font-bold text-lg">{installments.reduce((sum, item) => {
                              const amount = parseFloat(item.amount?.toString() || '0');
                              const paid = parseFloat(item.paid?.toString() || '0');
                              const discount = parseFloat(item.discount?.toString() || '0');
                              return sum + Math.max(0, amount - paid - discount);
                            }, 0).toFixed(2)}</span>
                          </td>
                          <td className="px-3 py-3 whitespace-nowrap border-r border-gray-300"></td>
                          <td className="px-3 py-3 whitespace-nowrap"></td>
                        </tr>
                      </tfoot>
                    </table>
                  </div>

                  {/* ملخص المعلومات المالية - خارج جدول الأقساط */}
                  <div className="mt-4 p-3 bg-gray-50 rounded-md border-2 border-[#FD1361] shadow-sm">
                    <h5 className="text-lg font-bold mb-2 text-[#FD1361]">ملخص المعلومات المالية</h5>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <p className="flex justify-between py-1 border-b border-gray-200">
                          <span className="font-semibold">الرسوم الدراسية الأصلية:</span>
                          <span>{parseFloat(financialInfo.tuition_fee?.toString() || '0').toFixed(2)}</span>
                        </p>
                        <p className="flex justify-between py-1 border-b border-gray-200">
                          <span className="font-semibold">مبلغ الخصم:</span>
                          <span>{parseFloat(financialInfo.discount_amount?.toString() || '0').toFixed(2)}</span>
                        </p>
                        <p className="flex justify-between py-1 border-b border-gray-200">
                          <span className="font-semibold">المبلغ بعد الخصم:</span>
                          <span>{(parseFloat(financialInfo.tuition_fee?.toString() || '0') - parseFloat(financialInfo.discount_amount?.toString() || '0')).toFixed(2)}</span>
                        </p>
                      </div>
                      <div>
                        <p className="flex justify-between py-1 border-b border-gray-200">
                          <span className="font-semibold">المبلغ المدفوع مسبقاً:</span>
                          <span>{parseFloat(financialInfo.paid_amount?.toString() || '0').toFixed(2)}</span>
                        </p>
                        <p className="flex justify-between py-1 border-b border-gray-200">
                          <span className="font-semibold">إجمالي المدفوع من الأقساط:</span>
                          <span>{installments.reduce((sum, item) => sum + parseFloat(item.paid?.toString() || '0'), 0).toFixed(2)}</span>
                        </p>
                        <p className="flex justify-between py-1 border-b border-gray-200 font-bold text-[#FD1361]">
                          <span>المبلغ المتبقي:</span>
                          <span>{installments.reduce((sum, item) => {
                            const amount = parseFloat(item.amount?.toString() || '0');
                            const paid = parseFloat(item.paid?.toString() || '0');
                            const discount = parseFloat(item.discount?.toString() || '0');
                            return sum + Math.max(0, amount - paid - discount);
                          }, 0).toFixed(2)}</span>
                        </p>
                      </div>
                    </div>
                  </div>
                </>
              )}
            </div>
          </div>
        )}

        {/* أزرار الإجراءات */}
        <div className="flex justify-end mt-6">
          <button
            type="button"
            className="px-4 py-2 bg-gray-200 text-gray-700 rounded-md ml-2 hover:bg-gray-300 transition-colors duration-200 flex items-center"
            onClick={onCancel}
          >
            <FaArrowRight className="ml-2" />
            إلغاء
          </button>
          <button
            type="button"
            className="px-4 py-2 bg-[#21ADE7] text-white rounded-md hover:bg-[#21ADE7]/90 transition-colors duration-200 flex items-center"
            onClick={handleSaveAll}
            disabled={isLoading}
          >
            <FaSave className="ml-2" />
            حفظ التغييرات
            {isLoading && <span className="mr-2">...</span>}
          </button>
        </div>
      </form>
    </div>
  );
};

export default ExpandableStudentEdit;
