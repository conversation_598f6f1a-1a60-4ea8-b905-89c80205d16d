# دليل إعداد قاعدة البيانات المحلية

## المتطلبات الأساسية
1. تثبيت PostgreSQL على جهازك المحلي
2. التأكد من تشغيل خدمة PostgreSQL

## خطوات الإعداد

### 1. إنشاء قاعدة البيانات
```sql
CREATE DATABASE school_management;
```

### 2. تنفيذ ملفات SQL
1. افتح محطة الأوامر (Command Prompt)
2. انتقل إلى مجلد المشروع
3. قم بتنفيذ الأمر التالي:
```bash
psql -U postgres -d school_management -f sql/run-all.sql
```

### 3. التحقق من الإعداد
1. تأكد من وجود الجداول التالية:
   - grades (المراحل الدراسية)
   - classes (الفصول الدراسية)

### 4. تكوين الاتصال
تم تكوين الاتصال بقاعدة البيانات المحلية في ملف `src/lib/database.ts` باستخدام المعلومات التالية:
- اسم المستخدم: postgres
- كلمة المرور: postgres
- اسم قاعدة البيانات: school_management
- المنفذ: 5432

إذا كنت تستخدم إعدادات مختلفة، قم بتعديل هذه القيم في الملف المذكور.

## استكشاف الأخطاء وإصلاحها
1. تأكد من تشغيل خدمة PostgreSQL
2. تحقق من صحة معلومات الاتصال
3. تأكد من وجود قاعدة البيانات والجداول
4. راجع سجلات الخطأ في وحدة تحكم التطبيق