"use client";

import React, { useState, useEffect } from 'react';
import { Installment, FinancialInfo } from '@/types/table.types';
import { FaCalendarAlt, FaMoneyBillWave, FaCheckCircle, FaExclamationCircle, FaHourglassHalf, FaPlus, FaSync } from 'react-icons/fa';
import { supabase } from '@/lib/supabase';
import EditInstallment from '@/components/editor/EditInstallment';

interface ViewInstallmentsProps {
  studentId: string | number;
}

const ViewInstallments: React.FC<ViewInstallmentsProps> = ({ studentId }) => {
  const [installments, setInstallments] = useState<Installment[]>([]);
  const [financialInfo, setFinancialInfo] = useState<FinancialInfo | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastRefresh, setLastRefresh] = useState<Date>(new Date());

  // تحميل البيانات المالية والأقساط
  const loadData = async () => {
    try {
      setIsLoading(true);
      setError(null);

      if (!studentId) {
        throw new Error('معرف الطالب غير موجود');
      }

      console.log('Loading financial info and installments for student:', studentId);

      // جلب المعلومات المالية مباشرة من قاعدة البيانات
      const { data: financialData, error: financialError } = await supabase
        .from('financial_info')
        .select('*')
        .eq('student_id', studentId)
        .single();

      if (financialError && financialError.code !== 'PGRST116') {
        console.error('Error fetching financial info directly:', financialError);
        throw new Error(financialError.message);
      }

      console.log('Financial info loaded directly:', financialData);
      setFinancialInfo(financialData);

      if (financialData) {
        // جلب الأقساط مباشرة من قاعدة البيانات
        const { data: installmentsData, error: installmentsError } = await supabase
          .from('installments')
          .select('*')
          .eq('financial_info_id', financialData.id)
          .order('due_date', { ascending: true });

        if (installmentsError) {
          console.error('Error fetching installments directly:', installmentsError);
          throw new Error(installmentsError.message);
        }

        console.log('Installments loaded directly:', installmentsData);
        setInstallments(installmentsData || []);
      }

      setLastRefresh(new Date());
    } catch (err: any) {
      console.error('Error fetching installments:', err);
      setError(err.message || 'حدث خطأ أثناء جلب بيانات الأقساط');
    } finally {
      setIsLoading(false);
    }
  };

  // تحميل البيانات عند تغيير معرف الطالب
  useEffect(() => {
    loadData();

    // إعداد اشتراك الوقت الحقيقي للمعلومات المالية
    const channel = supabase.channel('installments-changes');
    
    // الاشتراك في تغييرات المعلومات المالية
    channel.on('postgres_changes', {
      event: '*',
      schema: 'public',
      table: 'financial_info',
      filter: `student_id=eq.${studentId}`
    }, (payload) => {
      console.log('Financial info change received for installments:', payload);
      loadData();
    }).subscribe();
    
    // الاشتراك في تغييرات الأقساط
    channel.on('postgres_changes', {
      event: '*',
      schema: 'public',
      table: 'installments'
    }, (payload) => {
      console.log('Installments change received:', payload);
      loadData();
    }).subscribe();
    
    // دالة التنظيف
    return () => {
      supabase.removeChannel(channel);
    };
  }, [studentId]);

  // تنسيق التاريخ
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-EG');
  };

  // تنسيق المبلغ
  const formatAmount = (amount: number | null | undefined) => {
    if (amount === null || amount === undefined) {
      return '0 جنيه مصري';
    }
    return amount.toLocaleString('ar-EG', { style: 'currency', currency: 'EGP' });
  };

  // ترجمة حالة القسط
  const translateStatus = (status: string) => {
    switch (status) {
      case 'paid': return 'مدفوع';
      case 'pending': return 'قيد الانتظار';
      case 'overdue': return 'متأخر';
      default: return status;
    }
  };

  // لون حالة القسط
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'paid': return '#0ABB87';
      case 'pending': return '#5578EB';
      case 'overdue': return '#FD1361';
      default: return '#6B7280';
    }
  };

  // أيقونة حالة القسط
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'paid': return <FaCheckCircle size={18} className="text-[#0ABB87]" />;
      case 'pending': return <FaHourglassHalf size={18} className="text-[#5578EB]" />;
      case 'overdue': return <FaExclamationCircle size={18} className="text-[#FD1361]" />;
      default: return null;
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center p-8">
        <div className="relative">
          <div className="w-12 h-12 border-4 border-[#0ABB87]/30 border-t-[#0ABB87] rounded-full animate-spin"></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
            <FaCalendarAlt size={16} className="text-[#0ABB87]" />
          </div>
        </div>
        <p className="mr-3 text-gray-600">جاري تحميل البيانات...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-4 bg-[#FD1361]/10 border-r-4 border-[#FD1361] rounded-md">
        <p className="text-[#FD1361]">{error}</p>
        <button
          onClick={loadData}
          className="mt-2 px-4 py-2 bg-[#FD1361] text-white rounded-md hover:bg-[#FD1361]/80 flex items-center"
        >
          <FaSync className="ml-2" /> إعادة المحاولة
        </button>
      </div>
    );
  }

  if (!financialInfo) {
    return (
      <div className="p-4 bg-gray-100 border-r-4 border-gray-400 rounded-md">
        <p className="text-gray-600">لا توجد بيانات مالية للطالب (معرف الطالب: {studentId})</p>
        <button
          onClick={loadData}
          className="mt-2 px-4 py-2 bg-[#0ABB87] text-white rounded-md hover:bg-[#0ABB87]/80 flex items-center"
        >
          <FaSync className="ml-2" /> تحديث البيانات
        </button>
      </div>
    );
  }

  if (installments.length === 0) {
    return (
      <div className="p-4 bg-gray-100 border-r-4 border-gray-400 rounded-md">
        <p className="text-gray-600">لا توجد أقساط مسجلة للطالب</p>
        <EditInstallment
          financialInfoId={financialInfo.id}
          isNewInstallment={true}
          onUpdate={loadData}
        />
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg p-4">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-bold text-[#0ABB87]">الأقساط</h3>
        <div className="flex items-center">
          <button
            onClick={loadData}
            className="ml-2 p-2 bg-gray-100 text-gray-600 rounded-full hover:bg-gray-200"
            title="تحديث البيانات"
          >
            <FaSync size={14} />
          </button>
          <EditInstallment
            financialInfoId={financialInfo.id}
            isNewInstallment={true}
            onUpdate={loadData}
          />
        </div>
      </div>

      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                رقم القسط
              </th>
              <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                تاريخ الاستحقاق
              </th>
              <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                المبلغ
              </th>
              <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                المدفوع
              </th>
              <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                المتبقي
              </th>
              <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                الحالة
              </th>
              <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                الإجراءات
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {installments.map((installment, index) => (
              <tr key={installment.id}>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    <div className="w-8 h-8 rounded-full bg-[#0ABB87]/10 flex items-center justify-center text-[#0ABB87] ml-2">
                      <span className="text-sm font-medium">{index + 1}</span>
                    </div>
                    <span className="text-sm text-gray-900">القسط {index + 1}</span>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    <FaCalendarAlt size={16} className="text-[#5578EB] ml-2" />
                    <span className="text-sm text-gray-900">{formatDate(installment.due_date)}</span>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    <FaMoneyBillWave size={16} className="text-[#FD1361] ml-2" />
                    <span className="text-sm text-gray-900">{formatAmount(installment.amount)}</span>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className="text-sm text-gray-900">{formatAmount(installment.paid)}</span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className="text-sm text-gray-900">{formatAmount(installment.remaining)}</span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    {getStatusIcon(installment.status)}
                    <span
                      className="text-sm font-medium mr-2"
                      style={{ color: getStatusColor(installment.status) }}
                    >
                      {translateStatus(installment.status)}
                    </span>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center justify-center">
                    <EditInstallment
                      financialInfoId={financialInfo.id}
                      installment={installment}
                      onUpdate={loadData}
                    />
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
      <div className="mt-4 text-xs text-gray-400 text-left">
        آخر تحديث: {lastRefresh.toLocaleTimeString()}
      </div>
    </div>
  );
};

export default ViewInstallments;
