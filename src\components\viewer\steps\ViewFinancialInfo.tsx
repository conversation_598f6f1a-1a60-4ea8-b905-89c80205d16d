"use client";

import React, { useState, useEffect } from 'react';
import { FinancialInfo } from '@/types/table.types';
import { FaMoneyBillWave, FaCreditCard, FaPercentage, FaInfoCircle, FaMoneyCheckAlt, FaSync } from 'react-icons/fa';
import { supabase } from '@/lib/supabase';
import EditFinancialInfo from '@/components/editor/EditFinancialInfo';

interface ViewFinancialInfoProps {
  studentId: string | number;
}

const ViewFinancialInfo: React.FC<ViewFinancialInfoProps> = ({ studentId }) => {
  const [financialInfo, setFinancialInfo] = useState<FinancialInfo | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastRefresh, setLastRefresh] = useState<Date>(new Date());

  // تحميل البيانات المالية
  const loadFinancialInfo = async () => {
    try {
      setIsLoading(true);
      setError(null);

      if (!studentId) {
        throw new Error('معرف الطالب غير موجود');
      }

      console.log('Loading financial info for student:', studentId);

      // جلب البيانات المالية مباشرة من قاعدة البيانات
      const { data, error } = await supabase
        .from('financial_info')
        .select('*')
        .eq('student_id', studentId)
        .single();

      if (error && error.code !== 'PGRST116') {
        console.error('Error fetching financial info directly:', error);
        throw new Error(error.message);
      }

      console.log('Financial info loaded directly:', data);

      if (data) {
        setFinancialInfo(data);
        console.log('Financial info set for student:', studentId, data);
      } else {
        console.warn('No financial info found for student:', studentId);
        setFinancialInfo(null);
      }

      setLastRefresh(new Date());
    } catch (err: any) {
      console.error('Error fetching financial info:', err);
      setError(err.message || 'حدث خطأ أثناء جلب البيانات المالية');
    } finally {
      setIsLoading(false);
    }
  };

  // تحميل البيانات عند تغيير معرف الطالب
  useEffect(() => {
    loadFinancialInfo();

    // إعداد اشتراك الوقت الحقيقي للمعلومات المالية
    const channel = supabase.channel('financial-info-changes');

    channel.on('postgres_changes', {
      event: '*',
      schema: 'public',
      table: 'financial_info',
      filter: `student_id=eq.${studentId}`
    }, (payload) => {
      console.log('Financial info change received:', payload);
      loadFinancialInfo();
    }).subscribe();

    // دالة التنظيف
    return () => {
      supabase.removeChannel(channel);
    };
  }, [studentId]);

  // ترجمة طريقة الدفع
  const translatePaymentMethod = (method: string) => {
    switch (method) {
      case 'cash': return 'نقدي';
      case 'bank_transfer': return 'تحويل بنكي';
      case 'credit_card': return 'بطاقة ائتمان';
      case 'check': return 'شيك';
      case 'installments': return 'أقساط';
      default: return method;
    }
  };

  // تنسيق المبلغ
  const formatAmount = (amount: number | null | undefined) => {
    if (amount === null || amount === undefined) {
      return '0 جنيه مصري';
    }
    return amount.toLocaleString('ar-EG', { style: 'currency', currency: 'EGP' });
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center p-8">
        <div className="relative">
          <div className="w-12 h-12 border-4 border-[#FD1361]/30 border-t-[#FD1361] rounded-full animate-spin"></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
            <FaMoneyBillWave size={16} className="text-[#FD1361]" />
          </div>
        </div>
        <p className="mr-3 text-gray-600">جاري تحميل البيانات...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-4 bg-[#FD1361]/10 border-r-4 border-[#FD1361] rounded-md">
        <p className="text-[#FD1361]">{error}</p>
        <button
          onClick={loadFinancialInfo}
          className="mt-2 px-4 py-2 bg-[#FD1361] text-white rounded-md hover:bg-[#FD1361]/80 flex items-center"
        >
          <FaSync className="ml-2" /> إعادة المحاولة
        </button>
      </div>
    );
  }

  if (!financialInfo) {
    return (
      <div className="p-4 bg-gray-100 border-r-4 border-gray-400 rounded-md">
        <p className="text-gray-600">لا توجد بيانات مالية للطالب (معرف الطالب: {studentId})</p>
        <button
          onClick={loadFinancialInfo}
          className="mt-2 px-4 py-2 bg-[#FD1361] text-white rounded-md hover:bg-[#FD1361]/80 flex items-center"
        >
          <FaSync className="ml-2" /> تحديث البيانات المالية
        </button>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg p-4">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-bold text-[#0ABB87]">المعلومات المالية</h3>
        <div className="flex items-center">
          <button
            onClick={loadFinancialInfo}
            className="ml-2 p-2 bg-gray-100 text-gray-600 rounded-full hover:bg-gray-200"
            title="تحديث البيانات"
          >
            <FaSync size={14} />
          </button>
          <EditFinancialInfo
            studentId={studentId}
            financialInfo={financialInfo}
            onUpdate={loadFinancialInfo}
          />
        </div>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="flex items-start">
          <div className="w-10 h-10 rounded-full bg-[#FD1361]/10 flex items-center justify-center text-[#FD1361] ml-4">
            <FaMoneyBillWave size={18} />
          </div>
          <div>
            <h3 className="text-sm text-gray-500">الرسوم الدراسية</h3>
            <p className="font-medium">{formatAmount(financialInfo.tuition_fee)}</p>
          </div>
        </div>

        <div className="flex items-start">
          <div className="w-10 h-10 rounded-full bg-[#5578EB]/10 flex items-center justify-center text-[#5578EB] ml-4">
            <FaCreditCard size={18} />
          </div>
          <div>
            <h3 className="text-sm text-gray-500">طريقة الدفع</h3>
            <p className="font-medium">{translatePaymentMethod(financialInfo.payment_method)}</p>
          </div>
        </div>

        {financialInfo.discount_amount > 0 && (
          <div className="flex items-start">
            <div className="w-10 h-10 rounded-full bg-[#0ABB87]/10 flex items-center justify-center text-[#0ABB87] ml-4">
              <FaPercentage size={18} />
            </div>
            <div>
              <h3 className="text-sm text-gray-500">قيمة الخصم</h3>
              <p className="font-medium">{formatAmount(financialInfo.discount_amount)}</p>
            </div>
          </div>
        )}

        {financialInfo.discount_reason && (
          <div className="flex items-start">
            <div className="w-10 h-10 rounded-full bg-[#384AD7]/10 flex items-center justify-center text-[#384AD7] ml-4">
              <FaInfoCircle size={18} />
            </div>
            <div>
              <h3 className="text-sm text-gray-500">سبب الخصم</h3>
              <p className="font-medium">{financialInfo.discount_reason}</p>
            </div>
          </div>
        )}

        <div className="flex items-start">
          <div className="w-10 h-10 rounded-full bg-[#21ADE7]/10 flex items-center justify-center text-[#21ADE7] ml-4">
            <FaMoneyCheckAlt size={18} />
          </div>
          <div>
            <h3 className="text-sm text-gray-500">المبلغ المدفوع</h3>
            <p className="font-medium">{formatAmount(financialInfo.paid_amount)}</p>
          </div>
        </div>

        <div className="flex items-start">
          <div className="w-10 h-10 rounded-full bg-[#0ABB87]/10 flex items-center justify-center text-[#0ABB87] ml-4">
            <FaMoneyCheckAlt size={18} />
          </div>
          <div>
            <h3 className="text-sm text-gray-500">المبلغ الإجمالي</h3>
            <p className="font-medium">{formatAmount(financialInfo.tuition_fee - financialInfo.discount_amount)}</p>
          </div>
        </div>
      </div>
      <div className="mt-4 text-xs text-gray-400 text-left">
        آخر تحديث: {lastRefresh.toLocaleTimeString()}
      </div>
    </div>
  );
};

export default ViewFinancialInfo;
