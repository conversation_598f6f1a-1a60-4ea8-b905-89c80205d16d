"use client";

import React, { createContext, useContext, useState, useEffect, ReactNode, useRef } from 'react';
import { supabase } from '@/lib/supabase';
import { RealtimeChannel } from '@supabase/supabase-js';

// Define types for our context
interface RealtimeContextType {
  // Student data
  students: any[];
  refreshStudents: () => Promise<void>;

  // Financial info
  financialInfo: Record<string | number, any>;
  refreshFinancialInfo: (studentId: string | number) => Promise<void>;

  // Installments
  installments: Record<string | number, any[]>;
  refreshInstallments: (financialInfoId: string | number) => Promise<void>;

  // Other payments
  otherPayments: Record<string | number, any[]>;
  refreshOtherPayments: (studentId: string | number) => Promise<void>;

  // Health info
  healthInfo: Record<string | number, any>;
  refreshHealthInfo: (studentId: string | number) => Promise<void>;

  // Additional info
  additionalInfo: Record<string | number, any>;
  refreshAdditionalInfo: (studentId: string | number) => Promise<void>;

  // Guardians
  guardians: Record<string | number, any[]>;
  refreshGuardians: (studentId: string | number) => Promise<void>;

  // Attachments
  attachments: Record<string | number, any>;
  refreshAttachments: (studentId: string | number) => Promise<void>;

  // Loading states
  isLoading: Record<string, boolean>;
}

// Create the context
const RealtimeContext = createContext<RealtimeContextType | undefined>(undefined);

// Provider component
export function RealtimeProvider({ children }: { children: ReactNode }) {
  // State for all data types
  const [students, setStudents] = useState<any[]>([]);
  const [financialInfo, setFinancialInfo] = useState<Record<string | number, any>>({});
  const [installments, setInstallments] = useState<Record<string | number, any[]>>({});
  const [otherPayments, setOtherPayments] = useState<Record<string | number, any[]>>({});
  const [healthInfo, setHealthInfo] = useState<Record<string | number, any>>({});
  const [additionalInfo, setAdditionalInfo] = useState<Record<string | number, any>>({});
  const [guardians, setGuardians] = useState<Record<string | number, any[]>>({});
  const [attachments, setAttachments] = useState<Record<string | number, any>>({});

  // Loading states
  const [isLoading, setIsLoading] = useState<Record<string, boolean>>({
    students: false,
    financialInfo: false,
    installments: false,
    otherPayments: false,
    healthInfo: false,
    additionalInfo: false,
    guardians: false,
    attachments: false,
  });

  // Channels for real-time subscriptions
  const [channels, setChannels] = useState<RealtimeChannel[]>([]);

  // استخدام useRef للاحتفاظ بمراجع للدوال التي لا تتغير مع كل تصيير
  const refreshStudentsRef = useRef(async () => {
    try {
      setIsLoading(prev => ({ ...prev, students: true }));

      const { data, error } = await supabase
        .from('students')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) {
        throw new Error(error.message);
      }

      setStudents(data || []);
    } catch (err) {
      console.error('Error fetching students:', err);
    } finally {
      setIsLoading(prev => ({ ...prev, students: false }));
    }
  });

  const refreshFinancialInfoRef = useRef(async (studentId: string | number) => {
    try {
      setIsLoading(prev => ({ ...prev, financialInfo: true }));

      const { data, error } = await supabase
        .from('financial_info')
        .select('*')
        .eq('student_id', studentId)
        .single();

      if (error && error.code !== 'PGRST116') {
        throw new Error(error.message);
      }

      setFinancialInfo(prev => ({
        ...prev,
        [studentId]: data
      }));
    } catch (err) {
      console.error(`Error fetching financial info for student ${studentId}:`, err);
    } finally {
      setIsLoading(prev => ({ ...prev, financialInfo: false }));
    }
  });

  const refreshInstallmentsRef = useRef(async (financialInfoId: string | number) => {
    try {
      setIsLoading(prev => ({ ...prev, installments: true }));

      const { data, error } = await supabase
        .from('installments')
        .select('*')
        .eq('financial_info_id', financialInfoId)
        .order('due_date', { ascending: true });

      if (error) {
        throw new Error(error.message);
      }

      setInstallments(prev => ({
        ...prev,
        [financialInfoId]: data || []
      }));
    } catch (err) {
      console.error(`Error fetching installments for financial info ${financialInfoId}:`, err);
    } finally {
      setIsLoading(prev => ({ ...prev, installments: false }));
    }
  });

  const refreshOtherPaymentsRef = useRef(async (studentId: string | number) => {
    try {
      setIsLoading(prev => ({ ...prev, otherPayments: true }));

      const { data, error } = await supabase
        .from('other_payments')
        .select('*')
        .eq('student_id', studentId)
        .order('created_at', { ascending: false });

      if (error) {
        throw new Error(error.message);
      }

      setOtherPayments(prev => ({
        ...prev,
        [studentId]: data || []
      }));
    } catch (err) {
      console.error(`Error fetching other payments for student ${studentId}:`, err);
    } finally {
      setIsLoading(prev => ({ ...prev, otherPayments: false }));
    }
  });

  const refreshHealthInfoRef = useRef(async (studentId: string | number) => {
    try {
      setIsLoading(prev => ({ ...prev, healthInfo: true }));

      const { data, error } = await supabase
        .from('health_info')
        .select('*')
        .eq('student_id', studentId)
        .single();

      if (error && error.code !== 'PGRST116') {
        throw new Error(error.message);
      }

      setHealthInfo(prev => ({
        ...prev,
        [studentId]: data
      }));
    } catch (err) {
      console.error(`Error fetching health info for student ${studentId}:`, err);
    } finally {
      setIsLoading(prev => ({ ...prev, healthInfo: false }));
    }
  });

  const refreshAdditionalInfoRef = useRef(async (studentId: string | number) => {
    try {
      setIsLoading(prev => ({ ...prev, additionalInfo: true }));

      const { data, error } = await supabase
        .from('additional_info')
        .select('*')
        .eq('student_id', studentId)
        .single();

      if (error && error.code !== 'PGRST116') {
        throw new Error(error.message);
      }

      setAdditionalInfo(prev => ({
        ...prev,
        [studentId]: data
      }));
    } catch (err) {
      console.error(`Error fetching additional info for student ${studentId}:`, err);
    } finally {
      setIsLoading(prev => ({ ...prev, additionalInfo: false }));
    }
  });

  const refreshGuardiansRef = useRef(async (studentId: string | number) => {
    try {
      setIsLoading(prev => ({ ...prev, guardians: true }));

      const { data, error } = await supabase
        .from('guardians')
        .select('*')
        .eq('student_id', studentId);

      if (error) {
        throw new Error(error.message);
      }

      setGuardians(prev => ({
        ...prev,
        [studentId]: data || []
      }));
    } catch (err) {
      console.error(`Error fetching guardians for student ${studentId}:`, err);
    } finally {
      setIsLoading(prev => ({ ...prev, guardians: false }));
    }
  });

  const refreshAttachmentsRef = useRef(async (studentId: string | number) => {
    try {
      setIsLoading(prev => ({ ...prev, attachments: true }));

      const { data, error } = await supabase
        .from('attachments')
        .select('*')
        .eq('student_id', studentId)
        .single();

      if (error && error.code !== 'PGRST116') {
        throw new Error(error.message);
      }

      setAttachments(prev => ({
        ...prev,
        [studentId]: data
      }));
    } catch (err) {
      console.error(`Error fetching attachments for student ${studentId}:`, err);
    } finally {
      setIsLoading(prev => ({ ...prev, attachments: false }));
    }
  });

  // تعريف الدوال المستخدمة في السياق مباشرة من المراجع
  // بدون استخدام useEffect لتجنب التحديثات غير الضرورية
  const refreshStudents = refreshStudentsRef.current;
  const refreshFinancialInfo = refreshFinancialInfoRef.current;
  const refreshInstallments = refreshInstallmentsRef.current;
  const refreshOtherPayments = refreshOtherPaymentsRef.current;
  const refreshHealthInfo = refreshHealthInfoRef.current;
  const refreshAdditionalInfo = refreshAdditionalInfoRef.current;
  const refreshGuardians = refreshGuardiansRef.current;
  const refreshAttachments = refreshAttachmentsRef.current;

  // إعداد اشتراكات الوقت الحقيقي - استخدام useRef لتجنب التحديثات المتكررة
  const channelSetupRef = useRef(false);

  useEffect(() => {
    // تنفيذ الإعداد مرة واحدة فقط
    if (channelSetupRef.current) return;

    console.log('Setting up Realtime subscriptions...');

    try {
      // إنشاء قناة واحدة لجميع الجداول
      const channel = supabase.channel('db-changes');

      // الاشتراك في تغييرات جدول الطلاب
      channel.on('postgres_changes', {
        event: '*',
        schema: 'public',
        table: 'students'
      }, (payload) => {
        console.log('Students change received:', payload);
        // استخدام setTimeout لتجنب التحديثات المتزامنة
        setTimeout(() => {
          refreshStudentsRef.current();
        }, 100);
      });

      // الاشتراك في تغييرات جدول المعلومات المالية
      channel.on('postgres_changes', {
        event: '*',
        schema: 'public',
        table: 'financial_info'
      }, (payload) => {
        console.log('Financial info change received:', payload);
        const studentId = payload.new?.student_id || payload.old?.student_id;
        if (studentId) {
          setTimeout(() => {
            refreshFinancialInfoRef.current(studentId);
          }, 100);
        }
      });

      // الاشتراك في تغييرات جدول الأقساط
      channel.on('postgres_changes', {
        event: '*',
        schema: 'public',
        table: 'installments'
      }, (payload) => {
        console.log('Installments change received:', payload);
        const financialInfoId = payload.new?.financial_info_id || payload.old?.financial_info_id;
        if (financialInfoId) {
          setTimeout(() => {
            refreshInstallmentsRef.current(financialInfoId);
          }, 100);
        }
      });

      // الاشتراك في تغييرات جدول المدفوعات الأخرى
      channel.on('postgres_changes', {
        event: '*',
        schema: 'public',
        table: 'other_payments'
      }, (payload) => {
        console.log('Other payments change received:', payload);
        const studentId = payload.new?.student_id || payload.old?.student_id;
        if (studentId) {
          setTimeout(() => {
            refreshOtherPaymentsRef.current(studentId);
          }, 100);
        }
      });

      // الاشتراك في تغييرات جدول المعلومات الصحية
      channel.on('postgres_changes', {
        event: '*',
        schema: 'public',
        table: 'health_info'
      }, (payload) => {
        console.log('Health info change received:', payload);
        const studentId = payload.new?.student_id || payload.old?.student_id;
        if (studentId) {
          setTimeout(() => {
            refreshHealthInfoRef.current(studentId);
          }, 100);
        }
      });

      // الاشتراك في تغييرات جدول المعلومات الإضافية
      channel.on('postgres_changes', {
        event: '*',
        schema: 'public',
        table: 'additional_info'
      }, (payload) => {
        console.log('Additional info change received:', payload);
        const studentId = payload.new?.student_id || payload.old?.student_id;
        if (studentId) {
          setTimeout(() => {
            refreshAdditionalInfoRef.current(studentId);
          }, 100);
        }
      });

      // الاشتراك في تغييرات جدول أولياء الأمور
      channel.on('postgres_changes', {
        event: '*',
        schema: 'public',
        table: 'guardians'
      }, (payload) => {
        console.log('Guardians change received:', payload);
        const studentId = payload.new?.student_id || payload.old?.student_id;
        if (studentId) {
          setTimeout(() => {
            refreshGuardiansRef.current(studentId);
          }, 100);
        }
      });

      // الاشتراك في تغييرات جدول المرفقات
      channel.on('postgres_changes', {
        event: '*',
        schema: 'public',
        table: 'attachments'
      }, (payload) => {
        console.log('Attachments change received:', payload);
        const studentId = payload.new?.student_id || payload.old?.student_id;
        if (studentId) {
          setTimeout(() => {
            refreshAttachmentsRef.current(studentId);
          }, 100);
        }
      });

      // الاشتراك في القناة
      channel.subscribe((status) => {
        console.log('Realtime subscription status:', status);
        if (status === 'SUBSCRIBED') {
          channelSetupRef.current = true;
        }
      });

      // تخزين القناة للتنظيف
      setChannels([channel]);

      // تحميل البيانات الأولية
      setTimeout(() => {
        refreshStudentsRef.current();
      }, 100);

      // دالة التنظيف
      return () => {
        console.log('Cleaning up Realtime subscriptions...');
        if (channel) {
          supabase.removeChannel(channel);
          channelSetupRef.current = false;
        }
      };
    } catch (error) {
      console.error('Error setting up Realtime subscriptions:', error);
      channelSetupRef.current = false;
    }
  }, []);

  // الدوال تم تعريفها بالفعل أعلاه

  // Context value
  const value: RealtimeContextType = {
    students,
    refreshStudents,
    financialInfo,
    refreshFinancialInfo,
    installments,
    refreshInstallments,
    otherPayments,
    refreshOtherPayments,
    healthInfo,
    refreshHealthInfo,
    additionalInfo,
    refreshAdditionalInfo,
    guardians,
    refreshGuardians,
    attachments,
    refreshAttachments,
    isLoading
  };

  return (
    <RealtimeContext.Provider value={value}>
      {children}
    </RealtimeContext.Provider>
  );
}

// Custom hook to use the context
export function useRealtime() {
  const context = useContext(RealtimeContext);

  if (context === undefined) {
    throw new Error('useRealtime must be used within a RealtimeProvider');
  }

  return context;
}
