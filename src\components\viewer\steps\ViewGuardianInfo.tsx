"use client";

import React, { useState, useEffect } from 'react';
import { supabase } from '@/lib/supabase';
import { Guardian } from '@/types/table.types';
import { FaUserFriends, FaIdCard, FaPhone, FaEnvelope, FaBriefcase, FaBuilding } from 'react-icons/fa';

interface ViewGuardianInfoProps {
  studentId: string | number;
}

const ViewGuardianInfo: React.FC<ViewGuardianInfoProps> = ({ studentId }) => {
  const [guardians, setGuardians] = useState<Guardian[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchGuardians() {
      try {
        setIsLoading(true);
        setError(null);
        
        if (!studentId) {
          throw new Error('معرف الطالب غير موجود');
        }
        
        const { data, error } = await supabase
          .from('guardians')
          .select('*')
          .eq('student_id', studentId);
        
        if (error) {
          throw new Error(error.message);
        }
        
        setGuardians(data || []);
      } catch (err: any) {
        console.error('Error fetching guardians:', err);
        setError(err.message || 'حدث خطأ أثناء جلب بيانات ولي الأمر');
      } finally {
        setIsLoading(false);
      }
    }
    
    fetchGuardians();
  }, [studentId]);

  // ترجمة نوع العلاقة
  const translateRelationship = (relationship: string) => {
    switch (relationship) {
      case 'father': return 'الأب';
      case 'mother': return 'الأم';
      case 'brother': return 'الأخ';
      case 'sister': return 'الأخت';
      case 'grandfather': return 'الجد';
      case 'grandmother': return 'الجدة';
      case 'uncle': return 'العم';
      case 'aunt': return 'العمة';
      default: return relationship;
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center p-8">
        <div className="relative">
          <div className="w-12 h-12 border-4 border-[#5578EB]/30 border-t-[#5578EB] rounded-full animate-spin"></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
            <FaUserFriends size={16} className="text-[#5578EB]" />
          </div>
        </div>
        <p className="mr-3 text-gray-600">جاري تحميل البيانات...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-4 bg-[#FD1361]/10 border-r-4 border-[#FD1361] rounded-md">
        <p className="text-[#FD1361]">{error}</p>
      </div>
    );
  }

  if (guardians.length === 0) {
    return (
      <div className="p-4 bg-gray-100 border-r-4 border-gray-400 rounded-md">
        <p className="text-gray-600">لا توجد بيانات لولي الأمر</p>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg p-4">
      {guardians.map((guardian, index) => (
        <div key={guardian.id} className="mb-6 last:mb-0">
          {guardians.length > 1 && (
            <h3 className="text-lg font-bold text-[#5578EB] mb-4 border-r-4 border-[#5578EB] pr-3">
              {`ولي الأمر ${index + 1}`}
            </h3>
          )}
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="flex items-start">
              <div className="w-10 h-10 rounded-full bg-[#5578EB]/10 flex items-center justify-center text-[#5578EB] ml-4">
                <FaUserFriends size={18} />
              </div>
              <div>
                <h3 className="text-sm text-gray-500">الاسم الكامل</h3>
                <p className="font-medium">{guardian.full_name}</p>
              </div>
            </div>
            
            <div className="flex items-start">
              <div className="w-10 h-10 rounded-full bg-[#21ADE7]/10 flex items-center justify-center text-[#21ADE7] ml-4">
                <FaUserFriends size={18} />
              </div>
              <div>
                <h3 className="text-sm text-gray-500">صلة القرابة</h3>
                <p className="font-medium">{translateRelationship(guardian.relationship)}</p>
              </div>
            </div>
            
            <div className="flex items-start">
              <div className="w-10 h-10 rounded-full bg-[#5578EB]/10 flex items-center justify-center text-[#5578EB] ml-4">
                <FaIdCard size={18} />
              </div>
              <div>
                <h3 className="text-sm text-gray-500">رقم الهوية</h3>
                <p className="font-medium">{guardian.id_number}</p>
              </div>
            </div>
            
            <div className="flex items-start">
              <div className="w-10 h-10 rounded-full bg-[#0ABB87]/10 flex items-center justify-center text-[#0ABB87] ml-4">
                <FaPhone size={18} />
              </div>
              <div>
                <h3 className="text-sm text-gray-500">رقم الهاتف</h3>
                <p className="font-medium">{guardian.phone}</p>
              </div>
            </div>
            
            {guardian.email && (
              <div className="flex items-start">
                <div className="w-10 h-10 rounded-full bg-[#FD1361]/10 flex items-center justify-center text-[#FD1361] ml-4">
                  <FaEnvelope size={18} />
                </div>
                <div>
                  <h3 className="text-sm text-gray-500">البريد الإلكتروني</h3>
                  <p className="font-medium">{guardian.email || 'غير متوفر'}</p>
                </div>
              </div>
            )}
            
            {guardian.occupation && (
              <div className="flex items-start">
                <div className="w-10 h-10 rounded-full bg-[#384AD7]/10 flex items-center justify-center text-[#384AD7] ml-4">
                  <FaBriefcase size={18} />
                </div>
                <div>
                  <h3 className="text-sm text-gray-500">المهنة</h3>
                  <p className="font-medium">{guardian.occupation}</p>
                </div>
              </div>
            )}
            
            {guardian.workplace && (
              <div className="flex items-start">
                <div className="w-10 h-10 rounded-full bg-[#21ADE7]/10 flex items-center justify-center text-[#21ADE7] ml-4">
                  <FaBuilding size={18} />
                </div>
                <div>
                  <h3 className="text-sm text-gray-500">مكان العمل</h3>
                  <p className="font-medium">{guardian.workplace}</p>
                </div>
              </div>
            )}
          </div>
          
          {index < guardians.length - 1 && <hr className="my-6 border-gray-200" />}
        </div>
      ))}
    </div>
  );
};

export default ViewGuardianInfo;
