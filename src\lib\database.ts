import { Pool } from 'pg';

// تكوين الاتصال بقاعدة البيانات المحلية
const pool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'school_management',
  password: 'postgres',
  port: 5432,
});

// دالة للتحقق من الاتصال
async function testConnection() {
  try {
    const client = await pool.connect();
    console.log('تم الاتصال بقاعدة البيانات بنجاح');
    client.release();
    return true;
  } catch (error) {
    console.error('خطأ في الاتصال بقاعدة البيانات:', error);
    return false;
  }
}

// تصدير الدوال المطلوبة
export const db = {
  query: (text: string, params?: any[]) => pool.query(text, params),
  testConnection,
};