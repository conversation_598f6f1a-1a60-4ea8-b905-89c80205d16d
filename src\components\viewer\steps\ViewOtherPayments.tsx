"use client";

import React, { useState, useEffect } from 'react';
import { supabase } from '@/lib/supabase';
import { OtherPayment } from '@/types/table.types';
import { FaMoneyCheckAlt, FaCalendarAlt, FaMoneyBillWave, FaInfoCircle } from 'react-icons/fa';

interface ViewOtherPaymentsProps {
  studentId: string | number;
}

const ViewOtherPayments: React.FC<ViewOtherPaymentsProps> = ({ studentId }) => {
  const [payments, setPayments] = useState<OtherPayment[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchPayments() {
      try {
        setIsLoading(true);
        setError(null);

        if (!studentId) {
          throw new Error('معرف الطالب غير موجود');
        }

        const { data, error } = await supabase
          .from('other_payments')
          .select('*')
          .eq('student_id', studentId)
          .order('created_at', { ascending: false });

        if (error) {
          throw new Error(error.message);
        }

        setPayments(data || []);
      } catch (err: any) {
        console.error('Error fetching other payments:', err);
        setError(err.message || 'حدث خطأ أثناء جلب بيانات المدفوعات الأخرى');
      } finally {
        setIsLoading(false);
      }
    }

    fetchPayments();
  }, [studentId]);

  // تنسيق التاريخ
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-EG');
  };

  // تنسيق المبلغ
  const formatAmount = (amount: number | null | undefined) => {
    if (amount === null || amount === undefined) {
      return '0 جنيه مصري';
    }
    return amount.toLocaleString('ar-EG', { style: 'currency', currency: 'EGP' });
  };

  // الحصول على أنواع المدفوعات
  const getPaymentTypes = (payment: OtherPayment) => {
    const types = [];

    if (payment.file_opening_fee > 0) {
      types.push('رسوم فتح الملف');
    }

    if (payment.books_fee > 0) {
      types.push('رسوم الكتب');
    }

    if (payment.uniform_total > 0) {
      types.push('الزي المدرسي');
    }

    if (payment.transportation_fee > 0) {
      types.push('رسوم النقل');
    }

    return types.length > 0 ? types.join(', ') : 'مدفوعات أخرى';
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center p-8">
        <div className="relative">
          <div className="w-12 h-12 border-4 border-[#384AD7]/30 border-t-[#384AD7] rounded-full animate-spin"></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
            <FaMoneyCheckAlt size={16} className="text-[#384AD7]" />
          </div>
        </div>
        <p className="mr-3 text-gray-600">جاري تحميل البيانات...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-4 bg-[#FD1361]/10 border-r-4 border-[#FD1361] rounded-md">
        <p className="text-[#FD1361]">{error}</p>
      </div>
    );
  }

  if (payments.length === 0) {
    return (
      <div className="p-4 bg-gray-100 border-r-4 border-gray-400 rounded-md">
        <p className="text-gray-600">لا توجد مدفوعات أخرى للطالب</p>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg p-4">
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                أنواع المدفوعات
              </th>
              <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                رسوم فتح الملف
              </th>
              <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                رسوم الكتب
              </th>
              <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                الزي المدرسي
              </th>
              <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                رسوم النقل
              </th>
              <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                المبلغ الإجمالي
              </th>
              <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                تاريخ الإنشاء
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {payments.map((payment) => (
              <tr key={payment.id}>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    <div className="w-8 h-8 rounded-full bg-[#384AD7]/10 flex items-center justify-center text-[#384AD7] ml-2">
                      <FaMoneyCheckAlt size={16} />
                    </div>
                    <span className="text-sm text-gray-900">{getPaymentTypes(payment)}</span>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    <FaMoneyBillWave size={16} className="text-[#FD1361] ml-2" />
                    <span className="text-sm text-gray-900">{formatAmount(payment.file_opening_fee)}</span>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    <FaMoneyBillWave size={16} className="text-[#0ABB87] ml-2" />
                    <span className="text-sm text-gray-900">{formatAmount(payment.books_fee)}</span>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    <FaMoneyBillWave size={16} className="text-[#5578EB] ml-2" />
                    <span className="text-sm text-gray-900">{formatAmount(payment.uniform_total)} ({payment.uniform_count} قطعة)</span>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    <FaMoneyBillWave size={16} className="text-[#384AD7] ml-2" />
                    <span className="text-sm text-gray-900">{formatAmount(payment.transportation_fee)}</span>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    <FaMoneyBillWave size={16} className="text-[#FD1361] ml-2" />
                    <span className="text-sm font-bold text-gray-900">{formatAmount(payment.total_amount)}</span>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    <FaCalendarAlt size={16} className="text-[#0ABB87] ml-2" />
                    <span className="text-sm text-gray-900">{formatDate(payment.created_at)}</span>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default ViewOtherPayments;
