/* 
 * cursor.css
 * ملف أنماط مخصص لتحسين مؤشر الماوس عند لمس العناصر القابلة للنقر
 */

/* الروابط */
a, 
a * {
  cursor: pointer !important;
}

/* الأزرار */
button,
button *,
[role="button"],
[role="button"] *,
[type="button"],
[type="submit"],
[type="reset"],
.btn,
.btn * {
  cursor: pointer !important;
}

/* عناصر الإدخال القابلة للنقر */
input[type="checkbox"],
input[type="radio"],
input[type="file"],
input[type="color"],
input[type="range"],
select,
option,
label[for],
summary {
  cursor: pointer !important;
}

/* أيقونات قابلة للنقر */
.clickable,
.clickable *,
[data-clickable="true"],
[data-clickable="true"] * {
  cursor: pointer !important;
}

/* عناصر القائمة */
.menu-item,
.menu-item *,
.nav-item,
.nav-item *,
.sidebar-item,
.sidebar-item * {
  cursor: pointer !important;
}

/* أيقونات React */
svg[role="button"],
svg.clickable,
svg.interactive {
  cursor: pointer !important;
}

/* عناصر الجدول القابلة للنقر */
th[role="button"],
td[role="button"],
tr[data-clickable="true"],
tr.clickable {
  cursor: pointer !important;
}

/* عناصر الفورم القابلة للنقر */
.form-check,
.form-check *,
.form-radio,
.form-radio * {
  cursor: pointer !important;
}

/* عناصر التبديل */
.toggle,
.toggle *,
.switch,
.switch * {
  cursor: pointer !important;
}

/* عناصر الاختيار */
.dropdown,
.dropdown *,
.select,
.select * {
  cursor: pointer !important;
}

/* عناصر التنقل */
.pagination *,
.breadcrumb *,
.tabs *,
.nav-tabs * {
  cursor: pointer !important;
}

/* عناصر الإغلاق */
.close,
.close *,
.dismiss,
.dismiss * {
  cursor: pointer !important;
}

/* عناصر الإجراءات */
.action-btn,
.action-btn *,
.action-icon,
.action-icon * {
  cursor: pointer !important;
}

/* عناصر البطاقات القابلة للنقر */
.card.clickable,
.card.clickable *,
.card[data-clickable="true"],
.card[data-clickable="true"] * {
  cursor: pointer !important;
}

/* عناصر القائمة المنسدلة */
.dropdown-item,
.dropdown-item *,
.dropdown-toggle,
.dropdown-toggle * {
  cursor: pointer !important;
}

/* عناصر الملفات */
.file-upload,
.file-upload *,
.file-input,
.file-input * {
  cursor: pointer !important;
}

/* عناصر الصور القابلة للنقر */
img.clickable,
img[role="button"],
img[data-clickable="true"] {
  cursor: pointer !important;
}

/* عناصر الأيقونات */
.icon.clickable,
.icon[role="button"],
.material-icons {
  cursor: pointer !important;
}

/* عناصر التوسيع والطي */
.accordion-button,
.accordion-button *,
.collapse-toggle,
.collapse-toggle * {
  cursor: pointer !important;
}

/* عناصر التحقق */
.checkbox,
.checkbox *,
.radio,
.radio * {
  cursor: pointer !important;
}

/* عناصر الفرز */
.sortable,
.sortable *,
.sort-icon,
.sort-icon * {
  cursor: pointer !important;
}

/* عناصر التصفية */
.filter,
.filter *,
.filter-icon,
.filter-icon * {
  cursor: pointer !important;
}

/* عناصر الصفحات */
.page-item,
.page-item *,
.page-link,
.page-link * {
  cursor: pointer !important;
}

/* عناصر الإجراءات في الجداول */
.table-action,
.table-action * {
  cursor: pointer !important;
}

/* عناصر الخطوات */
.step-item.clickable,
.step-item.clickable * {
  cursor: pointer !important;
}
