# دليل إعداد قاعدة البيانات

## المشكلة

تم اكتشاف خطأ في الوصول إلى جدول المراحل الدراسية (`grades`) في قاعدة البيانات. الخطأ الذي ظهر هو:

```
Error: Error checking tables: {}
Error: Error with alternative table name: {}
Error: Error in fetchGrades: "لا يمكن الوصول إلى جدول المراحل الدراسية"
```

## السبب

المشكلة ناتجة عن أحد الأسباب التالية:

1. لم يتم إنشاء جدول `grades` في قاعدة البيانات Supabase.
2. لم يتم تنفيذ ملف SQL الخاص بإنشاء الجداول وإدخال البيانات.
3. مكون `GradeSelector` كان يحاول الوصول إلى جدول باسم `school_grades` بدلاً من `grades`.

## الحل

تم تحديث مكون `GradeSelector` ليستخدم اسم الجدول الصحيح `grades` كما هو محدد في ملف SQL. ولكن لضمان عمل التطبيق بشكل صحيح، يجب اتباع الخطوات التالية:

### 1. إعداد قاعدة البيانات في Supabase

1. قم بتسجيل الدخول إلى لوحة تحكم Supabase الخاصة بك على الرابط: https://app.supabase.com
2. اختر المشروع الخاص بك
3. انتقل إلى قسم SQL Editor
4. قم بتنفيذ ملف `run-all.sql` الموجود في مجلد `sql` في المشروع

### 2. تنفيذ ملف SQL

يمكنك نسخ محتوى ملف `run-all.sql` ولصقه في محرر SQL في Supabase ثم الضغط على زر Run لتنفيذ الأوامر. هذا سيقوم بإنشاء الجداول وإدخال البيانات الأساسية.

### 3. التحقق من إنشاء الجداول

بعد تنفيذ الملف، يمكنك التحقق من إنشاء الجداول بنجاح عن طريق:

1. الانتقال إلى قسم Table Editor في Supabase
2. التأكد من وجود جدول `grades` وجدول `classes`
3. التأكد من وجود بيانات في هذه الجداول

### 4. التحقق من متغيرات البيئة

تأكد من أن ملف `.env.local` يحتوي على معلومات الاتصال الصحيحة لـ Supabase:

```
NEXT_PUBLIC_SUPABASE_URL=https://your-project-url.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
```

## ملاحظات إضافية

- إذا كنت تستخدم قاعدة بيانات PostgreSQL محلية، يمكنك تنفيذ ملف SQL باستخدام الأمر التالي:

```bash
psql -U username -d database_name -f run-all.sql
```

- تأكد من أن لديك الصلاحيات الكافية للوصول إلى قاعدة البيانات وإنشاء الجداول.
- يمكنك الاطلاع على ملف `PORTABLE-SQL-GUIDE.md` في مجلد `sql` للحصول على مزيد من المعلومات حول استخدام SQL القياسي في المشروع.

## الاستعلامات الأساسية

يمكنك استخدام الاستعلامات التالية للتحقق من البيانات في قاعدة البيانات:

```sql
-- للاستعلام عن المراحل الدراسية
SELECT * FROM grades;

-- للاستعلام عن الفصول الدراسية
SELECT c.*, g.name as grade_name FROM classes c JOIN grades g ON c.grade_id = g.id;
```