{"name": "school-management-system", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@supabase/supabase-js": "^2.49.4", "@tanstack/react-query": "^5.75.2", "@tanstack/react-table": "^8.21.3", "date-fns": "^4.1.0", "formik": "^2.4.6", "google-auth-library": "^9.15.1", "google-spreadsheet": "^4.1.4", "next": "15.3.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-icons": "^5.5.0", "react-to-print": "^3.1.0", "recharts": "^2.15.3", "xlsx": "^0.18.5", "yup": "^1.6.1", "zustand": "^5.0.4"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.1", "tailwindcss": "^4", "typescript": "^5"}}