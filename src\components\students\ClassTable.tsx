import React, { useMemo } from 'react';
import { ColumnDef } from '@tanstack/react-table';
import { DataTable } from '../table/DataTable';
import { ActionButtons } from '../table/ActionButtons';
import { Class } from '@/types/table.types';

interface ClassTableProps {
  data: Class[];
  isLoading?: boolean;
  onView?: (classItem: Class) => void;
  onEdit?: (classItem: Class) => void;
  onDelete?: (classItem: Class) => void;
}

export const ClassTable: React.FC<ClassTableProps> = ({
  data,
  isLoading = false,
  onView,
  onEdit,
  onDelete,
}) => {
  // تعريف أعمدة الجدول - استخدام مصفوفة تبعيات ثابتة
  const columns = useMemo<ColumnDef<Class, any>[]>(
    () => [
      {
        id: 'name',
        header: 'اسم الفصل',
        accessorKey: 'name',
        cell: ({ row }) => (
          <div className="font-medium text-gray-900">{row.original.name}</div>
        ),
        meta: {
          color: '#21ADE7', // أزرق فاتح
        },
      },
      {
        id: 'grade_name',
        header: 'المرحلة الدراسية',
        accessorKey: 'grade_name',
        meta: {
          color: '#5578EB', // أزرق داكن
        },
      },
      {
        id: 'capacity',
        header: 'السعة',
        accessorKey: 'capacity',
        meta: {
          color: '#0ABB87', // أخضر
        },
      },
      {
        id: 'actions',
        header: 'الإجراءات',
        cell: ({ row }) => (
          <ActionButtons
            onView={onView ? () => onView(row.original) : undefined}
            onEdit={onEdit ? () => onEdit(row.original) : undefined}
            onDelete={onDelete ? () => onDelete(row.original) : undefined}
            itemName={`الفصل "${row.original.name}"`}
          />
        ),
        meta: {
          color: '#21ADE7', // أزرق فاتح
        },
      },
    ],
    // استخدام مصفوفة تبعيات ثابتة لتجنب مشاكل إعادة التصيير
    // eslint-disable-next-line react-hooks/exhaustive-deps
    []
  );

  return (
    <DataTable
      columns={columns}
      data={data}
      isLoading={isLoading}
      showSearch={true}
      searchPlaceholder="بحث..."
      emptyMessage="لا يوجد فصول"
      className="border-r-4 border-[#5578EB]"
    />
  );
};

export default ClassTable;
