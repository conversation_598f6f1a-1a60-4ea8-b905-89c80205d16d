import { NextRequest, NextResponse } from 'next/server';
import { GoogleSpreadsheet } from 'google-spreadsheet';
import { createJWT } from '@/lib/googleSheetsClient';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
);

// إنشاء جدول بيانات جديد في Google Sheets
const createNewSpreadsheet = async (title: string) => {
  try {
    const jwt = createJWT();
    const doc = new GoogleSpreadsheet('', jwt);
    
    // إنشاء جدول بيانات جديد
    await doc.createNewSpreadsheetDocument({ title });
    
    return doc;
  } catch (error) {
    console.error('Error creating new spreadsheet:', error);
    throw new Error('فشل إنشاء جدول بيانات جديد');
  }
};

// تصدير البيانات إلى Google Sheets
const exportToGoogleSheets = async (data: any[], title: string) => {
  try {
    // إنشاء جدول بيانات جديد
    const doc = await createNewSpreadsheet(title);
    
    // الحصول على الورقة الافتراضية
    const sheet = doc.sheetsByIndex[0];
    
    // تغيير اسم الورقة
    await sheet.updateProperties({ title: 'بيانات الطلاب' });
    
    // إضافة الصفوف
    await sheet.setHeaderRow(Object.keys(data[0]));
    await sheet.addRows(data);
    
    // إضافة ورقة جديدة للنموذج
    const templateSheet = await doc.addSheet({ title: 'نموذج إدخال البيانات' });
    
    // إضافة رؤوس الأعمدة في ورقة النموذج
    await templateSheet.setHeaderRow(Object.keys(data[0]));
    
    // إضافة صف فارغ كنموذج
    await templateSheet.addRow({
      'الاسم الكامل': '',
      'رقم الهوية': '',
      'تاريخ الميلاد': '',
      'الجنس': 'ذكر/أنثى',
      'الديانة': 'الإسلام/المسيحية',
      'الحالة الاجتماعية': '',
      'البريد الإلكتروني': '',
      'رقم الهاتف': '',
      'اسم ولي الأمر': '',
      'صلة القرابة': '',
      'هاتف ولي الأمر': '',
      'الرسوم الدراسية': '',
      'مبلغ الخصم': '',
      'المبلغ المدفوع': '',
      'طريقة الدفع': 'نقدي/بنكي',
    });
    
    return {
      spreadsheetId: doc.spreadsheetId,
      spreadsheetUrl: `https://docs.google.com/spreadsheets/d/${doc.spreadsheetId}/edit`,
    };
  } catch (error) {
    console.error('Error exporting to Google Sheets:', error);
    throw new Error('فشل تصدير البيانات إلى Google Sheets');
  }
};

export async function GET() {
  try {
    // استرجاع بيانات الطلاب من قاعدة البيانات
    const { data: students, error: studentsError } = await supabase
      .from('students')
      .select(`
        *,
        academic_records (
          grade_id,
          class_id,
          academic_year,
          school_grades (name),
          school_classes (name)
        ),
        financial_info (*),
        guardians (*)
      `);

    if (studentsError) {
      throw new Error('فشل استرجاع بيانات الطلاب');
    }

    if (!students || students.length === 0) {
      return NextResponse.json(
        { error: 'لا توجد بيانات طلاب للتصدير' },
        { status: 404 }
      );
    }

    // تحويل البيانات إلى تنسيق مناسب للتصدير
    const exportData = students.map(student => ({
      'الاسم الكامل': student.full_name,
      'رقم الهوية': student.id_number,
      'تاريخ الميلاد': student.birth_date,
      'الجنس': student.gender,
      'الديانة': student.religion,
      'الحالة الاجتماعية': student.marital_status,
      'البريد الإلكتروني': student.email,
      'رقم الهاتف': student.phone,
      'اسم ولي الأمر': student.guardians?.[0]?.full_name || '',
      'صلة القرابة': student.guardians?.[0]?.relationship || '',
      'هاتف ولي الأمر': student.guardians?.[0]?.phone || '',
      'المرحلة الدراسية': student.academic_records?.[0]?.school_grades?.name || '',
      'الفصل الدراسي': student.academic_records?.[0]?.school_classes?.name || '',
      'العام الدراسي': student.academic_records?.[0]?.academic_year || '',
      'الرسوم الدراسية': student.financial_info?.[0]?.tuition_fee || 0,
      'مبلغ الخصم': student.financial_info?.[0]?.discount_amount || 0,
      'سبب الخصم': student.financial_info?.[0]?.discount_reason || '',
      'المبلغ المدفوع': student.financial_info?.[0]?.paid_amount || 0,
      'المبلغ المتبقي': student.financial_info?.[0]?.remaining_amount || 0,
      'طريقة الدفع': student.financial_info?.[0]?.payment_method || '',
      'تاريخ التسجيل': new Date(student.created_at).toLocaleDateString('ar-EG'),
    }));

    // تصدير البيانات إلى Google Sheets
    const result = await exportToGoogleSheets(
      exportData,
      `بيانات طلاب مدرسة الجيل الواعد - ${new Date().toLocaleDateString('ar-EG')}`
    );

    return NextResponse.json({
      success: true,
      data: result,
      message: `تم تصدير ${exportData.length} طالب إلى Google Sheets بنجاح`,
      studentsCount: exportData.length
    });

  } catch (error) {
    console.error('Error in export API:', error);
    
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : 'حدث خطأ أثناء تصدير البيانات',
        details: error instanceof Error ? error.stack : undefined
      },
      { status: 500 }
    );
  }
}
