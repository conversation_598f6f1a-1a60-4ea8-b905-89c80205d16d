"use client";

import React from 'react';

interface TuitionFeeSectionProps {
  tuitionFee: {
    value: string;
    onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
    onBlur: (e: React.FocusEvent<HTMLElement>) => void;
    error?: string;
    touched?: boolean | any;
  };
  discountAmount: {
    value: string | number;
    onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
    onBlur: (e: React.FocusEvent<HTMLElement>) => void;
    error?: string;
    touched?: boolean | any;
  };
  discountReason: {
    value: string;
    onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
    onBlur: (e: React.FocusEvent<HTMLElement>) => void;
    error?: string;
    touched?: boolean | any;
  };
  hasDiscount: boolean;
}

const TuitionFeeSection: React.FC<TuitionFeeSectionProps> = ({
  tuitionFee,
  discountAmount,
  discountReason,
  hasDiscount
}) => {
  return (
    <>
      {/* قيمة الرسوم الدراسية */}
      <div>
        <label htmlFor="tuitionFee" className="block text-gray-700 font-medium mb-2">
          قيمة الرسوم الدراسية <span className="text-danger">*</span>
        </label>
        <input
          type="number"
          id="tuitionFee"
          name="tuitionFee"
          className={`w-full p-3 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary
            ${tuitionFee.touched && tuitionFee.error ? 'border-danger' : 'border-gray-300'}`}
          onChange={tuitionFee.onChange}
          onBlur={tuitionFee.onBlur}
          value={tuitionFee.value}
          placeholder="أدخل قيمة الرسوم الدراسية"
        />
        {tuitionFee.touched && tuitionFee.error && (
          <p className="mt-1 text-danger text-sm">{tuitionFee.error}</p>
        )}
      </div>

      {/* قيمة الخصم */}
      <div>
        <label htmlFor="discountAmount" className="block text-gray-700 font-medium mb-2">
          قيمة الخصم
        </label>
        <input
          type="number"
          id="discountAmount"
          name="discountAmount"
          className={`w-full p-3 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary
            ${discountAmount.touched && discountAmount.error ? 'border-danger' : 'border-gray-300'}`}
          onChange={discountAmount.onChange}
          onBlur={discountAmount.onBlur}
          value={discountAmount.value}
          placeholder="أدخل قيمة الخصم"
        />
        {discountAmount.touched && discountAmount.error && (
          <p className="mt-1 text-danger text-sm">{discountAmount.error}</p>
        )}
      </div>

      {/* سبب الخصم */}
      <div>
        <label htmlFor="discountReason" className="block text-gray-700 font-medium mb-2">
          سبب الخصم {hasDiscount && <span className="text-danger">*</span>}
        </label>
        <input
          type="text"
          id="discountReason"
          name="discountReason"
          className={`w-full p-3 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary
            ${discountReason.touched && discountReason.error ? 'border-danger' : 'border-gray-300'}`}
          onChange={discountReason.onChange}
          onBlur={discountReason.onBlur}
          value={discountReason.value}
          placeholder="أدخل سبب الخصم"
          disabled={!hasDiscount}
        />
        {discountReason.touched && discountReason.error && (
          <p className="mt-1 text-danger text-sm">{discountReason.error}</p>
        )}
      </div>
    </>
  );
};

export default TuitionFeeSection;
