import { NextResponse } from 'next/server';
import * as XLSX from 'xlsx';

export async function GET() {
  try {
    // إنشاء نموذج فارغ بأسماء الحقول بالعربية
    const template = {
      'الاسم الكامل': '',
      'رقم الهوية': '',
      'تاريخ الميلاد': '',
      'الجنس': 'ذكر/أنثى',
      'الديانة': 'الإسلام/المسيحية',
      'الحالة الاجتماعية': '',
      'البريد الإلكتروني': '',
      'رقم الهاتف': '',
    };

    // إنشاء ورقة عمل Excel
    const worksheet = XLSX.utils.json_to_sheet([template]);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, 'نموذج_بيانات_الطالب');
    
    // تحويل الملف إلى مصفوفة ثنائية
    const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'buffer' });
    
    // إنشاء استجابة مع الملف
    return new NextResponse(excelBuffer, {
      headers: {
        'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'Content-Disposition': 'attachment; filename="نموذج_بيانات_الطالب.xlsx"',
      },
    });
  } catch (error) {
    console.error('Error generating Excel template:', error);
    return NextResponse.json({ error: 'فشل إنشاء نموذج Excel' }, { status: 500 });
  }
}
