"use client";

import React, { useState, ReactNode } from 'react';
import {
  FaUser, FaUserFriends, FaMoneyBillWave, FaCalendarAlt,
  FaMoneyCheckAlt, FaFileInvoiceDollar, FaHeartbeat,
  FaInfoCircle, FaPaperclip
} from 'react-icons/fa';

interface Step {
  id: number;
  title: string;
  component: ReactNode;
  isRequired: boolean;
  icon?: ReactNode; // إضافة أيقونة اختيارية
  color?: string; // إضافة لون اختياري
}

interface StepWizardProps {
  steps: Step[];
  onComplete: (data: any) => void;
  initialData?: Record<string, any>;
}

// دالة للحصول على الأيقونة الافتراضية لكل خطوة
const getDefaultStepIcon = (stepId: number): ReactNode => {
  switch (stepId) {
    case 1: return <FaUser size={20} />;
    case 2: return <FaUserFriends size={20} />;
    case 3: return <FaMoneyBillWave size={20} />;
    case 4: return <FaCalendarAlt size={20} />;
    case 5: return <FaMoneyCheckAlt size={20} />;
    case 6: return <FaFileInvoiceDollar size={20} />;
    case 7: return <FaHeartbeat size={20} />;
    case 8: return <FaInfoCircle size={20} />;
    case 9: return <FaPaperclip size={20} />;
    default: return <FaInfoCircle size={20} />;
  }
};

// دالة للحصول على اللون الافتراضي لكل خطوة
const getDefaultStepColor = (stepId: number): string => {
  switch (stepId) {
    case 1: return '#21ADE7'; // أزرق فاتح
    case 2: return '#5578EB'; // أزرق داكن
    case 3: return '#FD1361'; // أحمر
    case 4: return '#0ABB87'; // أخضر
    case 5: return '#384AD7'; // أزرق غامق
    case 6: return '#21ADE7'; // أزرق فاتح
    case 7: return '#FD1361'; // أحمر
    case 8: return '#5578EB'; // أزرق داكن
    case 9: return '#0ABB87'; // أخضر
    default: return '#21ADE7'; // أزرق فاتح
  }
};

const StepWizard: React.FC<StepWizardProps> = ({ steps, onComplete, initialData }) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [formData, setFormData] = useState<Record<string, any>>(initialData || {});
  const [stepsCompleted, setStepsCompleted] = useState<Record<number, boolean>>({});

  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleStepComplete = (stepId: number, data: any, isValid: boolean) => {
    // استخدام الدالة الوظيفية لتحديث الحالة لتجنب الاعتماد على القيمة السابقة
    setFormData(prevData => {
      // مقارنة البيانات الجديدة مع البيانات السابقة لتجنب التحديثات غير الضرورية
      const isEqual = JSON.stringify(prevData) === JSON.stringify({ ...prevData, ...data });
      if (isEqual) {
        return prevData; // إرجاع نفس الكائن إذا لم تتغير البيانات
      }
      return { ...prevData, ...data };
    });

    // تحديث حالة إكمال الخطوات فقط إذا تغيرت
    setStepsCompleted(prevCompleted => {
      if (prevCompleted[stepId] === isValid) {
        return prevCompleted; // إرجاع نفس الكائن إذا لم تتغير الحالة
      }
      return { ...prevCompleted, [stepId]: isValid };
    });
  };

  const handleSubmit = () => {
    onComplete(formData);
  };

  const isStepValid = (stepId: number) => {
    return stepsCompleted[stepId] || false;
  };

  // تعديل دالة التحقق من تعطيل زر التالي
  // لا نريد تعطيل الزر حتى يتمكن المستخدم من التنقل بحرية
  const isNextButtonDisabled = () => {
    // يمكن للمستخدم الانتقال إلى الخطوة التالية دائمًا
    return false;
  };

  // تعديل دالة التحقق من تعطيل زر الحفظ والإنهاء
  // يجب أن تكون جميع الخطوات الإلزامية مكتملة قبل السماح بالحفظ
  const isSubmitButtonDisabled = () => {
    // التحقق من اكتمال جميع الخطوات الإلزامية
    const requiredStepsCompleted = steps
      .filter(step => step.isRequired)
      .every(step => isStepValid(step.id));

    return !requiredStepsCompleted;
  };

  // الحصول على الخطوات الإلزامية غير المكتملة
  const getIncompleteRequiredSteps = () => {
    return steps
      .filter(step => step.isRequired && !isStepValid(step.id))
      .map(step => ({
        title: step.title,
        index: steps.findIndex(s => s.id === step.id)
      }));
  };

  const handleStepClick = (index: number) => {
    // السماح بالتنقل بين الخطوات بغض النظر عن حالة الإكمال
    // هذا يسمح للمستخدم بالعودة إلى الخطوات السابقة لتعديل البيانات
    setCurrentStep(index);
  };

  const renderStepIndicators = () => {
    return (
      <div className="flex justify-between items-center mb-8 px-4">
        {steps.map((step, index) => {
          // الحصول على الأيقونة واللون لكل خطوة
          const stepIcon = step.icon || getDefaultStepIcon(step.id);
          const stepColor = step.color || getDefaultStepColor(step.id);

          // تحديد لون الخلفية بناءً على حالة الخطوة
          const bgColor = index < currentStep
            ? '#0ABB87' // خطوة مكتملة
            : index === currentStep
              ? stepColor // الخطوة الحالية
              : 'rgba(209, 213, 219, 0.8)'; // خطوة غير مكتملة

          return (
            <div
              key={step.id}
              className="flex flex-col items-center cursor-pointer"
              onClick={() => handleStepClick(index)}
            >
              <div
                className="w-12 h-12 rounded-full flex items-center justify-center text-white font-bold shadow-md hover:shadow-lg hover:opacity-90 transition-all duration-200 clickable"
                style={{ backgroundColor: bgColor }}
              >
                {stepIcon}
              </div>
              <div className="flex flex-col items-center">
                <span
                  className={`text-xs mt-2 text-center max-w-[80px] font-medium
                    ${index === currentStep ? 'font-bold' : ''}`}
                  style={{ color: index === currentStep ? stepColor : '#6B7280' }}
                >
                  {step.title}
                </span>
                {step.isRequired && (
                  <span className="text-[#FD1361] text-[10px] mt-1">
                    (مطلوب)
                  </span>
                )}
              </div>
            </div>
          );
        })}
      </div>
    );
  };

  const renderProgressBar = () => {
    const progress = ((currentStep + 1) / steps.length) * 100;
    const currentStepColor = steps[currentStep]?.color || getDefaultStepColor(steps[currentStep]?.id);

    return (
      <div className="w-full bg-gray-200 rounded-full h-3 mb-6 shadow-inner">
        <div
          className="h-3 rounded-full transition-all duration-300 ease-in-out"
          style={{
            width: `${progress}%`,
            background: `linear-gradient(to right, #0ABB87, ${currentStepColor})`
          }}
        ></div>
      </div>
    );
  };

  // استخدام نوع أكثر تحديدًا للمكون
  const currentStepComponent = React.cloneElement(
    steps[currentStep].component as React.ReactElement<{
      onComplete: (data: any, isValid: boolean) => void;
      formData: Record<string, any>;
    }>,
    {
      onComplete: (data: any, isValid: boolean) =>
        handleStepComplete(steps[currentStep].id, data, isValid),
      formData
    }
  );

  return (
    <div className="bg-white rounded-lg shadow-lg p-6 border-r-4"
      style={{ borderColor: steps[currentStep]?.color || getDefaultStepColor(steps[currentStep]?.id) }}>
      {renderProgressBar()}
      {renderStepIndicators()}

      <div className="mb-6 p-4 rounded-lg"
        style={{ backgroundColor: `${steps[currentStep]?.color || getDefaultStepColor(steps[currentStep]?.id)}10` }}>
        <div className="flex items-center mb-4">
          <div className="w-10 h-10 rounded-full flex items-center justify-center text-white mr-3"
            style={{ backgroundColor: steps[currentStep]?.color || getDefaultStepColor(steps[currentStep]?.id) }}>
            {steps[currentStep]?.icon || getDefaultStepIcon(steps[currentStep]?.id)}
          </div>
          <h2 className="text-xl font-bold"
            style={{ color: steps[currentStep]?.color || getDefaultStepColor(steps[currentStep]?.id) }}>
            {steps[currentStep]?.title}
          </h2>
        </div>
        {currentStepComponent}
      </div>

      <div className="flex justify-between mt-8">
        <button
          onClick={handlePrevious}
          disabled={currentStep === 0}
          className={`px-4 py-2 rounded-md shadow-sm ${
            currentStep === 0
              ? 'bg-gray-300 cursor-not-allowed'
              : 'hover:opacity-90 text-white font-medium transition-all duration-200 clickable'
          }`}
          style={{
            backgroundColor: currentStep === 0
              ? '#D1D5DB'
              : `${steps[Math.max(0, currentStep-1)]?.color || getDefaultStepColor(steps[Math.max(0, currentStep-1)]?.id)}`
          }}
        >
          السابق
        </button>

        <div className="flex gap-2">
          {/* زر التالي يظهر دائمًا ما لم نكن في الخطوة الأخيرة */}
          {currentStep < steps.length - 1 && (
            <button
              onClick={handleNext}
              disabled={isNextButtonDisabled()}
              className={`px-4 py-2 rounded-md shadow-sm ${
                isNextButtonDisabled()
                  ? 'bg-gray-300 cursor-not-allowed'
                  : 'hover:opacity-90 text-white font-medium transition-all duration-200 clickable'
              }`}
              style={{
                backgroundColor: isNextButtonDisabled()
                  ? '#D1D5DB'
                  : `${steps[Math.min(steps.length-1, currentStep+1)]?.color || getDefaultStepColor(steps[Math.min(steps.length-1, currentStep+1)]?.id)}`
              }}
            >
              التالي
            </button>
          )}

          {/* زر الحفظ والإنهاء يظهر فقط في الخطوة الأخيرة */}
          {currentStep === steps.length - 1 && (
            <div className="flex flex-col items-end">
              <button
                onClick={handleSubmit}
                disabled={isSubmitButtonDisabled()}
                className={`px-4 py-2 rounded-md shadow-sm ${
                  isSubmitButtonDisabled()
                    ? 'bg-gray-300 cursor-not-allowed'
                    : 'hover:opacity-90 text-white font-medium transition-all duration-200 clickable'
                }`}
                style={{ backgroundColor: isSubmitButtonDisabled() ? '#D1D5DB' : '#0ABB87' }}
              >
                حفظ وإنهاء
              </button>

              {isSubmitButtonDisabled() && (
                <div className="mt-2 text-[#FD1361] text-sm max-w-md">
                  <p>يرجى إكمال الخطوات الإلزامية التالية:</p>
                  <ul className="list-disc mr-5 mt-1">
                    {getIncompleteRequiredSteps().map((step, index) => (
                      <li
                        key={index}
                        className="cursor-pointer hover:underline"
                        onClick={() => handleStepClick(step.index)}
                      >
                        {step.title} (انقر للانتقال)
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default StepWizard;
