import { useEffect, useState, useRef } from 'react';
import { RealtimeChannel } from '@supabase/supabase-js';
import { supabase } from '@/lib/supabase';

type SupabaseRealtimeEvent = 'INSERT' | 'UPDATE' | 'DELETE' | '*';

interface UseSupabaseRealtimeOptions {
  table: string;
  event?: SupabaseRealtimeEvent;
  filter?: string;
  filterValue?: any;
  schema?: string;
}

/**
 * Custom hook for subscribing to Supabase real-time changes
 *
 * @param options Configuration options for the real-time subscription
 * @param callback Function to call when data changes
 * @returns Object containing subscription status and cleanup function
 */
export function useSupabaseRealtime<T = any>(
  options: UseSupabaseRealtimeOptions,
  callback: (payload: { new: T; old: T; eventType: string }) => void
) {
  const [isSubscribed, setIsSubscribed] = useState(false);
  const [channel, setChannel] = useState<RealtimeChannel | null>(null);
  const [error, setError] = useState<Error | null>(null);
  const callbackRef = useRef(callback);

  // Update the callback ref when the callback changes
  useEffect(() => {
    callbackRef.current = callback;
  }, [callback]);

  // استخدام useRef للاحتفاظ بالخيارات الحالية والتحكم في الإعداد
  const optionsRef = useRef(options);
  const setupDoneRef = useRef(false);

  // تحديث المرجع عند تغيير الخيارات
  useEffect(() => {
    optionsRef.current = options;
  }, [options]);

  // إعداد الاشتراك مرة واحدة فقط
  useEffect(() => {
    // تجنب إعادة الإعداد إذا تم بالفعل
    if (setupDoneRef.current) return;

    console.log('Setting up Supabase real-time subscription for table:', options.table);

    // إنشاء اسم فريد للقناة
    const channelName = `${options.table}${options.filter ? `-${options.filter}-${options.filterValue}` : ''}`;

    // إنشاء القناة
    const realtimeChannel = supabase.channel(channelName);

    try {
      // إعداد الاشتراك
      realtimeChannel.on(
        'postgres_changes',
        {
          event: options.event || '*',
          schema: options.schema || 'public',
          table: options.table,
          ...(options.filter && options.filterValue
            ? { filter: `${options.filter}=eq.${options.filterValue}` }
            : {})
        },
        (payload) => {
          console.log(`Received ${payload.eventType} event for table ${optionsRef.current.table}:`, payload);
          // استخدام setTimeout لتجنب التحديثات المتزامنة
          setTimeout(() => {
            // استخدام المرجع الحالي للدالة
            callbackRef.current({
              new: payload.new as T,
              old: payload.old as T,
              eventType: payload.eventType
            });
          }, 100);
        }
      );

      // الاشتراك في القناة
      realtimeChannel.subscribe((status) => {
        console.log(`Subscription status for ${options.table}:`, status);
        if (status === 'SUBSCRIBED') {
          setIsSubscribed(true);
          setupDoneRef.current = true;
        } else {
          setIsSubscribed(false);
        }
      });

      setChannel(realtimeChannel);
    } catch (err) {
      console.error('Error setting up Supabase real-time subscription:', err);
      setError(err instanceof Error ? err : new Error('Unknown error'));
      setIsSubscribed(false);
      setupDoneRef.current = false;
    }

    // دالة التنظيف
    return () => {
      console.log('Cleaning up Supabase real-time subscription for table:', options.table);
      if (realtimeChannel) {
        supabase.removeChannel(realtimeChannel);
      }
      setIsSubscribed(false);
      setChannel(null);
      setupDoneRef.current = false;
    };
  // استخدام مصفوفة تبعيات فارغة لتنفيذ الدالة مرة واحدة فقط
  // نحن نستخدم المراجع للوصول إلى أحدث القيم
  }, []);

  return { isSubscribed, error, channel };
}
