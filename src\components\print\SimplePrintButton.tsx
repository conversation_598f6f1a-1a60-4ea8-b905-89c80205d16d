'use client';

import React from 'react';
import { FaPrint } from 'react-icons/fa';
import { Student, Guardian, FinancialInfo, Installment, OtherPayment } from '@/types/table.types';

interface SimplePrintButtonProps {
  student: Student;
  guardians: Guardian[];
  financialInfo: FinancialInfo;
  installments: Installment[];
  otherPayments: OtherPayment[];
  className?: string;
}

const SimplePrintButton: React.FC<SimplePrintButtonProps> = ({
  student,
  guardians,
  financialInfo,
  installments,
  otherPayments,
  className = ''
}) => {
  // دالة تنسيق التاريخ
  const formatDate = (dateString: string | null | undefined) => {
    if (!dateString) return '';

    try {
      const date = new Date(dateString);
      return new Intl.DateTimeFormat('ar-EG', {
        year: 'numeric',
        month: 'numeric',
        day: 'numeric'
      }).format(date);
    } catch (error) {
      return dateString;
    }
  };

  // دالة تنسيق المبالغ المالية
  const formatCurrency = (amount: number | string | null | undefined) => {
    if (amount === null || amount === undefined) return '0.00 ج.م';
    const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount;
    return `${numAmount.toFixed(2)} ج.م`;
  };

  // حساب الإجماليات
  const calculateTotals = () => {
    const tuitionFee = parseFloat(financialInfo?.tuition_fee?.toString() || '0');
    const discountAmount = parseFloat(financialInfo?.discount_amount?.toString() || '0');
    const paidAmount = parseFloat(financialInfo?.paid_amount?.toString() || '0');
    const netTuition = tuitionFee - discountAmount;

    const totalOtherPayments = otherPayments?.reduce((sum, payment) => {
      return sum + parseFloat(payment.amount?.toString() || '0');
    }, 0) || 0;

    const totalInstallmentsPaid = installments?.reduce((sum, installment) => {
      return sum + parseFloat(installment.paid?.toString() || '0');
    }, 0) || 0;

    const totalPaid = paidAmount + totalInstallmentsPaid;
    const totalDue = netTuition + totalOtherPayments;
    const totalRemaining = totalDue - totalPaid;

    return {
      tuitionFee,
      discountAmount,
      netTuition,
      totalOtherPayments,
      totalPaid,
      totalDue,
      totalRemaining
    };
  };

  // دالة الطباعة
  const handlePrint = () => {
    const { tuitionFee, discountAmount, netTuition, totalOtherPayments, totalPaid, totalDue, totalRemaining } = calculateTotals();

    // إنشاء نافذة جديدة للطباعة
    const printWindow = window.open('', '_blank');
    if (!printWindow) {
      alert('يرجى السماح بفتح النوافذ المنبثقة لتتمكن من الطباعة');
      return;
    }

    // إنشاء محتوى HTML للطباعة
    const printContent = `
      <!DOCTYPE html>
      <html dir="rtl" lang="ar">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>ملخص تسجيل طالب</title>
        <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
        <style>
          body {
            font-family: 'Cairo', sans-serif;
            margin: 0;
            padding: 5px;
            color: #333;
            font-size: 10px;
          }
          .print-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 5px;
            border: 1px solid #ddd;
          }
          .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid #0F2A4A;
            padding-bottom: 5px;
            margin-bottom: 5px;
          }
          .school-info {
            text-align: center;
            flex: 1;
          }
          .school-name {
            font-size: 16px;
            font-weight: bold;
            color: #0F2A4A;
            margin: 0;
          }
          .school-name-en {
            font-size: 12px;
            font-weight: bold;
            margin: 2px 0;
          }
          .school-code {
            color: #0F2A4A;
            margin: 2px 0;
            font-size: 10px;
          }
          .document-title {
            text-align: center;
            margin: 5px 0;
          }
          .document-title h2 {
            font-size: 14px;
            font-weight: bold;
            color: #0F2A4A;
            display: inline-block;
            border-bottom: 1px solid #0F2A4A;
            padding-bottom: 2px;
            margin: 0;
          }
          .document-title p {
            margin: 3px 0 0 0;
            font-size: 9px;
          }
          .section {
            margin-bottom: 5px;
          }
          .section-title {
            font-size: 12px;
            font-weight: bold;
            color: #0F2A4A;
            border-right: 2px solid #0F2A4A;
            padding-right: 3px;
            margin-bottom: 5px;
          }
          .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 3px;
            background-color: #f9f9f9;
            padding: 5px;
            border-radius: 2px;
            border: 1px solid #ddd;
          }
          .info-item {
            display: flex;
            justify-content: space-between;
            font-size: 9px;
          }
          .info-label {
            font-weight: 500;
          }
          .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 3px;
            font-size: 8px;
          }
          .table th, .table td {
            border: 1px solid #ddd;
            padding: 2px;
            text-align: right;
          }
          .table th {
            background-color: #f2f2f2;
            font-weight: 500;
          }
          .signatures {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 10px;
            margin-top: 10px;
          }
          .signature {
            text-align: center;
          }
          .signature-line {
            height: 1px;
            background-color: #333;
            margin-bottom: 3px;
          }
          .signature p {
            margin: 0;
            font-size: 9px;
          }
          .footer {
            margin-top: 8px;
            text-align: center;
            font-size: 8px;
            color: #666;
          }
          @media print {
            body {
              print-color-adjust: exact;
              -webkit-print-color-adjust: exact;
            }
            @page {
              size: A4;
              margin: 0.5cm;
            }
          }
        </style>
      </head>
      <body>
        <div class="print-container">
          <div class="header">
            <div style="width: 100px;"></div>
            <div class="school-info">
              <h1 class="school-name">مدرسة الجيل الواعد</h1>
              <h2 class="school-name-en">Promising Generation School</h2>
              <p class="school-code">P.G.S</p>
            </div>
            <div style="width: 100px;"></div>
          </div>

          <div class="document-title">
            <h2>ملخص تسجيل طالب</h2>
            <p>تاريخ التسجيل: ${formatDate(student.registration_date || new Date().toISOString())}</p>
          </div>

          <div class="section">
            <h3 class="section-title">بيانات الطالب</h3>
            <div class="info-grid">
              <div class="info-item">
                <span class="info-label">الاسم الكامل:</span>
                <span>${student.first_name || ''} ${student.middle_name || ''} ${student.last_name || ''}</span>
              </div>
              <div class="info-item">
                <span class="info-label">رقم الهوية:</span>
                <span>${student.national_id || ''}</span>
              </div>
              <div class="info-item">
                <span class="info-label">تاريخ الميلاد:</span>
                <span>${formatDate(student.birth_date)}</span>
              </div>
              <div class="info-item">
                <span class="info-label">الجنس:</span>
                <span>${student.gender === 'male' ? 'ذكر' : 'أنثى'}</span>
              </div>
              <div class="info-item">
                <span class="info-label">البريد الإلكتروني:</span>
                <span>${student.email || 'غير متوفر'}</span>
              </div>
              <div class="info-item">
                <span class="info-label">رقم الهاتف:</span>
                <span>${student.phone || 'غير متوفر'}</span>
              </div>
              <div class="info-item">
                <span class="info-label">العنوان:</span>
                <span>${student.address || 'غير متوفر'}</span>
              </div>
              <div class="info-item">
                <span class="info-label">المرحلة الدراسية:</span>
                <span>${student.grade || 'غير محدد'}</span>
              </div>
            </div>
          </div>

          ${guardians && guardians.length > 0 ? `
          <div class="section">
            <h3 class="section-title">بيانات ولي الأمر</h3>
            ${guardians.map((guardian, index) => `
            <div class="info-grid" style="margin-bottom: 10px;">
              <div class="info-item">
                <span class="info-label">الاسم الكامل:</span>
                <span>${guardian.name || ''}</span>
              </div>
              <div class="info-item">
                <span class="info-label">العلاقة بالطالب:</span>
                <span>${guardian.relation === 'father' ? 'الأب' :
                       guardian.relation === 'mother' ? 'الأم' :
                       guardian.relation === 'brother' ? 'الأخ' :
                       guardian.relation === 'sister' ? 'الأخت' :
                       guardian.relation === 'grandfather' ? 'الجد' :
                       guardian.relation === 'grandmother' ? 'الجدة' :
                       guardian.relation === 'uncle' ? 'العم' :
                       guardian.relation === 'aunt' ? 'العمة' :
                       guardian.relation || 'غير محدد'}</span>
              </div>
              <div class="info-item">
                <span class="info-label">رقم الهوية:</span>
                <span>${guardian.national_id || 'غير متوفر'}</span>
              </div>
              <div class="info-item">
                <span class="info-label">رقم الهاتف:</span>
                <span>${guardian.phone || 'غير متوفر'}</span>
              </div>
              <div class="info-item">
                <span class="info-label">البريد الإلكتروني:</span>
                <span>${guardian.email || 'غير متوفر'}</span>
              </div>
            </div>
            `).join('')}
          </div>
          ` : ''}

          <div class="section">
            <h3 class="section-title">البيانات المالية</h3>
            <div class="info-grid">
              <div class="info-item">
                <span class="info-label">الرسوم الدراسية:</span>
                <span>${formatCurrency(tuitionFee)}</span>
              </div>
              <div class="info-item">
                <span class="info-label">قيمة الخصم:</span>
                <span>${formatCurrency(discountAmount)}</span>
              </div>
              <div class="info-item">
                <span class="info-label">سبب الخصم:</span>
                <span>${financialInfo?.discount_reason || 'لا يوجد'}</span>
              </div>
              <div class="info-item">
                <span class="info-label">الإجمالي بعد الخصم:</span>
                <span style="font-weight: bold; color: #0F2A4A;">${formatCurrency(netTuition)}</span>
              </div>
              <div class="info-item">
                <span class="info-label">المبلغ المدفوع مسبقاً:</span>
                <span>${formatCurrency(financialInfo?.paid_amount)}</span>
              </div>
              <div class="info-item">
                <span class="info-label">طريقة الدفع:</span>
                <span>${financialInfo?.payment_method === 'cash' ? 'نقدي' :
                       financialInfo?.payment_method === 'installments' ? 'تقسيط' :
                       financialInfo?.payment_method || ''}</span>
              </div>
            </div>
          </div>

          ${financialInfo?.payment_method === 'installments' && installments && installments.length > 0 ? `
          <div class="section">
            <h3 class="section-title">جدول الأقساط</h3>
            <table class="table">
              <thead>
                <tr>
                  <th>رقم القسط</th>
                  <th>المبلغ</th>
                  <th>تاريخ الاستحقاق</th>
                  <th>المدفوع</th>
                  <th>الخصم</th>
                  <th>المتبقي</th>
                  <th>الحالة</th>
                </tr>
              </thead>
              <tbody>
                ${installments.map((installment, index) => {
                  const amount = parseFloat(installment.amount?.toString() || '0');
                  const paid = parseFloat(installment.paid?.toString() || '0');
                  const discount = parseFloat(installment.discount?.toString() || '0');
                  const remaining = Math.max(0, amount - paid - discount);

                  return `
                  <tr>
                    <td>${index + 1}</td>
                    <td>${formatCurrency(amount)}</td>
                    <td>${formatDate(installment.due_date)}</td>
                    <td>${formatCurrency(paid)}</td>
                    <td>${formatCurrency(discount)}</td>
                    <td>${formatCurrency(remaining)}</td>
                    <td>${installment.status === 'paid' ? 'مدفوع' :
                         installment.status === 'pending' ? 'قيد الانتظار' :
                         installment.status === 'overdue' ? 'متأخر' :
                         installment.status || ''}</td>
                  </tr>
                  `;
                }).join('')}
              </tbody>
            </table>
          </div>
          ` : ''}

          ${otherPayments && otherPayments.length > 0 ? `
          <div class="section">
            <h3 class="section-title">المدفوعات الأخرى</h3>
            <table class="table">
              <thead>
                <tr>
                  <th>البند</th>
                  <th>المبلغ</th>
                  <th>ملاحظات</th>
                </tr>
              </thead>
              <tbody>
                ${otherPayments.map((payment) => `
                <tr>
                  <td>${payment.name || ''}</td>
                  <td>${formatCurrency(payment.amount)}</td>
                  <td>${payment.notes || ''}</td>
                </tr>
                `).join('')}
              </tbody>
            </table>
          </div>
          ` : ''}

          <div class="section">
            <h3 class="section-title">الإجماليات</h3>
            <div class="info-grid">
              <div class="info-item" style="grid-column: span 2; border-bottom: 1px solid #ddd; padding-bottom: 5px;">
                <span class="info-label">إجمالي المستحق:</span>
                <span style="font-weight: bold;">${formatCurrency(totalDue)}</span>
              </div>
              <div class="info-item" style="grid-column: span 2; border-bottom: 1px solid #ddd; padding-bottom: 5px;">
                <span class="info-label">إجمالي المدفوع:</span>
                <span style="font-weight: bold; color: green;">${formatCurrency(totalPaid)}</span>
              </div>
              <div class="info-item" style="grid-column: span 2;">
                <span class="info-label">إجمالي المتبقي:</span>
                <span style="font-weight: bold; color: red;">${formatCurrency(totalRemaining)}</span>
              </div>
            </div>
          </div>
          <div class="signatures">
            <div class="signature"><div class="signature-line"></div><p>توقيع ولي الأمر</p></div>
            <div class="signature"><div class="signature-line"></div><p>توقيع المحاسب</p></div>
            <div class="signature"><div class="signature-line"></div><p>توقيع المدير</p></div>
          </div>
          <div class="footer">
            <p>مدرسة الجيل الواعد - جميع الحقوق محفوظة © ${new Date().getFullYear()}</p>
          </div>
        </div>
      </body>
      </html>
    `;

    // كتابة المحتوى في نافذة الطباعة
    printWindow.document.open();
    printWindow.document.write(printContent);
    printWindow.document.close();

    // انتظار تحميل الصفحة ثم طباعتها
    printWindow.onload = () => {
      printWindow.print();
      // إغلاق النافذة بعد الطباعة (اختياري)
      // printWindow.close();
    };
  };

  return (
    <button
      onClick={handlePrint}
      className={`bg-[#0F2A4A] hover:bg-[#0F2A4A]/90 text-white px-4 py-2 rounded-md flex items-center ${className}`}
    >
      <FaPrint className="ml-2" />
      طباعة الملخص
    </button>
  );
};

export default SimplePrintButton;
