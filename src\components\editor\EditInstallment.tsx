"use client";

import React, { useState, useEffect } from 'react';
import { supabase } from '@/lib/supabase';
import { Installment } from '@/types/table.types';
import { FaCalendarAlt, FaEdit, FaPlus } from 'react-icons/fa';
import EditModal from '@/components/ui/EditModal';


interface EditInstallmentProps {
  financialInfoId: string | number;
  installment?: Installment | null;
  isNewInstallment?: boolean;
  onUpdate?: () => void;
}

const EditInstallment: React.FC<EditInstallmentProps> = ({
  financialInfoId,
  installment,
  isNewInstallment = false,
  onUpdate
}) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [formData, setFormData] = useState<Partial<Installment>>({});
  const [errors, setErrors] = useState<Record<string, string>>({});

  // تم إزالة استخدام سياق التزامن المالي

  // تحديث بيانات النموذج عند تغيير القسط
  useEffect(() => {
    if (installment) {
      setFormData({
        amount: installment.amount,
        due_date: installment.due_date ? new Date(installment.due_date).toISOString().split('T')[0] : '',
        status: installment.status,
        payment_date: installment.payment_date ? new Date(installment.payment_date).toISOString().split('T')[0] : '',
        notes: installment.notes || '',
      });
    } else {
      // إذا كان قسط جديد، قم بتعيين القيم الافتراضية
      const today = new Date();
      const nextMonth = new Date(today);
      nextMonth.setMonth(today.getMonth() + 1);

      setFormData({
        amount: 0,
        due_date: nextMonth.toISOString().split('T')[0],
        status: 'pending',
        payment_date: '',
        notes: '',
      });
    }
  }, [installment]);

  // فتح النافذة المنبثقة
  const handleOpenModal = () => {
    setIsModalOpen(true);
  };

  // إغلاق النافذة المنبثقة
  const handleCloseModal = () => {
    setIsModalOpen(false);
    setErrors({});
  };

  // تحديث بيانات النموذج
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;

    // تحويل القيم العددية
    if (name === 'amount') {
      setFormData(prev => ({ ...prev, [name]: parseFloat(value) || 0 }));
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));
    }

    // إذا تم تغيير الحالة إلى "مدفوع"، قم بتعيين تاريخ الدفع إلى اليوم
    if (name === 'status' && value === 'paid' && !formData.payment_date) {
      setFormData(prev => ({
        ...prev,
        payment_date: new Date().toISOString().split('T')[0]
      }));
    }

    // إزالة الخطأ عند تعديل الحقل
    if (errors[name]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
  };

  // التحقق من صحة البيانات
  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if ((formData.amount || 0) <= 0) {
      newErrors.amount = 'يجب أن يكون مبلغ القسط أكبر من صفر';
    }

    if (!formData.due_date) {
      newErrors.due_date = 'تاريخ الاستحقاق مطلوب';
    }

    if (!formData.status) {
      newErrors.status = 'حالة القسط مطلوبة';
    }

    if (formData.status === 'paid' && !formData.payment_date) {
      newErrors.payment_date = 'تاريخ الدفع مطلوب عندما تكون الحالة "مدفوع"';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // حفظ التغييرات
  const handleSave = async () => {
    if (!validateForm()) return;

    try {
      setIsSaving(true);

      if (installment && !isNewInstallment) {
        // تحديث قسط موجود
        const { data, error } = await supabase
          .from('installments')
          .update({
            amount: formData.amount,
            due_date: formData.due_date,
            status: formData.status,
            payment_date: formData.status === 'paid' ? (formData.payment_date || new Date().toISOString()) : null,
            notes: formData.notes || null,
            updated_at: new Date().toISOString(),
          })
          .eq('id', installment.id)
          .select()
          .single();

        if (error) {
          throw new Error(error.message);
        }

        console.log('تم تحديث القسط:', data);
      } else {
        // إنشاء قسط جديد
        const { data, error } = await supabase
          .from('installments')
          .insert({
            financial_info_id: financialInfoId,
            amount: formData.amount,
            due_date: formData.due_date,
            status: formData.status,
            payment_date: formData.status === 'paid' ? (formData.payment_date || new Date().toISOString()) : null,
            notes: formData.notes || null,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
          })
          .select()
          .single();

        if (error) {
          throw new Error(error.message);
        }

        console.log('تم إنشاء قسط جديد:', data);
      }

      // تحديث البيانات في الواجهة
      await refreshInstallments(financialInfoId);

      // استدعاء دالة التحديث إذا كانت موجودة
      if (onUpdate) {
        onUpdate();
      }

      // إغلاق النافذة المنبثقة
      handleCloseModal();
    } catch (err: any) {
      console.error('خطأ في حفظ القسط:', err);
      alert(`حدث خطأ أثناء حفظ البيانات: ${err.message}`);
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <>
      {/* زر التعديل أو الإضافة */}
      {isNewInstallment ? (
        <button
          onClick={handleOpenModal}
          className="bg-[#0ABB87] hover:bg-[#0ABB87]/90 text-white px-3 py-1 rounded-md flex items-center transition-colors duration-200 text-sm"
          title="إضافة قسط جديد"
        >
          <FaPlus className="ml-1" />
          إضافة قسط
        </button>
      ) : (
        <button
          onClick={handleOpenModal}
          className="bg-[#5578EB] hover:bg-[#5578EB]/90 text-white px-2 py-1 rounded-md flex items-center transition-colors duration-200 text-xs"
          title="تعديل القسط"
        >
          <FaEdit className="ml-1" />
          تعديل
        </button>
      )}

      {/* نافذة التعديل المنبثقة */}
      <EditModal
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        onSave={handleSave}
        title={isNewInstallment ? "إضافة قسط جديد" : "تعديل القسط"}
        isSaving={isSaving}
        color="#FD1361"
        icon={<FaCalendarAlt size={18} />}
      >
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* مبلغ القسط */}
          <div>
            <label className="block text-gray-700 mb-1">مبلغ القسط <span className="text-red-500">*</span></label>
            <input
              type="number"
              name="amount"
              value={formData.amount || 0}
              onChange={handleChange}
              className={`w-full p-2 border rounded-md ${errors.amount ? 'border-red-500' : 'border-gray-300'}`}
              min="0"
              step="0.01"
              dir="rtl"
            />
            {errors.amount && <p className="text-red-500 text-sm mt-1">{errors.amount}</p>}
          </div>

          {/* تاريخ الاستحقاق */}
          <div>
            <label className="block text-gray-700 mb-1">تاريخ الاستحقاق <span className="text-red-500">*</span></label>
            <input
              type="date"
              name="due_date"
              value={formData.due_date || ''}
              onChange={handleChange}
              className={`w-full p-2 border rounded-md ${errors.due_date ? 'border-red-500' : 'border-gray-300'}`}
              dir="rtl"
            />
            {errors.due_date && <p className="text-red-500 text-sm mt-1">{errors.due_date}</p>}
          </div>

          {/* حالة القسط */}
          <div>
            <label className="block text-gray-700 mb-1">حالة القسط <span className="text-red-500">*</span></label>
            <select
              name="status"
              value={formData.status || ''}
              onChange={handleChange}
              className={`w-full p-2 border rounded-md ${errors.status ? 'border-red-500' : 'border-gray-300'}`}
              dir="rtl"
            >
              <option value="">اختر الحالة</option>
              <option value="pending">قيد الانتظار</option>
              <option value="paid">مدفوع</option>
              <option value="late">متأخر</option>
            </select>
            {errors.status && <p className="text-red-500 text-sm mt-1">{errors.status}</p>}
          </div>

          {/* تاريخ الدفع */}
          <div>
            <label className="block text-gray-700 mb-1">
              تاريخ الدفع
              {formData.status === 'paid' && <span className="text-red-500">*</span>}
            </label>
            <input
              type="date"
              name="payment_date"
              value={formData.payment_date || ''}
              onChange={handleChange}
              className={`w-full p-2 border rounded-md ${errors.payment_date ? 'border-red-500' : 'border-gray-300'}`}
              dir="rtl"
              disabled={formData.status !== 'paid'}
            />
            {errors.payment_date && <p className="text-red-500 text-sm mt-1">{errors.payment_date}</p>}
          </div>

          {/* ملاحظات */}
          <div className="col-span-2">
            <label className="block text-gray-700 mb-1">ملاحظات</label>
            <textarea
              name="notes"
              value={formData.notes || ''}
              onChange={handleChange}
              className="w-full p-2 border border-gray-300 rounded-md"
              rows={3}
              dir="rtl"
              placeholder="أدخل أي ملاحظات إضافية هنا"
            ></textarea>
          </div>
        </div>
      </EditModal>
    </>
  );
};

export default EditInstallment;
