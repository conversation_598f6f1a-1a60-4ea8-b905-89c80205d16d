"use client";

import React from 'react';
import * as FaIcons from 'react-icons/fa';
import * as IoIcons from 'react-icons/io5';
import * as HiIcons from 'react-icons/hi';
import * as BiIcons from 'react-icons/bi';
import * as MdIcons from 'react-icons/md';

// تعريف الألوان المستخدمة في المشروع
export const COLORS = {
  PRIMARY: '#21ADE7',    // أزرق فاتح
  SECONDARY: '#5578EB',  // أزرق داكن
  SUCCESS: '#0ABB87',    // أخضر
  DANGER: '#FD1361',     // أحمر
  INFO: '#384AD7',       // أزرق غامق
  WARNING: '#FFC107',    // أصفر
  DARK: '#343a40',       // أسود
  LIGHT: '#f8f9fa',      // أبيض
  GRAY: '#6c757d',       // رمادي
};

// تعريف أحجام الأيقونات
export const SIZES = {
  XS: 12,
  SM: 16,
  MD: 20,
  LG: 24,
  XL: 32,
  XXL: 48,
};

// تعريف أنواع الأيقونات
export type IconType = 
  | keyof typeof FaIcons 
  | keyof typeof IoIcons 
  | keyof typeof HiIcons 
  | keyof typeof BiIcons
  | keyof typeof MdIcons;

// تعريف خصائص الأيقونة
export interface IconProps {
  name: IconType;
  color?: keyof typeof COLORS | string;
  size?: keyof typeof SIZES | number;
  className?: string;
  onClick?: () => void;
}

// مكون الأيقونة
export const Icon: React.FC<IconProps> = ({
  name,
  color = 'PRIMARY',
  size = 'MD',
  className = '',
  onClick,
}) => {
  // تحديد اللون
  const iconColor = COLORS[color as keyof typeof COLORS] || color;
  
  // تحديد الحجم
  const iconSize = SIZES[size as keyof typeof SIZES] || size;

  // تحديد المكتبة المناسبة بناءً على اسم الأيقونة
  if (name.startsWith('Fa')) {
    const IconComponent = FaIcons[name as keyof typeof FaIcons];
    return IconComponent ? (
      <IconComponent
        size={iconSize}
        style={{ color: iconColor }}
        className={className}
        onClick={onClick}
      />
    ) : null;
  }

  if (name.startsWith('Io')) {
    const IconComponent = IoIcons[name as keyof typeof IoIcons];
    return IconComponent ? (
      <IconComponent
        size={iconSize}
        style={{ color: iconColor }}
        className={className}
        onClick={onClick}
      />
    ) : null;
  }

  if (name.startsWith('Hi')) {
    const IconComponent = HiIcons[name as keyof typeof HiIcons];
    return IconComponent ? (
      <IconComponent
        size={iconSize}
        style={{ color: iconColor }}
        className={className}
        onClick={onClick}
      />
    ) : null;
  }

  if (name.startsWith('Bi')) {
    const IconComponent = BiIcons[name as keyof typeof BiIcons];
    return IconComponent ? (
      <IconComponent
        size={iconSize}
        style={{ color: iconColor }}
        className={className}
        onClick={onClick}
      />
    ) : null;
  }

  if (name.startsWith('Md')) {
    const IconComponent = MdIcons[name as keyof typeof MdIcons];
    return IconComponent ? (
      <IconComponent
        size={iconSize}
        style={{ color: iconColor }}
        className={className}
        onClick={onClick}
      />
    ) : null;
  }

  return null;
};

export default Icon;
