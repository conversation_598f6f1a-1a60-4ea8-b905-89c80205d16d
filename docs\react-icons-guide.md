# دليل استخدام React Icons الملونة في المشروع

هذا الدليل يشرح كيفية استخدام مكتبة React Icons مع إضافة ألوان للأيقونات في مشروع نظام إدارة المدرسة.

## مقدمة

[React Icons](https://react-icons.github.io/react-icons/) هي مكتبة تجمع بين العديد من مكتبات الأيقونات الشهيرة في مكان واحد. تتيح لك استخدام أيقونات من مكتبات مختلفة مثل Font Awesome وMaterial Design وBootstrap وغيرها.

## المكتبات المستخدمة في المشروع

- **Font Awesome** - `import { FaIconName } from 'react-icons/fa'` (المكتبة الرئيسية المستخدمة)

## الألوان المستخدمة في المشروع

تم استخدام مجموعة من الألوان المتناسقة لتحقيق الهوية البصرية للمشروع:

- **أزرق فاتح (Primary)**: `#21ADE7` - يستخدم للعناصر الرئيسية والإجراءات الأساسية
- **أزرق داكن (Secondary)**: `#5578EB` - يستخدم للعناصر الثانوية
- **أخضر (Success)**: `#0ABB87` - يستخدم للإجراءات الناجحة والعناصر الإيجابية
- **أحمر (Danger)**: `#FD1361` - يستخدم للتحذيرات والإجراءات الخطيرة
- **أزرق غامق (Info)**: `#384AD7` - يستخدم للمعلومات والإشعارات

## كيفية استخدام الأيقونات الملونة

### 1. استيراد الأيقونة

```jsx
// استيراد أيقونة من Font Awesome
import { FaUser, FaGraduationCap, FaMoneyBillWave } from 'react-icons/fa';
```

### 2. استخدام الأيقونة مع إضافة لون

هناك طريقتان لإضافة لون للأيقونة:

#### الطريقة الأولى: استخدام خاصية color

```jsx
<FaUser size={24} color="#21ADE7" />
```

#### الطريقة الثانية: استخدام خاصية style (الطريقة المفضلة)

```jsx
<FaUser size={24} style={{ color: '#21ADE7' }} />
```

### 3. استخدام الأيقونات الملونة في المشروع

```jsx
// أيقونة ملونة للصفحة الرئيسية
<FaHome size={24} style={{ color: '#21ADE7' }} />

// أيقونة ملونة لشؤون الطلاب
<FaGraduationCap size={24} style={{ color: '#0ABB87' }} />

// أيقونة ملونة للنظام المالي
<FaCoins size={24} style={{ color: '#FD1361' }} />
```

## خصائص الأيقونة

- **size** - حجم الأيقونة (بالبكسل)
- **style** - نمط CSS للأيقونة، يمكن استخدامه لتحديد اللون وخصائص أخرى
- **className** - فئة CSS للأيقونة
- **onClick** - دالة تُنفذ عند النقر على الأيقونة

## الأيقونات الملونة المستخدمة في المشروع

### أيقونات القائمة الجانبية

| الوصف | الأيقونة | اللون | الاستيراد |
|-------|---------|------|-----------|
| لوحة التحكم | `<FaHome style={{ color: '#21ADE7' }} />` | `#21ADE7` | `import { FaHome } from 'react-icons/fa'` |
| الإحصائيات | `<FaChartPie style={{ color: '#5578EB' }} />` | `#5578EB` | `import { FaChartPie } from 'react-icons/fa'` |
| شؤون الطلاب | `<FaGraduationCap style={{ color: '#0ABB87' }} />` | `#0ABB87` | `import { FaGraduationCap } from 'react-icons/fa'` |
| تسجيل الطلاب | `<FaUserPlus style={{ color: '#21ADE7' }} />` | `#21ADE7` | `import { FaUserPlus } from 'react-icons/fa'` |
| قائمة الطلاب | `<FaUserGraduate style={{ color: '#5578EB' }} />` | `#5578EB` | `import { FaUserGraduate } from 'react-icons/fa'` |
| مستندات الطلاب | `<FaFileAlt style={{ color: '#0ABB87' }} />` | `#0ABB87` | `import { FaFileAlt } from 'react-icons/fa'` |
| السجلات الأكاديمية | `<FaBook style={{ color: '#FD1361' }} />` | `#FD1361` | `import { FaBook } from 'react-icons/fa'` |
| البطاقات التعريفية | `<FaIdCard style={{ color: '#384AD7' }} />` | `#384AD7` | `import { FaIdCard } from 'react-icons/fa'` |
| التحويلات | `<FaExchangeAlt style={{ color: '#21ADE7' }} />` | `#21ADE7` | `import { FaExchangeAlt } from 'react-icons/fa'` |
| البحث المتقدم | `<FaSearchPlus style={{ color: '#5578EB' }} />` | `#5578EB` | `import { FaSearchPlus } from 'react-icons/fa'` |
| الحضور والغياب | `<FaCalendarAlt style={{ color: '#0ABB87' }} />` | `#0ABB87` | `import { FaCalendarAlt } from 'react-icons/fa'` |
| إسقاط وحذف | `<FaTrash style={{ color: '#FD1361' }} />` | `#FD1361` | `import { FaTrash } from 'react-icons/fa'` |
| الملفات الصحية | `<FaMedkit style={{ color: '#384AD7' }} />` | `#384AD7` | `import { FaMedkit } from 'react-icons/fa'` |
| النظام المالي | `<FaCoins style={{ color: '#FD1361' }} />` | `#FD1361` | `import { FaCoins } from 'react-icons/fa'` |
| الرسوم الدراسية | `<FaMoneyBill style={{ color: '#21ADE7' }} />` | `#21ADE7` | `import { FaMoneyBill } from 'react-icons/fa'` |
| المدفوعات | `<FaMoneyCheckAlt style={{ color: '#0ABB87' }} />` | `#0ABB87` | `import { FaMoneyCheckAlt } from 'react-icons/fa'` |
| التقارير المالية | `<FaFileInvoice style={{ color: '#5578EB' }} />` | `#5578EB` | `import { FaFileInvoice } from 'react-icons/fa'` |
| التقارير | `<FaChartLine style={{ color: '#384AD7' }} />` | `#384AD7` | `import { FaChartLine } from 'react-icons/fa'` |

### أيقونات التنقل

| الوصف | الأيقونة | اللون | الاستيراد |
|-------|---------|------|-----------|
| سهم لليسار | `<FaAngleLeft style={{ color: '#21ADE7' }} />` | `#21ADE7` | `import { FaAngleLeft } from 'react-icons/fa'` |
| سهم للأسفل | `<FaAngleDown style={{ color: '#5578EB' }} />` | `#5578EB` | `import { FaAngleDown } from 'react-icons/fa'` |

### أيقونات الإجراءات المقترحة للاستخدام

| الوصف | الأيقونة | اللون المقترح | الاستيراد |
|-------|---------|--------------|-----------|
| عرض | `<FaEye style={{ color: '#21ADE7' }} />` | `#21ADE7` | `import { FaEye } from 'react-icons/fa'` |
| تعديل | `<FaEdit style={{ color: '#5578EB' }} />` | `#5578EB` | `import { FaEdit } from 'react-icons/fa'` |
| حذف | `<FaTrash style={{ color: '#FD1361' }} />` | `#FD1361` | `import { FaTrash } from 'react-icons/fa'` |
| إضافة | `<FaPlus style={{ color: '#0ABB87' }} />` | `#0ABB87` | `import { FaPlus } from 'react-icons/fa'` |
| حفظ | `<FaSave style={{ color: '#0ABB87' }} />` | `#0ABB87` | `import { FaSave } from 'react-icons/fa'` |
| إلغاء | `<FaTimes style={{ color: '#FD1361' }} />` | `#FD1361` | `import { FaTimes } from 'react-icons/fa'` |
| تحميل | `<FaDownload style={{ color: '#21ADE7' }} />` | `#21ADE7` | `import { FaDownload } from 'react-icons/fa'` |
| رفع | `<FaUpload style={{ color: '#5578EB' }} />` | `#5578EB` | `import { FaUpload } from 'react-icons/fa'` |
| طباعة | `<FaPrint style={{ color: '#384AD7' }} />` | `#384AD7` | `import { FaPrint } from 'react-icons/fa'` |
| تصدير | `<FaFileExport style={{ color: '#0ABB87' }} />` | `#0ABB87` | `import { FaFileExport } from 'react-icons/fa'` |
| استيراد | `<FaFileImport style={{ color: '#5578EB' }} />` | `#5578EB` | `import { FaFileImport } from 'react-icons/fa'` |

## موارد إضافية

- [موقع React Icons](https://react-icons.github.io/react-icons/)
- [مستودع React Icons على GitHub](https://github.com/react-icons/react-icons)
- [قائمة الأيقونات المتاحة](https://react-icons.github.io/react-icons/icons?name=fa)
