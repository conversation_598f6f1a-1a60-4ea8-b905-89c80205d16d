# دليل قاعدة بيانات نظام إدارة مدرسة الجيل الواعد

هذا الدليل يشرح هيكل قاعدة البيانات المستخدمة في نظام إدارة مدرسة الجيل الواعد، وكيفية تثبيتها واستخدامها.

## نظرة عامة

تتكون قاعدة البيانات من مجموعة من الجداول المترابطة التي تغطي الجوانب الأساسية لإدارة المدرسة:

1. **المراحل والفصول الدراسية**: جداول المراحل الدراسية (school_grades) والفصول الدراسية (school_classes)
2. **بيانات الطلاب**: جدول الطلاب (students) وأولياء الأمور (guardians) والمعلومات الإضافية (additional_info)
3. **البيانات المالية**: المعلومات المالية (financial_info) والأقساط (installments) والمدفوعات الأخرى (other_payments)
4. **المعلومات الصحية والمرفقات**: المعلومات الصحية (health_info) والمرفقات (attachments)
5. **السجلات الأكاديمية**: السجلات الأكاديمية (academic_records)

## متطلبات النظام

- PostgreSQL 12 أو أحدث
- Supabase (موصى به للاستفادة من الميزات الإضافية)

## تثبيت قاعدة البيانات

### الطريقة 1: استخدام Supabase

1. قم بإنشاء مشروع جديد في [Supabase](https://supabase.com/)
2. انتقل إلى محرر SQL في لوحة التحكم
3. قم بتنفيذ الملف `run-all.sql` الذي سيقوم بتنفيذ جميع ملفات SQL بالترتيب الصحيح

### الطريقة 2: استخدام PostgreSQL مباشرة

1. قم بإنشاء قاعدة بيانات جديدة:
   ```sql
   CREATE DATABASE school_management;
   ```

2. اتصل بقاعدة البيانات:
   ```bash
   psql -U username -d school_management
   ```

3. قم بتنفيذ الملف `run-all.sql`:
   ```bash
   \i 'path/to/run-all.sql'
   ```

## هيكل الملفات

- `run-all.sql`: الملف الرئيسي الذي يقوم بتنفيذ جميع ملفات SQL بالترتيب الصحيح
- `actual-database-schema.sql`: إنشاء هيكل قاعدة البيانات (الجداول والعلاقات والفهارس والمشغلات)
- `sample-data.sql`: إدخال بيانات نموذجية للاختبار والتطوير

## العلاقات الرئيسية بين الجداول

### الطلاب والفصول

- يتم تخزين بيانات الطلاب في جدول `students`
- يتم تخزين المراحل الدراسية في جدول `school_grades`
- يتم تخزين الفصول الدراسية في جدول `school_classes`
- يتم ربط الطالب بالمرحلة والفصل الدراسي من خلال جدول `academic_records`

### الطلاب والمعلومات المالية

- كل طالب له سجل واحد في جدول المعلومات المالية (`financial_info`)
- كل سجل مالي يمكن أن يكون له عدة أقساط في جدول الأقساط (`installments`)
- كل طالب يمكن أن يكون له عدة مدفوعات أخرى في جدول المدفوعات الأخرى (`other_payments`)

### الطلاب وأولياء الأمور

- كل طالب يمكن أن يكون له عدة أولياء أمور في جدول أولياء الأمور (`guardians`)

### الطلاب والمعلومات الإضافية

- كل طالب له سجل واحد في جدول المعلومات الصحية (`health_info`)
- كل طالب له سجل واحد في جدول المعلومات الإضافية (`additional_info`)
- كل طالب له سجل واحد في جدول المرفقات (`attachments`)

## المشغلات (Triggers) والدوال (Functions)

تم إنشاء عدة مشغلات ودوال لأتمتة بعض العمليات:

1. **تحديث حقل updated_at**: يتم تحديث حقل `updated_at` تلقائيًا عند تحديث أي سجل
2. **تحديث حالة الأقساط**: يتم تحديث حالة القسط تلقائيًا بناءً على المبلغ المدفوع والخصم وتاريخ الاستحقاق
3. **تحديث إجمالي المبلغ المدفوع**: يتم تحديث إجمالي المبلغ المدفوع في المعلومات المالية تلقائيًا عند تحديث الأقساط

## الاستعلامات الشائعة

### استعلام بيانات الطالب الكاملة

```sql
SELECT s.*, ar.grade_id, g.name AS grade_name, ar.class_id, c.name AS class_name
FROM students s
LEFT JOIN academic_records ar ON s.id = ar.student_id
LEFT JOIN school_grades g ON ar.grade_id = g.id
LEFT JOIN school_classes c ON ar.class_id = c.id
WHERE s.id = 1;
```

### استعلام البيانات المالية للطالب

```sql
SELECT f.*,
       (f.tuition_fee - f.discount_amount) AS net_tuition,
       (SELECT SUM(amount) FROM other_payments WHERE student_id = f.student_id) AS other_payments_total,
       (SELECT SUM(paid) FROM installments WHERE financial_info_id = f.id) AS total_paid_installments
FROM financial_info f
WHERE f.student_id = 1;
```

### استعلام الأقساط المستحقة

```sql
SELECT i.*, f.student_id, s.full_name
FROM installments i
JOIN financial_info f ON i.financial_info_id = f.id
JOIN students s ON f.student_id = s.id
WHERE i.status = 'pending' AND i.due_date <= CURRENT_DATE;
```

### استعلام أولياء أمور الطالب

```sql
SELECT g.*
FROM guardians g
WHERE g.student_id = 1;
```

## الصيانة والنسخ الاحتياطي

### النسخ الاحتياطي

```bash
pg_dump -U username -d school_management > backup_$(date +%Y%m%d).sql
```

### استعادة النسخ الاحتياطي

```bash
psql -U username -d school_management < backup_file.sql
```

## ملاحظات هامة

- تم تصميم قاعدة البيانات لتكون متوافقة مع Supabase وPostgreSQL القياسي
- تم استخدام المفاتيح الخارجية والقيود للحفاظ على سلامة البيانات
- تم إنشاء فهارس على الحقول الأكثر استخدامًا في الاستعلامات للحصول على أداء أفضل
- تم استخدام المشغلات لأتمتة بعض العمليات وتقليل الحاجة إلى كود إضافي في التطبيق
