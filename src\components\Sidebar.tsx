import React, { useState } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { Icon } from './ui';

interface MenuItem {
  id: string;
  title: string;
  icon: string; // اسم الأيقونة
  path: string;
  subItems?: MenuItem[];
}

const menuItems: MenuItem[] = [
  {
    id: 'dashboard',
    title: 'لوحة التحكم',
    icon: 'FaHome', // أيقونة للصفحة الرئيسية
    path: '/',
    subItems: [
      {
        id: 'statistics',
        title: 'الإحصائيات',
        icon: 'FaChartPie', // أيقونة للإحصائيات
        path: '/dashboard/statistics',
      },
      {
        id: 'ui-components',
        title: 'مكتبة المكونات',
        icon: 'FaPalette', // أيقونة لمكتبة المكونات
        path: '/ui-components',
      },
    ],
  },
  {
    id: 'students',
    title: 'شؤون الطلاب',
    icon: 'FaGraduationCap', // أيقونة لشؤون الطلاب
    path: '/students',
    subItems: [
      {
        id: 'student-registration',
        title: 'تسجيل الطلاب',
        icon: 'FaUserPlus', // أيقونة لتسجيل الطلاب
        path: '/students/registration',
      },
      {
        id: 'student-list',
        title: 'قائمة الطلاب',
        icon: 'FaUserGraduate', // أيقونة لقائمة الطلاب
        path: '/students/list',
      },
      {
        id: 'grades',
        title: 'المراحل الدراسية',
        icon: 'FaGraduationCap', // أيقونة للمراحل الدراسية
        path: '/grades',
      },
      {
        id: 'classes',
        title: 'الفصول الدراسية',
        icon: 'FaChalkboardTeacher', // أيقونة للفصول الدراسية
        path: '/classes',
      },
      {
        id: 'student-documents',
        title: 'مستندات الطلاب',
        icon: 'FaFileAlt', // أيقونة لمستندات الطلاب
        path: '/students/documents',
      },
      {
        id: 'academic-records',
        title: 'السجلات الأكاديمية',
        icon: 'FaBook', // أيقونة للسجلات الأكاديمية
        path: '/students/academic-records',
      },
      {
        id: 'id-cards',
        title: 'البطاقات التعريفية',
        icon: 'FaIdCard', // أيقونة للبطاقات التعريفية
        path: '/students/id-cards',
      },
      {
        id: 'transfers',
        title: 'التحويلات',
        icon: 'FaExchangeAlt', // أيقونة للتحويلات
        path: '/students/transfers',
      },
      {
        id: 'advanced-search',
        title: 'البحث المتقدم',
        icon: 'FaSearchPlus', // أيقونة للبحث المتقدم
        path: '/students/search',
      },
      {
        id: 'attendance',
        title: 'الحضور والغياب',
        icon: 'FaCalendarAlt', // أيقونة للحضور والغياب
        path: '/students/attendance',
      },
      {
        id: 'student-drop',
        title: 'إسقاط وحذف',
        icon: 'FaTrash', // أيقونة للإسقاط والحذف
        path: '/students/drop',
      },
      {
        id: 'export-students',
        title: 'تصدير البيانات',
        icon: 'FaFileExport', // أيقونة لتصدير البيانات
        path: '/students/export',
      },
      {
        id: 'health-records',
        title: 'الملفات الصحية',
        icon: 'FaMedkit', // أيقونة للملفات الصحية
        path: '/students/health-records',
      },
    ],
  },
  {
    id: 'financial',
    title: 'النظام المالي',
    icon: 'FaCoins', // أيقونة للنظام المالي
    path: '/financial',
    subItems: [
      {
        id: 'grade-fees',
        title: 'إدارة رسوم المراحل',
        icon: 'FaMoneyBillWave', // أيقونة لإدارة رسوم المراحل
        path: '/academic/grade-fees',
      },
      {
        id: 'fees',
        title: 'الرسوم الدراسية',
        icon: 'FaMoneyBill', // أيقونة للرسوم الدراسية
        path: '/financial/fees',
      },
      {
        id: 'payments',
        title: 'المدفوعات',
        icon: 'FaMoneyCheckAlt', // أيقونة للمدفوعات
        path: '/financial/payments',
      },
      {
        id: 'financial-reports',
        title: 'التقارير المالية',
        icon: 'FaFileInvoice', // أيقونة للتقارير المالية
        path: '/financial/reports',
      },
    ],
  },
  {
    id: 'reports',
    title: 'التقارير',
    icon: 'FaChartLine', // أيقونة للتقارير
    path: '/reports',
  },
];

const Sidebar: React.FC = () => {
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [expandedItems, setExpandedItems] = useState<string[]>([]);
  const pathname = usePathname();

  const toggleSidebar = () => {
    setIsCollapsed(!isCollapsed);
  };

  const toggleSubMenu = (id: string) => {
    setExpandedItems((prev) =>
      prev.includes(id) ? prev.filter((item) => item !== id) : [...prev, id]
    );
  };

  const isActive = (path: string) => {
    return pathname === path;
  };

  const isSubMenuActive = (item: MenuItem) => {
    if (item.subItems) {
      return item.subItems.some((subItem) => isActive(subItem.path));
    }
    return false;
  };

  return (
    <div
      className={`bg-white shadow-lg transition-all duration-300 h-screen border-l-4 border-[#5578EB] ${
        isCollapsed ? 'w-16' : 'w-64'
      }`}
    >
      <div className="p-4 flex justify-between items-center border-b">
        {!isCollapsed && (
          <h1 className="text-primary font-bold text-xl">نظام المدرسة</h1>
        )}
        <button
          onClick={toggleSidebar}
          className="p-2 rounded-full hover:bg-primary/20 transition-all duration-200 clickable"
        >
          <span className={`transition-transform duration-300 flex items-center justify-center ${
            isCollapsed ? 'rotate-180' : ''
          }`}>
            <Icon name="FaAngleLeft" color="PRIMARY" size="LG" />
          </span>
        </button>
      </div>

      <nav className="mt-4">
        <ul className="space-y-2">
          {menuItems.map((item) => (
            <li key={item.id}>
              {item.subItems ? (
                <div>
                  <button
                    onClick={() => toggleSubMenu(item.id)}
                    className={`w-full flex items-center p-3 ${
                      isCollapsed ? 'justify-center' : 'justify-between'
                    } ${
                      isActive(item.path) || isSubMenuActive(item)
                        ? 'bg-[#21ADE7] text-white shadow-md'
                        : 'hover:bg-[#21ADE7]/20 text-gray-700'
                    } rounded-md transition-all duration-200 clickable`}
                  >
                    <div className="flex items-center">
                      <span className="flex items-center justify-center">
                        <Icon name={item.icon} color={isActive(item.path) || isSubMenuActive(item) ? "LIGHT" : "PRIMARY"} size="LG" />
                      </span>
                      {!isCollapsed && (
                        <span className="mr-3">{item.title}</span>
                      )}
                    </div>
                    {!isCollapsed && (
                      <span className={`transition-transform duration-300 ${
                        expandedItems.includes(item.id) ? 'rotate-180' : ''
                      }`}>
                        <Icon name="FaAngleDown" color="SECONDARY" size="SM" />
                      </span>
                    )}
                  </button>
                  {!isCollapsed && expandedItems.includes(item.id) && (
                    <ul className="mt-2 mr-4 space-y-1">
                      {item.subItems.map((subItem) => (
                        <li key={subItem.id}>
                          <Link href={subItem.path} className="clickable">
                            <div
                              className={`flex items-center p-2 ${
                                isActive(subItem.path)
                                  ? 'bg-[#5578EB] text-white shadow-sm'
                                  : 'hover:bg-[#5578EB]/20 text-gray-700'
                              } rounded-md transition-all duration-200 clickable`}
                            >
                              <span className="flex items-center justify-center">
                                <Icon name={subItem.icon} color={isActive(subItem.path) ? "LIGHT" : "SECONDARY"} size="SM" />
                              </span>
                              <span className="mr-2">{subItem.title}</span>
                            </div>
                          </Link>
                        </li>
                      ))}
                    </ul>
                  )}
                </div>
              ) : (
                <Link href={item.path} className="clickable">
                  <div
                    className={`flex items-center p-3 ${
                      isCollapsed ? 'justify-center' : ''
                    } ${
                      isActive(item.path)
                        ? 'bg-[#21ADE7] text-white shadow-md'
                        : 'hover:bg-[#21ADE7]/20 text-gray-700'
                    } rounded-md transition-all duration-200 clickable`}
                  >
                    <span className="flex items-center justify-center">
                      <Icon name={item.icon} color={isActive(item.path) ? "LIGHT" : "PRIMARY"} size="LG" />
                    </span>
                    {!isCollapsed && (
                      <span className="mr-3">{item.title}</span>
                    )}
                  </div>
                </Link>
              )}
            </li>
          ))}
        </ul>
      </nav>
    </div>
  );
};

export default Sidebar;
