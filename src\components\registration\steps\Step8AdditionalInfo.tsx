"use client";

import React, { useEffect } from 'react';
import { useFormik } from 'formik';
import * as Yup from 'yup';

interface Step8Props {
  onComplete: (data: any, isValid: boolean) => void;
  formData: Record<string, any>;
}

const Step8AdditionalInfo: React.FC<Step8Props> = ({ onComplete, formData }) => {
  // التحقق من صحة البيانات باستخدام Yup
  const validationSchema = Yup.object({
    hobbies: Yup.string(),
    achievements: Yup.string(),
    specialNeeds: Yup.string(),
    transportationMethod: Yup.string(),
    additionalNotes: Yup.string(),
  });

  // إعداد نموذج Formik
  const formik = useFormik({
    initialValues: {
      hobbies: formData.hobbies || '',
      achievements: formData.achievements || '',
      specialNeeds: formData.specialNeeds || '',
      transportationMethod: formData.transportationMethod || '',
      additionalNotes: formData.additionalNotes || '',
    },
    validationSchema,
    onSubmit: (values) => {
      console.log('Form submitted with values:', values);
    },
  });

  // تحديث البيانات عند تغييرها
  useEffect(() => {
    onComplete(formik.values, true); // هذه الخطوة اختيارية، لذلك دائمًا صالحة
  }, [formik.values, onComplete]);

  return (
    <div className="bg-white rounded-lg p-6">
      <h2 className="text-xl font-bold text-primary mb-6 border-r-4 border-primary pr-3">
        معلومات إضافية
      </h2>
      
      <div className="bg-blue-50 border-r-4 border-blue-400 p-4 mb-6">
        <p className="text-blue-700">
          هذه المعلومات اختيارية ولكنها تساعدنا في فهم احتياجات الطالب بشكل أفضل وتقديم الدعم المناسب له.
        </p>
      </div>
      
      <form className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* الهوايات والاهتمامات */}
          <div>
            <label htmlFor="hobbies" className="block text-gray-700 font-medium mb-2">
              الهوايات والاهتمامات
            </label>
            <textarea
              id="hobbies"
              name="hobbies"
              rows={3}
              className="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              value={formik.values.hobbies}
              placeholder="الهوايات والاهتمامات التي يفضلها الطالب"
            ></textarea>
          </div>

          {/* الإنجازات السابقة */}
          <div>
            <label htmlFor="achievements" className="block text-gray-700 font-medium mb-2">
              الإنجازات السابقة
            </label>
            <textarea
              id="achievements"
              name="achievements"
              rows={3}
              className="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              value={formik.values.achievements}
              placeholder="أي إنجازات أو جوائز حصل عليها الطالب"
            ></textarea>
          </div>

          {/* الاحتياجات الخاصة */}
          <div>
            <label htmlFor="specialNeeds" className="block text-gray-700 font-medium mb-2">
              الاحتياجات الخاصة
            </label>
            <textarea
              id="specialNeeds"
              name="specialNeeds"
              rows={3}
              className="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              value={formik.values.specialNeeds}
              placeholder="أي احتياجات خاصة للطالب (تعليمية، سلوكية، إلخ)"
            ></textarea>
          </div>

          {/* طريقة الوصول إلى المدرسة */}
          <div>
            <label htmlFor="transportationMethod" className="block text-gray-700 font-medium mb-2">
              طريقة الوصول إلى المدرسة
            </label>
            <select
              id="transportationMethod"
              name="transportationMethod"
              className="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              value={formik.values.transportationMethod}
            >
              <option value="">اختر طريقة الوصول</option>
              <option value="schoolBus">باص المدرسة</option>
              <option value="parentCar">سيارة ولي الأمر</option>
              <option value="walking">مشياً على الأقدام</option>
              <option value="publicTransport">وسائل النقل العامة</option>
              <option value="other">أخرى</option>
            </select>
          </div>

          {/* ملاحظات إضافية */}
          <div className="md:col-span-2">
            <label htmlFor="additionalNotes" className="block text-gray-700 font-medium mb-2">
              ملاحظات إضافية
            </label>
            <textarea
              id="additionalNotes"
              name="additionalNotes"
              rows={4}
              className="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              value={formik.values.additionalNotes}
              placeholder="أي ملاحظات أو معلومات إضافية ترغب في مشاركتها مع المدرسة"
            ></textarea>
          </div>
        </div>
      </form>
      
      <div className="mt-6 p-4 bg-gray-50 rounded-lg border border-gray-200">
        <h3 className="text-lg font-semibold text-primary mb-2">كيف يمكننا المساعدة؟</h3>
        <p className="text-gray-600 mb-4">
          تهدف المدرسة إلى توفير بيئة تعليمية داعمة ومحفزة لجميع الطلاب. إذا كان لدى الطالب أي احتياجات خاصة أو مواهب تحتاج إلى تطوير، يرجى مشاركتها معنا لنتمكن من تقديم الدعم المناسب.
        </p>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-white p-3 rounded-lg border border-gray-200 text-center">
            <span className="material-icons text-primary text-3xl mb-2">school</span>
            <h4 className="font-medium mb-1">دعم أكاديمي</h4>
            <p className="text-sm text-gray-500">برامج تقوية وتطوير المهارات الأكاديمية</p>
          </div>
          <div className="bg-white p-3 rounded-lg border border-gray-200 text-center">
            <span className="material-icons text-secondary text-3xl mb-2">sports_soccer</span>
            <h4 className="font-medium mb-1">أنشطة رياضية</h4>
            <p className="text-sm text-gray-500">فرق رياضية وبرامج تدريبية متنوعة</p>
          </div>
          <div className="bg-white p-3 rounded-lg border border-gray-200 text-center">
            <span className="material-icons text-success text-3xl mb-2">palette</span>
            <h4 className="font-medium mb-1">أنشطة فنية</h4>
            <p className="text-sm text-gray-500">برامج لتنمية المواهب الفنية والإبداعية</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Step8AdditionalInfo;
