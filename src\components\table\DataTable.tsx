import React, { useState, useMemo } from 'react';
import {
  useReactTable,
  getCoreRowModel,
  getSortedRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  flexRender,
  ColumnDef,
  SortingState,
  ColumnFiltersState,
  FilterFn,
} from '@tanstack/react-table';
import {
  FaSort,
  FaSortUp,
  FaSortDown,
  FaSearch,
  FaChevronLeft,
  FaChevronRight,
  FaAngleDoubleLeft,
  FaAngleDoubleRight,
} from 'react-icons/fa';

// تعريف أنواع البيانات
export type DataTableProps<TData, TValue> = {
  columns: ColumnDef<TData, TValue>[];
  data: TData[];
  onRowClick?: (row: TData) => void;
  isLoading?: boolean;
  showSearch?: boolean;
  searchPlaceholder?: string;
  emptyMessage?: string;
  className?: string;
  rowsPerPageOptions?: number[];
  defaultPageSize?: number;
};

// مكون الجدول الرئيسي
export function DataTable<TData, TValue>({
  columns,
  data,
  onRowClick,
  isLoading = false,
  showSearch = true,
  searchPlaceholder = "بحث...",
  emptyMessage = "لا توجد بيانات",
  className = "",
  rowsPerPageOptions = [10, 25, 50, 100],
  defaultPageSize = 10,
}: DataTableProps<TData, TValue>) {
  // حالة الفرز
  const [sorting, setSorting] = useState<SortingState>([]);

  // حالة التصفية
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);

  // حالة البحث العام
  const [globalFilter, setGlobalFilter] = useState('');

  // إضافة عمود الترقيم
  const numberedColumns = useMemo(() => {
    const indexColumn: ColumnDef<TData, any> = {
      id: 'index',
      header: '#',
      cell: ({ row }) => <div className="text-center">{row.index + 1}</div>,
      enableSorting: false,
      size: 50,
    };
    return [indexColumn, ...columns];
  }, [columns]);

  // إنشاء جدول React Table
  const table = useReactTable({
    data,
    columns: numberedColumns,
    state: {
      sorting,
      columnFilters,
      globalFilter,
    },
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onGlobalFilterChange: setGlobalFilter,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    initialState: {
      pagination: {
        pageSize: defaultPageSize,
      },
    },
    // منع إعادة حساب النموذج المصفى تلقائيًا
    autoResetPageIndex: false,
  });

  // متغيرات useMemo التي كانت داخل JSX
  const filteredRowsCount = useMemo(() => table.getFilteredRowModel().rows.length, [table]);
  const headerGroups = useMemo(() => table.getHeaderGroups(), [table]);
  const rowModelRows = useMemo(() => table.getRowModel().rows, [table]);
  const rowModelRowsLength = useMemo(() => table.getRowModel().rows.length, [table]);
  const pageCount = useMemo(() => table.getPageCount(), [table]);
  const canPreviousPage = useMemo(() => table.getCanPreviousPage(), [table]);
  const canNextPage = useMemo(() => table.getCanNextPage(), [table]);

  // تحديد لون الخلفية للصفوف بالتناوب
  const getRowClassName = (index: number) => {
    return index % 2 === 0 ? 'bg-white' : 'bg-gray-50';
  };

  return (
    <div className={`bg-white rounded-lg shadow-md p-4 ${className}`}>
      {/* شريط البحث */}
      {showSearch && (
        <div className="mb-4 flex justify-between items-center">
          <div className="relative w-1/3"> {/* تم تغيير العرض إلى 1/3 */}
            <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
              <FaSearch className="text-gray-400" />
            </div>
            <input
              type="text"
              value={globalFilter ?? ''}
              onChange={(e) => setGlobalFilter(e.target.value)}
              placeholder={searchPlaceholder}
              className="block w-full p-2 pr-10 text-sm text-gray-900 border border-gray-300 rounded-lg bg-gray-50 focus:ring-primary focus:border-primary"
            />
          </div>
          <div className="flex items-center text-sm text-gray-500">
            {filteredRowsCount} من أصل {data.length} سجل
          </div>
        </div>
      )}

      {/* الجدول */}
      <div className="overflow-x-auto">
        <table className="w-full text-sm text-right text-gray-700 border-collapse">
          <thead className="text-xs text-gray-700 uppercase bg-gray-100">
            {headerGroups.map((headerGroup) => (
              <tr key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <th
                    key={header.id}
                    className="px-4 py-3 font-medium border-b"
                    style={{ width: header.getSize() !== 150 ? header.getSize() : undefined }}
                  >
                    {header.isPlaceholder ? null : (
                      <div
                        className={`flex items-center justify-between ${
                          header.column.getCanSort() ? 'cursor-pointer select-none' : ''
                        }`}
                        onClick={header.column.getToggleSortingHandler()}
                      >
                        {flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )}
                        {header.column.getCanSort() && (
                          <div className="mr-2">
                            {header.column.getIsSorted() === 'asc' ? (
                              <FaSortUp className="text-primary" />
                            ) : header.column.getIsSorted() === 'desc' ? (
                              <FaSortDown className="text-primary" />
                            ) : (
                              <FaSort className="text-gray-400" />
                            )}
                          </div>
                        )}
                      </div>
                    )}
                  </th>
                ))}
              </tr>
            ))}
          </thead>
          <tbody>
            {isLoading ? (
              <tr>
                <td
                  colSpan={numberedColumns.length}
                  className="px-4 py-8 text-center"
                >
                  <div className="flex justify-center items-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                    <span className="mr-3">جاري التحميل...</span>
                  </div>
                </td>
              </tr>
            ) : rowModelRowsLength === 0 ? (
              <tr>
                <td
                  colSpan={numberedColumns.length}
                  className="px-4 py-8 text-center text-gray-500"
                >
                  {emptyMessage}
                </td>
              </tr>
            ) : (
              rowModelRows.map((row, index) => (
                <tr
                  key={row.id}
                  className={`${getRowClassName(index)} ${
                    onRowClick ? 'cursor-pointer hover:bg-gray-100' : ''
                  } transition-colors duration-150 ease-in-out`}
                  onClick={() => onRowClick && onRowClick(row.original)}
                >
                  {row.getVisibleCells().map((cell) => (
                    <td key={cell.id} className="px-4 py-3 border-b">
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                    </td>
                  ))}
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      {/* ترقيم الصفحات */}
      <div className="flex flex-col md:flex-row justify-between items-center mt-4 gap-4">
        <div className="flex items-center gap-2">
          <span className="text-sm text-gray-700">
            عرض
          </span>
          <select
            value={table.getState().pagination.pageSize}
            onChange={(e) => table.setPageSize(Number(e.target.value))}
            className="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary focus:border-primary block p-1"
          >
            {rowsPerPageOptions.map((pageSize) => (
              <option key={pageSize} value={pageSize}>
                {pageSize}
              </option>
            ))}
          </select>
          <span className="text-sm text-gray-700">
            من أصل {filteredRowsCount} سجل
          </span>
        </div>

        <div className="flex items-center gap-2">
          {/* أزرار ترقيم الصفحات */}
          <button
            onClick={() => table.setPageIndex(0)}
            disabled={!canPreviousPage}
            className={`p-1 rounded-md ${
              canPreviousPage
                ? 'text-primary hover:bg-gray-100'
                : 'text-gray-300 cursor-not-allowed'
            }`}
          >
            <FaAngleDoubleRight size={18} />
          </button>
          <button
            onClick={() => table.previousPage()}
            disabled={!canPreviousPage}
            className={`p-1 rounded-md ${
              canPreviousPage
                ? 'text-primary hover:bg-gray-100'
                : 'text-gray-300 cursor-not-allowed'
            }`}
          >
            <FaChevronRight size={18} />
          </button>
          <span className="text-sm text-gray-700">
            صفحة{' '}
            <strong>
              {table.getState().pagination.pageIndex + 1} من{' '}
              {pageCount}
            </strong>
          </span>
          <button
            onClick={() => table.nextPage()}
            disabled={!canNextPage}
            className={`p-1 rounded-md ${
              canNextPage
                ? 'text-primary hover:bg-gray-100'
                : 'text-gray-300 cursor-not-allowed'
            }`}
          >
            <FaChevronLeft size={18} />
          </button>
          <button
            onClick={() => table.setPageIndex(pageCount - 1)}
            disabled={!canNextPage}
            className={`p-1 rounded-md ${
              canNextPage
                ? 'text-primary hover:bg-gray-100'
                : 'text-gray-300 cursor-not-allowed'
            }`}
          >
            <FaAngleDoubleLeft size={18} />
          </button>
        </div>
      </div>
    </div>
  );
}
