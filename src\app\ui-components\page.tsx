"use client";

import React, { useState } from 'react';
import Layout from "@/components/Layout";
import { 
  <PERSON>con, 
  But<PERSON>, 
  Card, 
  Badge, 
  Alert, 
  DataTable 
} from '@/components/ui';

export default function UIComponentsPage() {
  const [showAlert, setShowAlert] = useState(true);

  // بيانات للجدول
  const data = [
    { id: 1, name: 'أحمد محمد', age: 25, city: 'القاهرة', status: 'نشط' },
    { id: 2, name: 'سارة أحمد', age: 30, city: 'الإسكندرية', status: 'غير نشط' },
    { id: 3, name: 'محمد علي', age: 28, city: 'الجيزة', status: 'نشط' },
    { id: 4, name: 'فاطمة محمود', age: 22, city: 'أسيوط', status: 'معلق' },
    { id: 5, name: 'خا<PERSON>د عبدالله', age: 35, city: 'المنصورة', status: 'نشط' },
  ];

  // تعريف أعمدة الجدول
  const columns = [
    { key: 'id', header: 'الرقم', width: 'w-16' },
    { key: 'name', header: 'الاسم', sortable: true },
    { key: 'age', header: 'العمر', sortable: true },
    { key: 'city', header: 'المدينة', sortable: true },
    { 
      key: 'status', 
      header: 'الحالة', 
      sortable: true,
      render: (item: any) => {
        const statusMap: Record<string, { variant: string, icon: string }> = {
          'نشط': { variant: 'success', icon: 'FaCheckCircle' },
          'غير نشط': { variant: 'danger', icon: 'FaTimesCircle' },
          'معلق': { variant: 'warning', icon: 'FaExclamationCircle' },
        };
        
        const { variant, icon } = statusMap[item.status] || { variant: 'light', icon: 'FaInfoCircle' };
        
        return (
          <Badge variant={variant as any} icon={icon as any} size="md">
            {item.status}
          </Badge>
        );
      }
    },
  ];

  // تعريف إجراءات الجدول
  const actions = [
    { 
      icon: 'FaEye', 
      label: 'عرض', 
      color: 'PRIMARY',
      onClick: (item: any) => alert(`عرض العنصر: ${item.name}`) 
    },
    { 
      icon: 'FaEdit', 
      label: 'تعديل', 
      color: 'SECONDARY',
      onClick: (item: any) => alert(`تعديل العنصر: ${item.name}`) 
    },
    { 
      icon: 'FaTrash', 
      label: 'حذف', 
      color: 'DANGER',
      onClick: (item: any) => alert(`حذف العنصر: ${item.name}`) 
    },
  ];

  return (
    <Layout>
      <div className="p-6">
        <h1 className="text-2xl font-bold mb-6">مكتبة المكونات</h1>

        {/* قسم الأيقونات */}
        <section className="mb-8">
          <h2 className="text-xl font-semibold mb-4">الأيقونات</h2>
          <Card>
            <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
              <div className="flex flex-col items-center p-4 border rounded-md">
                <Icon name="FaHome" color="PRIMARY" size="LG" />
                <span className="mt-2 text-sm">FaHome</span>
              </div>
              <div className="flex flex-col items-center p-4 border rounded-md">
                <Icon name="FaUser" color="SECONDARY" size="LG" />
                <span className="mt-2 text-sm">FaUser</span>
              </div>
              <div className="flex flex-col items-center p-4 border rounded-md">
                <Icon name="FaCheckCircle" color="SUCCESS" size="LG" />
                <span className="mt-2 text-sm">FaCheckCircle</span>
              </div>
              <div className="flex flex-col items-center p-4 border rounded-md">
                <Icon name="FaExclamationTriangle" color="DANGER" size="LG" />
                <span className="mt-2 text-sm">FaExclamationTriangle</span>
              </div>
              <div className="flex flex-col items-center p-4 border rounded-md">
                <Icon name="FaInfoCircle" color="INFO" size="LG" />
                <span className="mt-2 text-sm">FaInfoCircle</span>
              </div>
              <div className="flex flex-col items-center p-4 border rounded-md">
                <Icon name="FaBell" color="WARNING" size="LG" />
                <span className="mt-2 text-sm">FaBell</span>
              </div>
            </div>
          </Card>
        </section>

        {/* قسم الأزرار */}
        <section className="mb-8">
          <h2 className="text-xl font-semibold mb-4">الأزرار</h2>
          <Card>
            <div className="space-y-4">
              <div className="flex flex-wrap gap-2">
                <Button variant="primary">أساسي</Button>
                <Button variant="secondary">ثانوي</Button>
                <Button variant="success">نجاح</Button>
                <Button variant="danger">خطر</Button>
                <Button variant="info">معلومات</Button>
                <Button variant="warning">تحذير</Button>
              </div>
              <div className="flex flex-wrap gap-2">
                <Button variant="outline-primary">أساسي</Button>
                <Button variant="outline-secondary">ثانوي</Button>
                <Button variant="outline-success">نجاح</Button>
                <Button variant="outline-danger">خطر</Button>
                <Button variant="outline-info">معلومات</Button>
                <Button variant="outline-warning">تحذير</Button>
              </div>
              <div className="flex flex-wrap gap-2">
                <Button variant="primary" icon="FaPlus">إضافة</Button>
                <Button variant="success" icon="FaSave">حفظ</Button>
                <Button variant="danger" icon="FaTrash">حذف</Button>
                <Button variant="info" icon="FaSearch" iconPosition="right">بحث</Button>
              </div>
              <div className="flex flex-wrap gap-2">
                <Button variant="primary" size="sm">صغير</Button>
                <Button variant="primary">متوسط</Button>
                <Button variant="primary" size="lg">كبير</Button>
                <Button variant="primary" rounded>دائري</Button>
                <Button variant="primary" disabled>معطل</Button>
              </div>
            </div>
          </Card>
        </section>

        {/* قسم البطاقات */}
        <section className="mb-8">
          <h2 className="text-xl font-semibold mb-4">البطاقات</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card
              title="بطاقة بسيطة"
              icon="FaInfoCircle"
              iconColor="PRIMARY"
            >
              <p>هذه بطاقة بسيطة مع عنوان وأيقونة.</p>
            </Card>
            
            <Card
              title="بطاقة مع إجراءات"
              icon="FaUserGraduate"
              iconColor="SECONDARY"
              actions={
                <div className="flex gap-2">
                  <Button variant="outline-primary" size="sm" icon="FaEdit">تعديل</Button>
                  <Button variant="outline-danger" size="sm" icon="FaTrash">حذف</Button>
                </div>
              }
            >
              <p>هذه بطاقة مع إجراءات في الرأس.</p>
            </Card>
            
            <Card
              title="بطاقة مع تذييل"
              icon="FaChartBar"
              iconColor="SUCCESS"
              footer={
                <div className="flex justify-end">
                  <Button variant="primary" size="sm">حفظ</Button>
                </div>
              }
            >
              <p>هذه بطاقة مع تذييل يحتوي على زر.</p>
            </Card>
            
            <Card
              title="بطاقة بدون حدود وظل"
              icon="FaCog"
              iconColor="INFO"
              bordered={false}
              shadow={false}
            >
              <p>هذه بطاقة بدون حدود وظل.</p>
            </Card>
          </div>
        </section>

        {/* قسم الشارات */}
        <section className="mb-8">
          <h2 className="text-xl font-semibold mb-4">الشارات</h2>
          <Card>
            <div className="space-y-4">
              <div className="flex flex-wrap gap-2">
                <Badge variant="primary">أساسي</Badge>
                <Badge variant="secondary">ثانوي</Badge>
                <Badge variant="success">نجاح</Badge>
                <Badge variant="danger">خطر</Badge>
                <Badge variant="info">معلومات</Badge>
                <Badge variant="warning">تحذير</Badge>
              </div>
              <div className="flex flex-wrap gap-2">
                <Badge variant="primary" icon="FaCheckCircle">مكتمل</Badge>
                <Badge variant="danger" icon="FaTimesCircle">مرفوض</Badge>
                <Badge variant="warning" icon="FaExclamationCircle">معلق</Badge>
                <Badge variant="info" icon="FaInfoCircle">معلومات</Badge>
              </div>
              <div className="flex flex-wrap gap-2">
                <Badge variant="primary" size="sm">صغير</Badge>
                <Badge variant="primary">متوسط</Badge>
                <Badge variant="primary" size="lg">كبير</Badge>
                <Badge variant="primary" pill>دائري</Badge>
              </div>
            </div>
          </Card>
        </section>

        {/* قسم التنبيهات */}
        <section className="mb-8">
          <h2 className="text-xl font-semibold mb-4">التنبيهات</h2>
          <div className="space-y-4">
            <Alert variant="primary" title="معلومات">
              هذا تنبيه معلوماتي يحتوي على معلومات مهمة.
            </Alert>
            
            <Alert variant="success" title="نجاح">
              تم حفظ البيانات بنجاح.
            </Alert>
            
            <Alert variant="warning" title="تحذير">
              يرجى الانتباه إلى هذا التحذير.
            </Alert>
            
            <Alert variant="danger" title="خطأ" dismissible>
              حدث خطأ أثناء معالجة طلبك. يرجى المحاولة مرة أخرى.
            </Alert>
            
            <Alert variant="info" icon="FaInfoCircle">
              هذا تنبيه بأيقونة مخصصة.
            </Alert>
          </div>
        </section>

        {/* قسم جدول البيانات */}
        <section className="mb-8">
          <h2 className="text-xl font-semibold mb-4">جدول البيانات</h2>
          <Card>
            <DataTable
              data={data}
              columns={columns}
              actions={actions}
              keyExtractor={(item) => item.id}
              searchable
              searchKeys={['name', 'city', 'status']}
              pagination
              itemsPerPage={3}
              bordered
              striped
              hoverable
            />
          </Card>
        </section>
      </div>
    </Layout>
  );
}
