"use client";

import React, { useEffect, useState } from 'react';
import { supabase } from '@/lib/supabase';

interface Attachment {
  id: string;
  name: string;
  type: string;
  file: File | null;
  url: string;
  uploaded: boolean;
  uploading: boolean;
  error: string | null;
}

interface Step9Props {
  onComplete: (data: any, isValid: boolean) => void;
  formData: Record<string, any>;
}

const Step9Attachments: React.FC<Step9Props> = ({ onComplete, formData }) => {
  const [attachments, setAttachments] = useState<Attachment[]>([
    {
      id: 'photo',
      name: 'صورة شخصية للطالب',
      type: 'image',
      file: null,
      url: formData.photoUrl || '',
      uploaded: !!formData.photoUrl,
      uploading: false,
      error: null,
    },
    {
      id: 'birthCertificate',
      name: 'شهادة الميلاد',
      type: 'document',
      file: null,
      url: formData.birthCertificateUrl || '',
      uploaded: !!formData.birthCertificateUrl,
      uploading: false,
      error: null,
    },
    {
      id: 'idCard',
      name: 'بطاقة الهوية الوطنية',
      type: 'document',
      file: null,
      url: formData.idCardUrl || '',
      uploaded: !!formData.idCardUrl,
      uploading: false,
      error: null,
    },
    {
      id: 'academicRecord',
      name: 'السجل الأكاديمي',
      type: 'document',
      file: null,
      url: formData.academicRecordUrl || '',
      uploaded: !!formData.academicRecordUrl,
      uploading: false,
      error: null,
    },
    {
      id: 'healthDocuments',
      name: 'مستندات صحية أو تأمينية',
      type: 'document',
      file: null,
      url: formData.healthDocumentsUrl || '',
      uploaded: !!formData.healthDocumentsUrl,
      uploading: false,
      error: null,
    },
  ]);

  // تحديث البيانات عند تغييرها
  useEffect(() => {
    const attachmentData = {
      photoUrl: attachments.find(a => a.id === 'photo')?.url || '',
      birthCertificateUrl: attachments.find(a => a.id === 'birthCertificate')?.url || '',
      idCardUrl: attachments.find(a => a.id === 'idCard')?.url || '',
      academicRecordUrl: attachments.find(a => a.id === 'academicRecord')?.url || '',
      healthDocumentsUrl: attachments.find(a => a.id === 'healthDocuments')?.url || '',
    };
    onComplete(attachmentData, true); // هذه الخطوة اختيارية، لذلك دائمًا صالحة
  }, [attachments, onComplete]);

  // اختيار ملف
  const handleFileChange = (id: string, e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      setAttachments(prev =>
        prev.map(attachment =>
          attachment.id === id
            ? { ...attachment, file, uploaded: false, error: null }
            : attachment
        )
      );
      handleUpload(id, file);
    }
  };

  // رفع الملف عبر API route بدلاً من Supabase مباشرة
  const handleUpload = async (id: string, file: File) => {
    setAttachments(prev =>
      prev.map(attachment =>
        attachment.id === id
          ? { ...attachment, uploading: true, error: null }
          : attachment
      )
    );

    const fileSizeInMB = file.size / (1024 * 1024);
    const maxSizeInMB = file.type.startsWith('image/') ? 5 : 10;
    if (fileSizeInMB > maxSizeInMB) {
      setAttachments(prev =>
        prev.map(attachment =>
          attachment.id === id
            ? {
                ...attachment,
                uploading: false,
                error: `حجم الملف (${fileSizeInMB.toFixed(2)} ميجابايت) يتجاوز الحد الأقصى المسموح به (${maxSizeInMB} ميجابايت)`
              }
            : attachment
        )
      );
      return;
    }

    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('id', id);
      const res = await fetch('/api/attachments/upload', {
        method: 'POST',
        body: formData,
      });
      const result = await res.json();
      if (!res.ok || !result.url) {
        throw new Error(result.error || 'فشل رفع الملف');
      }
      setAttachments(prev =>
        prev.map(attachment =>
          attachment.id === id
            ? {
                ...attachment,
                url: result.url,
                uploaded: true,
                uploading: false
              }
            : attachment
        )
      );
    } catch (error: any) {
      setAttachments(prev =>
        prev.map(attachment =>
          attachment.id === id
            ? {
                ...attachment,
                uploading: false,
                error: error.message || 'حدث خطأ أثناء رفع الملف'
              }
            : attachment
        )
      );
    }
  };

  // حذف ملف
  const handleDelete = async (id: string) => {
    const attachment = attachments.find(a => a.id === id);
    if (!attachment || !attachment.url) return;

    try {
      // استخراج اسم الملف من URL
      const url = new URL(attachment.url);
      const pathParts = url.pathname.split('/');
      // المسار الكامل للملف بعد attachments/
      const filePath = pathParts.slice(pathParts.indexOf('attachments')).join('/');

      // حذف الملف عبر API route
      const res = await fetch('/api/attachments/delete', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ filePath }),
      });
      const result = await res.json();
      if (!res.ok) {
        throw new Error(result.error || 'فشل حذف الملف');
      }

      // تحديث حالة المرفق في واجهة المستخدم بغض النظر عن نتيجة الحذف
      setAttachments(prev =>
        prev.map(a =>
          a.id === id
            ? { ...a, file: null, url: '', uploaded: false, error: null }
            : a
        )
      );
      console.log('Attachment state updated in UI');
    } catch (error) {
      console.error('Error in delete process:', error);
      setAttachments(prev =>
        prev.map(a =>
          a.id === id
            ? { ...a, error: `حدث خطأ أثناء حذف الملف: ${error instanceof Error ? error.message : 'خطأ غير معروف'}. يمكنك المحاولة مرة أخرى أو تخطي هذه الخطوة.` }
            : a
        )
      );
    }
  };

  return (
    <div className="bg-white rounded-lg p-6">
      <h2 className="text-xl font-bold text-primary mb-6 border-r-4 border-primary pr-3">
        المرفقات
      </h2>

      <div className="bg-green-50 border-r-4 border-green-400 p-4 mb-6">
        <p className="text-green-700">
          يمكنك رفع المستندات هنا (اختياري). الصور المقبولة بتنسيق JPG أو PNG، والمستندات بتنسيق PDF.
        </p>
        <p className="text-green-700 mt-2 font-bold">
          ملاحظة: جميع المرفقات اختيارية تمامًا ويمكنك تخطي هذه الخطوة للحفاظ على مساحة التخزين.
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {attachments.map((attachment) => (
          <div
            key={attachment.id}
            className="p-4 border border-gray-200 rounded-lg bg-gray-50"
          >
            <div className="flex justify-between items-start mb-3">
              <h3 className="font-medium text-gray-700">{attachment.name}</h3>
              <span className={`text-xs px-2 py-1 rounded-full ${
                attachment.uploaded
                  ? 'bg-success/10 text-success'
                  : 'bg-gray-200 text-gray-600'
              }`}>
                {attachment.uploaded ? 'تم الرفع' : 'لم يتم الرفع'}
              </span>
            </div>

            {attachment.url ? (
              <div className="mb-3">
                {attachment.type === 'image' ? (
                  <div className="relative w-full h-40 bg-gray-200 rounded-md overflow-hidden">
                    <img
                      src={attachment.url}
                      alt={attachment.name}
                      className="w-full h-full object-cover"
                    />
                  </div>
                ) : (
                  <div className="flex items-center p-3 bg-gray-100 rounded-md">
                    <span className="material-icons text-primary ml-2">description</span>
                    <span className="text-sm truncate flex-1">تم رفع المستند</span>
                    <a
                      href={attachment.url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-primary hover:underline text-sm"
                    >
                      عرض
                    </a>
                  </div>
                )}

                <button
                  type="button"
                  onClick={() => handleDelete(attachment.id)}
                  className="mt-2 text-danger hover:text-danger/80 text-sm flex items-center"
                >
                  <span className="material-icons text-sm ml-1">delete</span>
                  حذف
                </button>
              </div>
            ) : (
              <div className="mb-3">
                <label
                  htmlFor={`file-${attachment.id}`}
                  className="block w-full p-4 border-2 border-dashed border-gray-300 rounded-md text-center cursor-pointer hover:bg-gray-100 transition-colors"
                >
                  <span className="material-icons text-gray-400 text-3xl mb-2">
                    {attachment.type === 'image' ? 'add_photo_alternate' : 'upload_file'}
                  </span>
                  <span className="block text-gray-500">
                    انقر لاختيار ملف أو اسحب الملف هنا
                  </span>
                  <span className="block text-xs text-gray-400 mt-1">
                    {attachment.type === 'image'
                      ? 'JPG أو PNG، بحد أقصى 5 ميجابايت'
                      : 'PDF، بحد أقصى 10 ميجابايت'}
                  </span>
                </label>
                <input
                  type="file"
                  id={`file-${attachment.id}`}
                  accept={attachment.type === 'image' ? 'image/jpeg,image/png' : 'application/pdf'}
                  className="hidden"
                  onChange={(e) => handleFileChange(attachment.id, e)}
                />
              </div>
            )}

            {attachment.uploading && (
              <div className="w-full bg-gray-200 rounded-full h-2.5 mb-2">
                <div className="bg-primary h-2.5 rounded-full w-3/4 animate-pulse"></div>
                <p className="text-xs text-gray-500 mt-1">جاري الرفع...</p>
              </div>
            )}

            {attachment.error && (
              <p className="text-danger text-sm mt-1">{attachment.error}</p>
            )}
          </div>
        ))}
      </div>

      <div className="mt-6 p-4 bg-gray-50 rounded-lg border border-gray-200">
        <h3 className="text-lg font-semibold text-primary mb-2">ملاحظات هامة</h3>
        <ul className="list-disc list-inside space-y-1 text-gray-600">
          <li>جميع المرفقات <span className="font-bold text-success">اختيارية تمامًا</span> ويمكنك تخطي هذه الخطوة.</li>
          <li>يجب أن تكون جميع المستندات واضحة وكاملة.</li>
          <li>الحد الأقصى لحجم الصور هو 5 ميجابايت.</li>
          <li>الحد الأقصى لحجم المستندات هو 10 ميجابايت.</li>
          <li>يمكنك رفع المستندات لاحقاً إذا لم تكن متوفرة حالياً.</li>
        </ul>

        <div className="mt-4 text-center">
          <button
            type="button"
            onClick={() => onComplete({}, true)}
            className="bg-gray-200 hover:bg-gray-300 text-gray-700 px-6 py-2 rounded-md"
          >
            تخطي هذه الخطوة
          </button>
        </div>
      </div>
    </div>
  );
};

export default Step9Attachments;
