"use client";

import { useState, useEffect, useRef } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/lib/supabase';
import { Installment } from '@/types/table.types';
import { RealtimeChannel } from '@supabase/supabase-js';

interface UseRealtimeInstallmentsProps {
  financialInfoId: number | null;
  tuitionFee: number;
  discountAmount: number;
  installmentsCount: number;
}

export const useRealtimeInstallments = ({
  financialInfoId,
  tuitionFee,
  discountAmount,
  installmentsCount
}: UseRealtimeInstallmentsProps) => {
  const queryClient = useQueryClient();
  const totalAmount = tuitionFee - discountAmount;
  const realtimeChannelRef = useRef<RealtimeChannel | null>(null);

  // Fetch installments from the database
  const {
    data: installments = [],
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['installments', financialInfoId],
    queryFn: async () => {
      if (!financialInfoId) return [];

      const { data, error } = await supabase
        .from('installments')
        .select('*')
        .eq('financial_info_id', financialInfoId)
        .order('due_date', { ascending: true });

      if (error) {
        console.error("Error fetching installments:", error);
        throw error;
      }

      return data || [];
    },
    enabled: !!financialInfoId,
  });

  // Set up realtime subscription
  useEffect(() => {
    if (!financialInfoId) return;

    // Clean up any existing subscription
    if (realtimeChannelRef.current) {
      realtimeChannelRef.current.unsubscribe();
    }

    // Create a new subscription
    const channel = supabase
      .channel(`installments-${financialInfoId}`)
      .on('postgres_changes', {
        event: '*',
        schema: 'public',
        table: 'installments',
        filter: `financial_info_id=eq.${financialInfoId}`
      }, (payload) => {
        console.log('Realtime update received:', payload);
        // Invalidate the query to trigger a refetch
        queryClient.invalidateQueries({ queryKey: ['installments', financialInfoId] });
      })
      .subscribe((status) => {
        console.log('Realtime subscription status:', status);
      });

    realtimeChannelRef.current = channel;

    // Clean up subscription on unmount
    return () => {
      if (realtimeChannelRef.current) {
        realtimeChannelRef.current.unsubscribe();
      }
    };
  }, [financialInfoId, queryClient]);

  // Calculate installment amount with proper financial logic
  const calculateInstallmentAmount = (tuitionFee: number, discountAmount: number, paidAmount: number, count: number) => {
    if (count <= 0) return 0;

    // Calculate the remaining amount after discount and paid amount
    const remainingAmount = Math.max(0, tuitionFee - discountAmount - paidAmount);

    // If nothing remains to be paid, return 0
    if (remainingAmount <= 0) return 0;

    // Divide the remaining amount by the number of installments
    // Round to 2 decimal places for currency
    return Math.round((remainingAmount / count) * 100) / 100;
  };

  // Create or update installments based on count and total amount
  const syncInstallments = async () => {
    if (!financialInfoId) {
      console.error("Invalid inputs for syncInstallments: No financial info ID");
      return [];
    }

    try {
      // Get the latest financial info from the database to ensure we have the most up-to-date values
      const { data: financialData, error: financialError } = await supabase
        .from('financial_info')
        .select('*')
        .eq('id', financialInfoId)
        .single();

      if (financialError) {
        console.error("Error fetching financial info:", financialError);
        throw financialError;
      }

      if (!financialData) {
        console.error("No financial data found for ID:", financialInfoId);
        return [];
      }

      // Extract values from financial data
      const dbTuitionFee = parseFloat(financialData.tuition_fee?.toString() || '0');
      const dbDiscountAmount = parseFloat(financialData.discount_amount?.toString() || '0');
      const dbPaidAmount = parseFloat(financialData.paid_amount?.toString() || '0');
      const dbInstallmentsCount = parseInt(financialData.installments_count?.toString() || '0');

      // Use the values from the database, not from the component state
      const paidAmount = dbPaidAmount;
      const currentTuitionFee = dbTuitionFee;
      const currentDiscountAmount = dbDiscountAmount;
      const currentInstallmentsCount = dbInstallmentsCount;

      console.log(`Syncing installments: count=${currentInstallmentsCount}, tuitionFee=${currentTuitionFee}, discountAmount=${currentDiscountAmount}, paidAmount=${paidAmount}`);

      // If installments count is 0, delete all installments and return
      if (currentInstallmentsCount <= 0) {
        console.log("Installments count is 0, deleting all installments");
        const { error: deleteError } = await supabase
          .from('installments')
          .delete()
          .eq('financial_info_id', financialInfoId);

        if (deleteError) {
          console.error("Error deleting installments:", deleteError);
        }
        return [];
      }

      // First, delete all existing installments
      const { error: deleteError } = await supabase
        .from('installments')
        .delete()
        .eq('financial_info_id', financialInfoId);

      if (deleteError) {
        console.error("Error deleting existing installments:", deleteError);
        throw deleteError;
      }

      // Create new installments
      const today = new Date();
      const installmentsToInsert = [];

      // Calculate the remaining amount after discount and paid amount
      const remainingAmount = Math.max(0, currentTuitionFee - currentDiscountAmount - paidAmount);
      console.log(`Remaining amount for installments: ${remainingAmount}`);

      // Only create installments if there's an amount to be paid
      if (remainingAmount > 0 && currentInstallmentsCount > 0) {
        // Calculate the amount per installment
        const installmentAmount = Math.round((remainingAmount / currentInstallmentsCount) * 100) / 100;

        // Calculate any rounding difference to add to the last installment
        const roundingDifference = remainingAmount - (installmentAmount * currentInstallmentsCount);

        for (let i = 0; i < currentInstallmentsCount; i++) {
          const dueDate = new Date(today);
          dueDate.setMonth(today.getMonth() + i + 1);

          // For the last installment, add any rounding difference
          const amount = i === currentInstallmentsCount - 1
            ? installmentAmount + roundingDifference
            : installmentAmount;

          installmentsToInsert.push({
            financial_info_id: financialInfoId,
            amount: amount,
            due_date: dueDate.toISOString().split('T')[0],
            paid: 0,
            discount: 0,
            status: 'pending',
            created_at: new Date().toISOString()
          });
        }
      }

      // Insert all installments at once
      const { error: insertError } = await supabase
        .from('installments')
        .insert(installmentsToInsert);

      if (insertError) {
        console.error("Error inserting installments:", insertError);
        throw insertError;
      }

      // The realtime subscription will trigger a refetch
      return true;
    } catch (error) {
      console.error("Error in syncInstallments:", error);
      throw error;
    }
  };

  // Mutation for syncing installments
  const syncMutation = useMutation({
    mutationFn: syncInstallments,
    onSuccess: () => {
      console.log("Successfully synced installments");
    },
    onError: (error) => {
      console.error("Error syncing installments:", error);
    }
  });

  // Update installment
  const updateInstallment = async (id: number, updates: Partial<Installment>) => {
    if (!id) {
      console.error("Cannot update installment: no ID provided");
      return;
    }

    try {
      // Calculate remaining if amount, paid, or discount is updated
      if ('amount' in updates || 'paid' in updates || 'discount' in updates) {
        const installment = installments.find(i => i.id === id);
        if (installment) {
          const amount = 'amount' in updates ?
            parseFloat(updates.amount?.toString() || '0') :
            parseFloat(installment.amount?.toString() || '0');

          const paid = 'paid' in updates ?
            parseFloat(updates.paid?.toString() || '0') :
            parseFloat(installment.paid?.toString() || '0');

          const discount = 'discount' in updates ?
            parseFloat(updates.discount?.toString() || '0') :
            parseFloat(installment.discount?.toString() || '0');

          // Update status based on payment
          if (amount - paid - discount <= 0) {
            updates.status = 'paid';
            if (!updates.payment_date) {
              updates.payment_date = new Date().toISOString().split('T')[0];
            }
          } else {
            const dueDate = new Date(installment.due_date || '');
            const today = new Date();

            if (dueDate < today) {
              updates.status = 'overdue';
            } else {
              updates.status = 'pending';
            }
          }
        }
      }

      const { error } = await supabase
        .from('installments')
        .update(updates)
        .eq('id', id);

      if (error) {
        console.error("Error updating installment:", error);
        throw error;
      }

      // The realtime subscription will trigger a refetch
      return true;
    } catch (error) {
      console.error("Error in updateInstallment:", error);
      throw error;
    }
  };

  // Add new installment
  const addInstallment = async () => {
    if (!financialInfoId) {
      console.error("Cannot add installment: no financial info ID");
      return;
    }

    try {
      // Create default due date (next month after the last installment)
      let dueDate = new Date();

      if (installments.length > 0) {
        const lastInstallment = installments[installments.length - 1];
        const lastDueDate = new Date(lastInstallment.due_date || '');
        dueDate = new Date(lastDueDate);
        dueDate.setMonth(dueDate.getMonth() + 1);
      } else {
        dueDate.setMonth(dueDate.getMonth() + 1);
      }

      const { error } = await supabase
        .from('installments')
        .insert({
          financial_info_id: financialInfoId,
          amount: 0,
          due_date: dueDate.toISOString().split('T')[0],
          paid: 0,
          discount: 0,
          status: 'pending',
          created_at: new Date().toISOString()
        });

      if (error) {
        console.error("Error adding installment:", error);
        throw error;
      }

      // The realtime subscription will trigger a refetch
      return true;
    } catch (error) {
      console.error("Error in addInstallment:", error);
      throw error;
    }
  };

  // Delete installment
  const deleteInstallment = async (id: number) => {
    if (!id) {
      console.error("Cannot delete installment: no ID provided");
      return;
    }

    try {
      const { error } = await supabase
        .from('installments')
        .delete()
        .eq('id', id);

      if (error) {
        console.error("Error deleting installment:", error);
        throw error;
      }

      // The realtime subscription will trigger a refetch
      return true;
    } catch (error) {
      console.error("Error in deleteInstallment:", error);
      throw error;
    }
  };

  // Calculate totals with proper financial logic
  const calculateTotals = async () => {
    // Get the latest paid amount from the database
    let paidBeforeInstallments = 0;

    if (financialInfoId) {
      try {
        const { data: financialData, error: financialError } = await supabase
          .from('financial_info')
          .select('paid_amount')
          .eq('id', financialInfoId)
          .single();

        if (!financialError && financialData) {
          paidBeforeInstallments = parseFloat(financialData.paid_amount?.toString() || '0');
        }
      } catch (error) {
        console.error("Error fetching paid amount:", error);
      }
    }

    if (!installments || installments.length === 0) {
      return {
        totalAmount: 0,
        totalPaid: 0,
        totalDiscount: 0,
        totalRemaining: 0,
        originalTuitionFee: tuitionFee,
        globalDiscountAmount: discountAmount,
        paidBeforeInstallments,
        netAmountDue: Math.max(0, tuitionFee - discountAmount)
      };
    }

    // paidBeforeInstallments is already defined above

    // Calculate totals from installments
    const totalAmount = installments.reduce((sum, item) =>
      sum + parseFloat(item.amount?.toString() || '0'), 0);

    const totalPaid = installments.reduce((sum, item) =>
      sum + parseFloat(item.paid?.toString() || '0'), 0);

    const totalDiscount = installments.reduce((sum, item) =>
      sum + parseFloat(item.discount?.toString() || '0'), 0);

    const totalRemaining = installments.reduce((sum, item) => {
      const amount = parseFloat(item.amount?.toString() || '0');
      const paid = parseFloat(item.paid?.toString() || '0');
      const discount = parseFloat(item.discount?.toString() || '0');
      return sum + Math.max(0, amount - paid - discount);
    }, 0);

    // Calculate the net amount due (original tuition - global discount)
    const netAmountDue = Math.max(0, tuitionFee - discountAmount);

    // The total amount of installments should equal the net amount due minus any amount paid before installments
    const expectedTotalAmount = Math.max(0, netAmountDue - paidBeforeInstallments);

    return {
      totalAmount,
      totalPaid,
      totalDiscount,
      totalRemaining,
      originalTuitionFee: tuitionFee,
      globalDiscountAmount: discountAmount,
      paidBeforeInstallments,
      netAmountDue,
      expectedTotalAmount
    };
  };

  // Effect to sync installments when financial info changes
  useEffect(() => {
    if (financialInfoId) {
      console.log("Financial info changed, syncing installments...", {
        financialInfoId,
        installmentsCount,
        tuitionFee,
        discountAmount
      });

      // Debounce the sync to avoid multiple calls
      const timer = setTimeout(() => {
        syncMutation.mutate();
      }, 500);

      return () => clearTimeout(timer);
    }
  }, [financialInfoId, installmentsCount, tuitionFee, discountAmount]);

  return {
    installments,
    isLoading,
    error,
    updateInstallment,
    addInstallment,
    deleteInstallment,
    syncInstallments: syncMutation.mutate,
    isSyncing: syncMutation.isPending,
    calculateTotals,
    refetch
  };
};
