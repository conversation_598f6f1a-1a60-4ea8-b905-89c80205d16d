"use client";

import React from 'react';

interface PaymentMethodSectionProps {
  paidAmount: {
    value: string;
    onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
    onBlur: (e: React.FocusEvent<HTMLElement>) => void;
    error?: string;
    touched?: boolean | any;
  };
  paymentMethod: {
    value: string;
    onChange: (e: React.ChangeEvent<HTMLSelectElement>) => void;
    onBlur: (e: React.FocusEvent<HTMLElement>) => void;
    error?: string;
    touched?: boolean | any;
  };
  installmentsCount: {
    value: string;
    onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
    onBlur: (e: React.FocusEvent<HTMLElement>) => void;
    error?: string;
    touched?: boolean | any;
  };
  isInstallments: boolean;
}

const PaymentMethodSection: React.FC<PaymentMethodSectionProps> = ({
  paidAmount,
  paymentMethod,
  installmentsCount,
  isInstallments
}) => {
  return (
    <>
      {/* القيمة المقدمة */}
      <div>
        <label htmlFor="paidAmount" className="block text-gray-700 font-medium mb-2">
          القيمة المقدمة <span className="text-danger">*</span>
        </label>
        <input
          type="number"
          id="paidAmount"
          name="paidAmount"
          className={`w-full p-3 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary
            ${paidAmount.touched && paidAmount.error ? 'border-danger' : 'border-gray-300'}`}
          onChange={paidAmount.onChange}
          onBlur={paidAmount.onBlur}
          value={paidAmount.value}
          placeholder="أدخل القيمة المقدمة"
        />
        {paidAmount.touched && paidAmount.error && (
          <p className="mt-1 text-danger text-sm">{paidAmount.error}</p>
        )}
      </div>

      {/* طريقة الدفع */}
      <div>
        <label htmlFor="paymentMethod" className="block text-gray-700 font-medium mb-2">
          طريقة الدفع <span className="text-danger">*</span>
        </label>
        <select
          id="paymentMethod"
          name="paymentMethod"
          className={`w-full p-3 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary
            ${paymentMethod.touched && paymentMethod.error ? 'border-danger' : 'border-gray-300'}`}
          onChange={paymentMethod.onChange}
          onBlur={paymentMethod.onBlur}
          value={paymentMethod.value}
        >
          <option value="">اختر طريقة الدفع</option>
          <option value="cash">نقدي</option>
          <option value="installments">تقسيط</option>
        </select>
        {paymentMethod.touched && paymentMethod.error && (
          <p className="mt-1 text-danger text-sm">{paymentMethod.error}</p>
        )}
      </div>

      {/* عدد الأقساط (يظهر فقط عند اختيار طريقة الدفع "تقسيط") */}
      {isInstallments && (
        <div>
          <label htmlFor="installmentsCount" className="block text-gray-700 font-medium mb-2">
            عدد الأقساط <span className="text-danger">*</span>
          </label>
          <input
            type="number"
            id="installmentsCount"
            name="installmentsCount"
            className={`w-full p-3 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary
              ${installmentsCount.touched && installmentsCount.error ? 'border-danger' : 'border-gray-300'}`}
            onChange={installmentsCount.onChange}
            onBlur={installmentsCount.onBlur}
            value={installmentsCount.value}
            placeholder="أدخل عدد الأقساط"
            min="2"
          />
          {installmentsCount.touched && installmentsCount.error && (
            <p className="mt-1 text-danger text-sm">{installmentsCount.error}</p>
          )}
        </div>
      )}
    </>
  );
};

export default PaymentMethodSection;
