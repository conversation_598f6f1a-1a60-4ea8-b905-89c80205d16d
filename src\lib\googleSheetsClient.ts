import { GoogleSpreadsheet } from 'google-spreadsheet';
import { JWT } from 'google-auth-library';

// معلومات حساب الخدمة
const SERVICE_ACCOUNT_EMAIL = process.env.GOOGLE_SHEETS_CLIENT_EMAIL || '<EMAIL>';
const PRIVATE_KEY = `-----B<PERSON>IN PRIVATE KEY-----
MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQCgUDMToOCr887+
eGcYNBIxfxFqeQT6evF1ksdussZxikSeyZrH04evp+tkMT4UnjqM3ggTfClb71xA
s9EzkmIwk3f+OmsohWYVh+miLNKwLwOvLrrFQDvYhdpMm/DfO6NczrtzssKWsfwe
3UTQK3hmHpp3ci0cbUhBcaJP+hLBj69ri4aI3tZjMX/irz3U0D85E9rMEsYK1CE0
oN06mcu9eyGgKQCipbt/VvCsE400mEz2Cp71SkWBivMzRMCXX4o0UCcspOxoLkae
nhFOFVLWpOw8Awwg91lvtiu1/2GYURhld1AuJQK9By2bBDg18a8+V9gqZZRjoyzY
3o5mu3wPAgMBAAECggEAEyfiIotLrWBRsJiEgSDft6heVWGmlaORLKJgUDrueWyz
LGIcq5n8EaI3/13e9CnAB0sUnfdtHb4pQNX7vnTUZMeKRAeI/GuMOjBmW2mNt122
y/0CdkVpoULFRfgwPQxsRc+yGG4Y0FbuMGXyLgmztkgb3v/2ofnw81j3syTHjvdC
7Qs+Bo2Mk73TdJBhzwgGr6XFdoW0OltjBqo9WJOOzEkI8dKV2O9v4nY7ZZUkbCJ9
7qn8cUEZD79z1GpJ0R3Ic55Ykso4s6lcc27tYDrvW72g/vW1BgkF0VV0vguwEVM8
Fy66+RVhCNHJjV4cQ2DEeVhV+RpL1KAwq2sJSxpeQQKBgQDM1KZGsqGf1Fw/7jxe
DPvBphUG37iFAHtROuUIEtM3NaLZEtCU/R+yGjyxR1QHZ1/Zmn1CaP1pRTfATwhl
7kCb5HD/rKdvjVM9cdtWCLxkKvGguRxJhjehnsDUEdVl6b4B+5f6JqKZbcqIfE32
jBqwUW1640IZj0MoXain0GmdIQKBgQDIXJHrNSdfHhHqyHw2IrccZU/Mfi4IKCQB
LFRUN70g+1tPFIfO010hnnOjTvxYJetFYgYSEDinYeuVeWmr4qP/IzM6O8Fu/YA
fGHw4Niq+qYH/GwKTam42cagAHfZMWR1CgeKVyHW6iF3i1Iz4n3aeMWkdrHRGkRR
pNXVIDhDLwKBgDGxtdWZKRU51Fe+WO8If1vwC1HrnTzHbSO8Aiz/RDc00BmSMwCy
mKoBK5BnGb/iGKMWlCU0i6aqoGgLxKHWN96agb/GB6lMxFE3+lrZK7uxn4W6414D
LiQL/AR3fyqGlw6sF6cdEH3g4S5T2iZiv2bfmowh7rt8wnJcprpwGe+BAoGBAMfk
0w++cBFNhsKeUo4QBG+oA/0YxgQNSB6Dg6mhnU6FcJLS43uVRqlCoLOZ5Hvkym48
VRMkwtdUItGjkQLDj5InfLHSkuU9qSjC71PKf1/VwV+mToPZB8MMQkXPMcLWrEq4
Pt2eIHyyCcGildh91uHoLAbxBGaBtjdeJJgFFRyxAoGBAIjUGam8guYQQQz4i7Cb
DOVgO9f/7ntcDXcc3Gc/mA9pkEs4sIyatu8Zg/TzTBZLsXYlYndCrqBxIMSIZQ4J
TADB2Sb1MIP6MDtv8j4vshGUXDTQ2J3+QuUGpdzr/VZPuf7XBLL1SLrXEJMk7Pr
PDgo6lIXS1wibIwEmpk4MRPV
-----END PRIVATE KEY-----`;

// تكوين الاتصال بـ Google Sheets API
const SCOPES = [
  'https://www.googleapis.com/auth/spreadsheets',
  'https://www.googleapis.com/auth/drive.file',
];

// إنشاء JWT للمصادقة
const createJWT = () => {
  return new JWT({
    email: SERVICE_ACCOUNT_EMAIL,
    key: PRIVATE_KEY,
    scopes: SCOPES,
  });
};

// تحويل البيانات إلى تنسيق نموذج التسجيل
const mapSheetDataToStudentForm = (rowData: Record<string, any>) => {
  return {
    // البيانات الأساسية
    fullName: rowData['الاسم الكامل'] || rowData['full_name'] || '',
    idNumber: rowData['رقم الهوية'] || rowData['id_number'] || '',
    birthDate: rowData['تاريخ الميلاد'] || rowData['birth_date'] || '',
    gender: rowData['الجنس'] || rowData['gender'] || '',
    religion: rowData['الديانة'] || rowData['religion'] || '',
    maritalStatus: rowData['الحالة الاجتماعية'] || rowData['marital_status'] || '',
    email: rowData['البريد الإلكتروني'] || rowData['email'] || '',
    phone: rowData['رقم الهاتف'] || rowData['phone'] || '',
    
    // بيانات ولي الأمر
    guardians: [{
      fullName: rowData['اسم ولي الأمر'] || rowData['guardian_name'] || '',
      relationship: rowData['صلة القرابة'] || rowData['relationship'] || '',
      idNumber: rowData['رقم هوية ولي الأمر'] || rowData['guardian_id'] || '',
      phone: rowData['هاتف ولي الأمر'] || rowData['guardian_phone'] || '',
      email: rowData['بريد ولي الأمر'] || rowData['guardian_email'] || '',
      occupation: rowData['مهنة ولي الأمر'] || rowData['guardian_occupation'] || '',
      workplace: rowData['مكان العمل'] || rowData['guardian_workplace'] || '',
    }],
    
    // البيانات المالية
    tuitionFee: parseFloat(rowData['الرسوم الدراسية'] || rowData['tuition_fee'] || '0'),
    discountAmount: parseFloat(rowData['مبلغ الخصم'] || rowData['discount_amount'] || '0'),
    discountReason: rowData['سبب الخصم'] || rowData['discount_reason'] || '',
    paidAmount: parseFloat(rowData['المبلغ المدفوع'] || rowData['paid_amount'] || '0'),
    paymentMethod: rowData['طريقة الدفع'] || rowData['payment_method'] || 'نقدي',
    installmentsCount: parseInt(rowData['عدد الأقساط'] || rowData['installments_count'] || '1'),
    
    // المعلومات الصحية
    generalHealth: rowData['الحالة الصحية العامة'] || rowData['general_health'] || '',
    chronicDiseases: rowData['الأمراض المزمنة'] || rowData['chronic_diseases'] || '',
    drugAllergies: rowData['حساسية الأدوية'] || rowData['drug_allergies'] || '',
    doctorName: rowData['اسم الطبيب'] || rowData['doctor_name'] || '',
    doctorPhone: rowData['هاتف الطبيب'] || rowData['doctor_phone'] || '',
    insuranceCompany: rowData['شركة التأمين'] || rowData['insurance_company'] || '',
    insurancePolicyNumber: rowData['رقم وثيقة التأمين'] || rowData['insurance_policy'] || '',
    
    // المعلومات الإضافية
    hobbies: rowData['الهوايات'] || rowData['hobbies'] || '',
    achievements: rowData['الإنجازات'] || rowData['achievements'] || '',
    specialNeeds: rowData['الاحتياجات الخاصة'] || rowData['special_needs'] || '',
    transportationMethod: rowData['وسيلة المواصلات'] || rowData['transportation'] || '',
    additionalNotes: rowData['ملاحظات إضافية'] || rowData['notes'] || '',
  };
};

// استرجاع بيانات الطلاب من Google Sheets عبر API
export const getStudentsFromSheet = async (spreadsheetId: string, sheetIndex = 0) => {
  try {
    const response = await fetch(`/api/google-sheets?spreadsheetId=${spreadsheetId}&sheetIndex=${sheetIndex}`);
    
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'فشل استرجاع البيانات من جدول البيانات');
    }
    
    const data = await response.json();
    return data.data;
  } catch (error) {
    console.error('Error fetching students from sheet:', error);
    throw new Error(error instanceof Error ? error.message : 'فشل استرجاع بيانات الطلاب من جدول البيانات.');
  }
};

// تصدير البيانات إلى Google Sheets
export const exportStudentsToSheet = async () => {
  try {
    const response = await fetch('/api/students/export');
    
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'فشل تصدير البيانات إلى Google Sheets');
    }
    
    const data = await response.json();
    return data.data;
  } catch (error) {
    console.error('Error exporting students to sheet:', error);
    throw new Error(error instanceof Error ? error.message : 'فشل تصدير بيانات الطلاب إلى Google Sheets.');
  }
};

export { createJWT, mapSheetDataToStudentForm };
