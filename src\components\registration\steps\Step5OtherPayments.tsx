"use client";

import React, { useEffect, useState } from 'react';
import { useFormik } from 'formik';
import * as Yup from 'yup';

interface Step5Props {
  onComplete: (data: any, isValid: boolean) => void;
  formData: Record<string, any>;
}

// القيم الثابتة للمدفوعات الأخرى
const FIXED_FEES = {
  fileOpeningFee: 300,
  booksFee: 300,
  uniformPrice: 300,
  transportationFee: 300,
};

const Step5OtherPayments: React.FC<Step5Props> = ({ onComplete, formData }) => {
  // التحقق من صحة البيانات باستخدام Yup
  const validationSchema = Yup.object({
    fileOpeningFee: Yup.number().required('رسوم فتح الملف مطلوبة').min(0, 'يجب أن تكون القيمة أكبر من أو تساوي صفر'),
    booksFee: Yup.number().required('رسوم الكتب مطلوبة').min(0, 'يجب أن تكون القيمة أكبر من أو تساوي صفر'),
    uniformCount: Yup.number().required('عدد قطع الزي المدرسي مطلوب').min(0, 'يجب أن تكون القيمة أكبر من أو تساوي صفر'),
    transportationFee: Yup.number().required('رسوم النقل المدرسي مطلوبة').min(0, 'يجب أن تكون القيمة أكبر من أو تساوي صفر'),
    uniformItems: Yup.object({
      pants: Yup.number().min(0, 'يجب أن تكون القيمة أكبر من أو تساوي صفر'),
      jacket: Yup.number().min(0, 'يجب أن تكون القيمة أكبر من أو تساوي صفر'),
      tshirt: Yup.number().min(0, 'يجب أن تكون القيمة أكبر من أو تساوي صفر'),
    }),
  });

  // إعداد نموذج Formik
  const formik = useFormik({
    initialValues: {
      fileOpeningFee: formData.fileOpeningFee || FIXED_FEES.fileOpeningFee,
      booksFee: formData.booksFee || FIXED_FEES.booksFee,
      uniformCount: formData.uniformCount || 0,
      transportationFee: formData.transportationFee || FIXED_FEES.transportationFee,
      uniformItems: formData.uniformItems || {
        pants: 0,
        jacket: 0,
        tshirt: 0,
      },
    },
    validationSchema,
    onSubmit: (values) => {
      console.log('Form submitted with values:', values);
    },
  });

  // حساب إجمالي الزي المدرسي
  const calculateUniformTotal = () => {
    const { pants, jacket, tshirt } = formik.values.uniformItems;
    return (parseInt(pants) + parseInt(jacket) + parseInt(tshirt)) * FIXED_FEES.uniformPrice;
  };

  // حساب إجمالي المدفوعات الأخرى
  const calculateTotalOtherPayments = () => {
    return (
      parseFloat(formik.values.fileOpeningFee) +
      parseFloat(formik.values.booksFee) +
      calculateUniformTotal() +
      parseFloat(formik.values.transportationFee)
    );
  };

  // تحديث عدد قطع الزي المدرسي عند تغيير القطع الفردية
  useEffect(() => {
    const { pants, jacket, tshirt } = formik.values.uniformItems;
    const totalCount = parseInt(pants) + parseInt(jacket) + parseInt(tshirt);
    formik.setFieldValue('uniformCount', totalCount);
  }, [formik.values.uniformItems]);

  // تحديث البيانات عند تغييرها
  useEffect(() => {
    const isValid = formik.isValid;
    const otherPaymentsData = {
      ...formik.values,
      uniformTotal: calculateUniformTotal(),
      totalAmount: calculateTotalOtherPayments(),
    };
    onComplete(otherPaymentsData, isValid);
  }, [formik.values, formik.isValid, onComplete]);

  // تحديث قطع الزي المدرسي
  const handleUniformItemChange = (item: string, value: string) => {
    formik.setFieldValue(`uniformItems.${item}`, value);
  };

  return (
    <div className="bg-white rounded-lg p-6">
      <h2 className="text-xl font-bold text-primary mb-6 border-r-4 border-primary pr-3">
        المدفوعات الأخرى
      </h2>

      <div className="overflow-x-auto">
        <table className="w-full border-collapse">
          <thead>
            <tr className="bg-primary text-black">
              <th className="p-3 text-right border border-gray-300">البند</th>
              <th className="p-3 text-right border border-gray-300">التفاصيل</th>
              <th className="p-3 text-right border border-gray-300">القيمة</th>
              <th className="p-3 text-right border border-gray-300">الإجمالي</th>
            </tr>
          </thead>
          <tbody>
            {/* رسوم فتح الملف */}
            <tr className="bg-gray-50">
              <td className="p-3 text-right border border-gray-300 font-medium">رسوم فتح الملف</td>
              <td className="p-3 text-right border border-gray-300">رسوم إدارية لفتح ملف الطالب</td>
              <td className="p-3 text-right border border-gray-300">
                <input
                  type="number"
                  id="fileOpeningFee"
                  name="fileOpeningFee"
                  className={`w-full p-2 border rounded-md focus:outline-none focus:ring-1 focus:ring-primary
                    ${formik.touched.fileOpeningFee && formik.errors.fileOpeningFee ? 'border-danger' : 'border-gray-300'}`}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  value={formik.values.fileOpeningFee}
                />
                {formik.touched.fileOpeningFee && formik.errors.fileOpeningFee && (
                  <p className="mt-1 text-danger text-sm">{formik.errors.fileOpeningFee}</p>
                )}
              </td>
              <td className="p-3 text-right border border-gray-300 font-bold">
                {parseFloat(formik.values.fileOpeningFee)} ج.م
              </td>
            </tr>

            {/* رسوم الكتب */}
            <tr className="bg-white">
              <td className="p-3 text-right border border-gray-300 font-medium">رسوم الكتب</td>
              <td className="p-3 text-right border border-gray-300">الكتب الدراسية للعام الدراسي</td>
              <td className="p-3 text-right border border-gray-300">
                <input
                  type="number"
                  id="booksFee"
                  name="booksFee"
                  className={`w-full p-2 border rounded-md focus:outline-none focus:ring-1 focus:ring-primary
                    ${formik.touched.booksFee && formik.errors.booksFee ? 'border-danger' : 'border-gray-300'}`}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  value={formik.values.booksFee}
                />
                {formik.touched.booksFee && formik.errors.booksFee && (
                  <p className="mt-1 text-danger text-sm">{formik.errors.booksFee}</p>
                )}
              </td>
              <td className="p-3 text-right border border-gray-300 font-bold">
                {parseFloat(formik.values.booksFee)} ج.م
              </td>
            </tr>

            {/* الزي المدرسي */}
            <tr className="bg-gray-50">
              <td className="p-3 text-right border border-gray-300 font-medium">الزي المدرسي</td>
              <td className="p-3 text-right border border-gray-300">
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <label htmlFor="pants" className="text-sm">بنطلون ({FIXED_FEES.uniformPrice} ج.م/قطعة):</label>
                    <input
                      type="number"
                      id="pants"
                      name="uniformItems.pants"
                      className="w-20 p-1 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary"
                      onChange={(e) => handleUniformItemChange('pants', e.target.value)}
                      value={formik.values.uniformItems.pants}
                      min="0"
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <label htmlFor="jacket" className="text-sm">جاكيت ({FIXED_FEES.uniformPrice} ج.م/قطعة):</label>
                    <input
                      type="number"
                      id="jacket"
                      name="uniformItems.jacket"
                      className="w-20 p-1 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary"
                      onChange={(e) => handleUniformItemChange('jacket', e.target.value)}
                      value={formik.values.uniformItems.jacket}
                      min="0"
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <label htmlFor="tshirt" className="text-sm">تيشيرت ({FIXED_FEES.uniformPrice} ج.م/قطعة):</label>
                    <input
                      type="number"
                      id="tshirt"
                      name="uniformItems.tshirt"
                      className="w-20 p-1 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary"
                      onChange={(e) => handleUniformItemChange('tshirt', e.target.value)}
                      value={formik.values.uniformItems.tshirt}
                      min="0"
                    />
                  </div>
                </div>
              </td>
              <td className="p-3 text-right border border-gray-300">
                <div className="text-center">
                  <span className="block font-medium">{formik.values.uniformCount} قطعة</span>
                  <span className="block text-sm text-gray-500">({FIXED_FEES.uniformPrice} ج.م/قطعة)</span>
                </div>
              </td>
              <td className="p-3 text-right border border-gray-300 font-bold">
                {calculateUniformTotal()} ج.م
              </td>
            </tr>

            {/* النقل المدرسي */}
            <tr className="bg-white">
              <td className="p-3 text-right border border-gray-300 font-medium">النقل المدرسي</td>
              <td className="p-3 text-right border border-gray-300">رسوم النقل المدرسي الشهرية</td>
              <td className="p-3 text-right border border-gray-300">
                <input
                  type="number"
                  id="transportationFee"
                  name="transportationFee"
                  className={`w-full p-2 border rounded-md focus:outline-none focus:ring-1 focus:ring-primary
                    ${formik.touched.transportationFee && formik.errors.transportationFee ? 'border-danger' : 'border-gray-300'}`}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  value={formik.values.transportationFee}
                />
                {formik.touched.transportationFee && formik.errors.transportationFee && (
                  <p className="mt-1 text-danger text-sm">{formik.errors.transportationFee}</p>
                )}
              </td>
              <td className="p-3 text-right border border-gray-300 font-bold">
                {parseFloat(formik.values.transportationFee)} ج.م
              </td>
            </tr>
          </tbody>
          <tfoot>
            <tr className="bg-gray-100 font-bold">
              <td colSpan={3} className="p-3 text-right border border-gray-300">
                إجمالي المدفوعات الأخرى
              </td>
              <td className="p-3 text-right border border-gray-300 text-primary">
                {calculateTotalOtherPayments()} ج.م
              </td>
            </tr>
          </tfoot>
        </table>
      </div>

      <div className="mt-6 p-4 bg-gray-50 rounded-lg border border-gray-200">
        <h3 className="text-lg font-semibold text-primary mb-2">ملاحظات</h3>
        <ul className="list-disc list-inside space-y-1 text-gray-600">
          <li>جميع الرسوم المذكورة أعلاه إلزامية وتضاف إلى الرسوم الدراسية.</li>
          <li>يمكنك تعديل القيم حسب الحاجة.</li>
          <li>رسوم الزي المدرسي تعتمد على عدد القطع المختارة.</li>
          <li>رسوم النقل المدرسي شهرية وتدفع مقدماً.</li>
        </ul>
      </div>
    </div>
  );
};

export default Step5OtherPayments;
