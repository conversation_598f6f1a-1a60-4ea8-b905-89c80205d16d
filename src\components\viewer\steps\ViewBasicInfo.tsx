"use client";

import React, { useState, useEffect } from 'react';
import { supabase } from '@/lib/supabase';
import { Student } from '@/types/table.types';
import { FaUser, FaIdCard, FaCalendarAlt, FaVenusMars, FaPray, FaRing, FaEnvelope, FaPhone } from 'react-icons/fa';
import EditBasicInfo from '@/components/editor/EditBasicInfo';

interface ViewBasicInfoProps {
  studentId: string | number;
}

const ViewBasicInfo: React.FC<ViewBasicInfoProps> = ({ studentId }) => {
  const [student, setStudent] = useState<Student | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // دالة جلب بيانات الطالب
  const fetchStudent = async () => {
    try {
      setIsLoading(true);
      setError(null);

      if (!studentId) {
        throw new Error('معرف الطالب غير موجود');
      }

      const { data, error } = await supabase
        .from('students')
        .select('*')
        .eq('id', studentId)
        .single();

      if (error) {
        throw new Error(error.message);
      }

      if (!data) {
        throw new Error('الطالب غير موجود');
      }

      setStudent(data);
    } catch (err: any) {
      console.error('Error fetching student:', err);
      setError(err.message || 'حدث خطأ أثناء جلب بيانات الطالب');
    } finally {
      setIsLoading(false);
    }
  };

  // جلب البيانات عند تحميل المكون
  useEffect(() => {
    fetchStudent();
  }, [studentId]);

  // تنسيق التاريخ
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-EG');
  };

  // ترجمة الجنس
  const translateGender = (gender: string) => {
    return gender === 'male' ? 'ذكر' :
           gender === 'female' ? 'أنثى' :
           gender;
  };

  // ترجمة الديانة
  const translateReligion = (religion: string | null) => {
    if (!religion) return 'غير محدد';
    return religion === 'islam' ? 'الإسلام' :
           religion === 'christianity' ? 'المسيحية' :
           religion;
  };

  // ترجمة الحالة الاجتماعية
  const translateMaritalStatus = (status: string | null) => {
    if (!status) return 'غير محدد';
    return status === 'single' ? 'أعزب' :
           status === 'married' ? 'متزوج' :
           status === 'divorced' ? 'مطلق' :
           status === 'widowed' ? 'أرمل' :
           status;
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center p-8">
        <div className="relative">
          <div className="w-12 h-12 border-4 border-[#21ADE7]/30 border-t-[#21ADE7] rounded-full animate-spin"></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
            <FaUser size={16} className="text-[#21ADE7]" />
          </div>
        </div>
        <p className="mr-3 text-gray-600">جاري تحميل البيانات...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-4 bg-[#FD1361]/10 border-r-4 border-[#FD1361] rounded-md">
        <p className="text-[#FD1361]">{error}</p>
      </div>
    );
  }

  if (!student) {
    return (
      <div className="p-4 bg-gray-100 border-r-4 border-gray-400 rounded-md">
        <p className="text-gray-600">لا توجد بيانات للعرض</p>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg p-4">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-bold text-[#21ADE7]">البيانات الأساسية</h3>
        <EditBasicInfo student={student} onUpdate={() => fetchStudent()} />
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="flex items-start">
          <div className="w-10 h-10 rounded-full bg-[#21ADE7]/10 flex items-center justify-center text-[#21ADE7] ml-4">
            <FaUser size={18} />
          </div>
          <div>
            <h3 className="text-sm text-gray-500">الاسم الكامل</h3>
            <p className="font-medium">{student.full_name}</p>
          </div>
        </div>

        <div className="flex items-start">
          <div className="w-10 h-10 rounded-full bg-[#5578EB]/10 flex items-center justify-center text-[#5578EB] ml-4">
            <FaIdCard size={18} />
          </div>
          <div>
            <h3 className="text-sm text-gray-500">رقم الهوية</h3>
            <p className="font-medium">{student.id_number}</p>
          </div>
        </div>

        <div className="flex items-start">
          <div className="w-10 h-10 rounded-full bg-[#0ABB87]/10 flex items-center justify-center text-[#0ABB87] ml-4">
            <FaCalendarAlt size={18} />
          </div>
          <div>
            <h3 className="text-sm text-gray-500">تاريخ الميلاد</h3>
            <p className="font-medium">{formatDate(student.birth_date)}</p>
          </div>
        </div>

        <div className="flex items-start">
          <div className="w-10 h-10 rounded-full bg-[#FD1361]/10 flex items-center justify-center text-[#FD1361] ml-4">
            <FaVenusMars size={18} />
          </div>
          <div>
            <h3 className="text-sm text-gray-500">الجنس</h3>
            <p className="font-medium">{translateGender(student.gender)}</p>
          </div>
        </div>

        <div className="flex items-start">
          <div className="w-10 h-10 rounded-full bg-[#384AD7]/10 flex items-center justify-center text-[#384AD7] ml-4">
            <FaPray size={18} />
          </div>
          <div>
            <h3 className="text-sm text-gray-500">الديانة</h3>
            <p className="font-medium">{translateReligion(student.religion)}</p>
          </div>
        </div>

        <div className="flex items-start">
          <div className="w-10 h-10 rounded-full bg-[#21ADE7]/10 flex items-center justify-center text-[#21ADE7] ml-4">
            <FaRing size={18} />
          </div>
          <div>
            <h3 className="text-sm text-gray-500">الحالة الاجتماعية</h3>
            <p className="font-medium">{translateMaritalStatus(student.marital_status)}</p>
          </div>
        </div>

        <div className="flex items-start">
          <div className="w-10 h-10 rounded-full bg-[#5578EB]/10 flex items-center justify-center text-[#5578EB] ml-4">
            <FaEnvelope size={18} />
          </div>
          <div>
            <h3 className="text-sm text-gray-500">البريد الإلكتروني</h3>
            <p className="font-medium">{student.email || 'غير متوفر'}</p>
          </div>
        </div>

        <div className="flex items-start">
          <div className="w-10 h-10 rounded-full bg-[#0ABB87]/10 flex items-center justify-center text-[#0ABB87] ml-4">
            <FaPhone size={18} />
          </div>
          <div>
            <h3 className="text-sm text-gray-500">رقم الهاتف</h3>
            <p className="font-medium">{student.phone || 'غير متوفر'}</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ViewBasicInfo;
