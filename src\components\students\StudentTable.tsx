import React, { useMemo } from 'react';
import { ColumnDef } from '@tanstack/react-table';
import { DataTable } from '../table/DataTable';
import { ActionButtons } from '../table/ActionButtons';
import { Student } from '@/types/table.types';

interface StudentTableProps {
  data: Student[];
  isLoading?: boolean;
  onView?: (student: Student) => void;
  onEdit?: (student: Student) => void;
  onDelete?: (student: Student) => void;
}

export const StudentTable: React.FC<StudentTableProps> = ({
  data,
  isLoading = false,
  onView,
  onEdit,
  onDelete,
}) => {
  // تعريف أعمدة الجدول - استخدام مصفوفة تبعيات ثابتة
  const columns = useMemo<ColumnDef<Student, any>[]>(
    () => [
      {
        id: 'full_name',
        header: 'اسم الطالب',
        accessorKey: 'full_name',
        cell: ({ row }) => (
          <div className="font-medium text-gray-900">{row.original.full_name}</div>
        ),
        meta: {
          color: '#21ADE7', // أزرق فاتح
        },
      },
      {
        id: 'id_number',
        header: 'رقم الهوية',
        accessorKey: 'id_number',
        meta: {
          color: '#5578EB', // أزرق داكن
        },
      },
      {
        id: 'gender',
        header: 'الجنس',
        accessorKey: 'gender',
        cell: ({ row }) => (
          <div>
            {row.original.gender === 'male' ? 'ذكر' :
             row.original.gender === 'female' ? 'أنثى' :
             row.original.gender}
          </div>
        ),
        meta: {
          color: '#0ABB87', // أخضر
        },
      },
      {
        id: 'birth_date',
        header: 'تاريخ الميلاد',
        accessorKey: 'birth_date',
        cell: ({ row }) => {
          // تنسيق التاريخ
          const date = new Date(row.original.birth_date);
          return <div>{date.toLocaleDateString('ar-EG')}</div>;
        },
        meta: {
          color: '#384AD7', // أزرق غامق
        },
      },
      {
        id: 'phone',
        header: 'رقم الهاتف',
        accessorKey: 'phone',
        meta: {
          color: '#FD1361', // أحمر
        },
      },
      {
        id: 'actions',
        header: 'الإجراءات',
        cell: ({ row }) => (
          <ActionButtons
            onView={onView ? () => onView(row.original) : undefined}
            onEdit={onEdit ? () => onEdit(row.original) : undefined}
            onDelete={onDelete ? () => onDelete(row.original) : undefined}
            itemName={`الطالب "${row.original.full_name}"`}
          />
        ),
        meta: {
          color: '#21ADE7', // أزرق فاتح
        },
      },
    ],
    // استخدام مصفوفة تبعيات ثابتة لتجنب مشاكل إعادة التصيير
    // eslint-disable-next-line react-hooks/exhaustive-deps
    []
  );

  return (
    <DataTable
      columns={columns}
      data={data}
      isLoading={isLoading}
      showSearch={true}
      searchPlaceholder="بحث..."
      emptyMessage="لا يوجد طلاب"
      className="border-r-4 border-[#21ADE7]"
    />
  );
};

export default StudentTable;
