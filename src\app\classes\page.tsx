"use client";

import { useState, useEffect } from 'react';
import Layout from "@/components/Layout";
import { ClassTable } from '@/components/students';
import { Class } from '@/types/table.types';
import { supabase } from '@/lib/supabase';
import { FaPlus, FaChalkboardTeacher } from 'react-icons/fa';

export default function ClassList() {
  const [classes, setClasses] = useState<Class[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // جلب بيانات الفصول
  useEffect(() => {
    async function fetchClasses() {
      try {
        setIsLoading(true);
        setError(null);

        // جلب بيانات الفصول مع اسم المرحلة الدراسية
        const { data, error } = await supabase
          .from('school_classes')
          .select(`
            *,
            school_grades (
              name
            )
          `)
          .order('grade_id', { ascending: true })
          .order('name', { ascending: true });

        if (error) {
          throw new Error(error.message);
        }

        // تحويل البيانات إلى الشكل المطلوب
        const formattedData = data?.map(item => ({
          ...item,
          grade_name: item.school_grades?.name || 'غير محدد'
        })) || [];

        setClasses(formattedData);
      } catch (err: any) {
        console.error('Error fetching classes:', err);
        setError(err.message || 'حدث خطأ أثناء جلب بيانات الفصول');
      } finally {
        setIsLoading(false);
      }
    }

    fetchClasses();
  }, []);

  // عرض تفاصيل الفصل
  const handleViewClass = (classItem: Class) => {
    // عرض تفاصيل الفصل في نافذة منبثقة
    alert(`تفاصيل الفصل: ${classItem.name}\nالمرحلة: ${classItem.grade_name}\nالسعة: ${classItem.capacity}`);
  };

  // تعديل بيانات الفصل
  const handleEditClass = (classItem: Class) => {
    // تنفيذ عملية التعديل (سيتم تطويرها لاحقًا)
    alert(`سيتم تطوير صفحة تعديل الفصل ${classItem.name} قريبًا`);
  };

  // حذف الفصل
  const handleDeleteClass = async (classItem: Class) => {
    try {
      setIsLoading(true);

      // حذف الفصل من قاعدة البيانات
      const { error } = await supabase
        .from('school_classes')
        .delete()
        .eq('id', classItem.id);

      if (error) {
        throw new Error(error.message);
      }

      // تحديث قائمة الفصول بعد الحذف
      setClasses(classes.filter(c => c.id !== classItem.id));

      // عرض رسالة نجاح
      setError(null);

      // إعادة تحميل البيانات
      fetchClasses();
    } catch (err: any) {
      console.error('Error deleting class:', err);
      setError(err.message || 'حدث خطأ أثناء حذف الفصل');
    } finally {
      setIsLoading(false);
    }
  };

  // وظيفة لجلب بيانات الفصول
  const fetchClasses = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // جلب بيانات الفصول مع اسم المرحلة الدراسية
      const { data, error } = await supabase
        .from('school_classes')
        .select(`
          *,
          school_grades (
            name
          )
        `)
        .order('grade_id', { ascending: true })
        .order('name', { ascending: true });

      if (error) {
        throw new Error(error.message);
      }

      // تحويل البيانات إلى الشكل المطلوب
      const formattedData = data?.map(item => ({
        ...item,
        grade_name: item.school_grades?.name || 'غير محدد'
      })) || [];

      setClasses(formattedData);
    } catch (err: any) {
      console.error('Error fetching classes:', err);
      setError(err.message || 'حدث خطأ أثناء جلب بيانات الفصول');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Layout>
      <div className="p-6">
        <div className="mb-6 bg-gradient-to-l from-[#5578EB]/10 to-transparent p-4 rounded-lg border-r-4 border-[#5578EB]">
          <div className="flex justify-between items-center">
            <div className="flex items-center">
              <div className="w-12 h-12 rounded-full bg-[#5578EB] flex items-center justify-center text-white ml-4">
                <FaChalkboardTeacher size={24} />
              </div>
              <div>
                <h1 className="text-2xl font-bold" style={{ color: '#5578EB' }}>الفصول الدراسية</h1>
                <p className="text-gray-600 mt-1">
                  عرض وإدارة الفصول الدراسية في المدرسة
                </p>
              </div>
            </div>
            <button
              className="bg-[#5578EB] hover:bg-[#5578EB]/90 text-white px-4 py-2 rounded-md flex items-center transition-colors duration-200 clickable"
            >
              <FaPlus className="ml-2" />
              إضافة فصل
            </button>
          </div>
        </div>

        {error && (
          <div className="mb-6 p-4 bg-[#FD1361]/10 border-r-4 border-[#FD1361] rounded-md">
            <p className="text-[#FD1361]">{error}</p>
          </div>
        )}

        <ClassTable
          data={classes}
          isLoading={isLoading}
          onView={handleViewClass}
          onEdit={handleEditClass}
          onDelete={handleDeleteClass}
        />
      </div>
    </Layout>
  );
}
