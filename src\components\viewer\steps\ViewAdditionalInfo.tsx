"use client";

import React, { useState, useEffect } from 'react';
import { supabase } from '@/lib/supabase';
import { AdditionalInfo } from '@/types/table.types';
import { FaInfoCircle, FaGamepad, FaTrophy, FaAccessibleIcon, FaBus, FaStickyNote } from 'react-icons/fa';

interface ViewAdditionalInfoProps {
  studentId: string | number;
}

const ViewAdditionalInfo: React.FC<ViewAdditionalInfoProps> = ({ studentId }) => {
  const [additionalInfo, setAdditionalInfo] = useState<AdditionalInfo | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchAdditionalInfo() {
      try {
        setIsLoading(true);
        setError(null);
        
        if (!studentId) {
          throw new Error('معرف الطالب غير موجود');
        }
        
        const { data, error } = await supabase
          .from('additional_info')
          .select('*')
          .eq('student_id', studentId)
          .single();
        
        if (error && error.code !== 'PGRST116') {
          throw new Error(error.message);
        }
        
        setAdditionalInfo(data);
      } catch (err: any) {
        console.error('Error fetching additional info:', err);
        setError(err.message || 'حدث خطأ أثناء جلب المعلومات الإضافية');
      } finally {
        setIsLoading(false);
      }
    }
    
    fetchAdditionalInfo();
  }, [studentId]);

  // ترجمة وسيلة النقل
  const translateTransportation = (method: string | null) => {
    if (!method) return 'غير محدد';
    
    switch (method) {
      case 'school_bus': return 'حافلة المدرسة';
      case 'private_car': return 'سيارة خاصة';
      case 'public_transport': return 'وسائل النقل العامة';
      case 'walking': return 'المشي';
      default: return method;
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center p-8">
        <div className="relative">
          <div className="w-12 h-12 border-4 border-[#5578EB]/30 border-t-[#5578EB] rounded-full animate-spin"></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
            <FaInfoCircle size={16} className="text-[#5578EB]" />
          </div>
        </div>
        <p className="mr-3 text-gray-600">جاري تحميل البيانات...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-4 bg-[#FD1361]/10 border-r-4 border-[#FD1361] rounded-md">
        <p className="text-[#FD1361]">{error}</p>
      </div>
    );
  }

  if (!additionalInfo) {
    return (
      <div className="p-4 bg-gray-100 border-r-4 border-gray-400 rounded-md">
        <p className="text-gray-600">لا توجد معلومات إضافية للطالب</p>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg p-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {additionalInfo.hobbies && (
          <div className="flex items-start">
            <div className="w-10 h-10 rounded-full bg-[#5578EB]/10 flex items-center justify-center text-[#5578EB] ml-4">
              <FaGamepad size={18} />
            </div>
            <div>
              <h3 className="text-sm text-gray-500">الهوايات</h3>
              <p className="font-medium">{additionalInfo.hobbies}</p>
            </div>
          </div>
        )}
        
        {additionalInfo.achievements && (
          <div className="flex items-start">
            <div className="w-10 h-10 rounded-full bg-[#0ABB87]/10 flex items-center justify-center text-[#0ABB87] ml-4">
              <FaTrophy size={18} />
            </div>
            <div>
              <h3 className="text-sm text-gray-500">الإنجازات</h3>
              <p className="font-medium">{additionalInfo.achievements}</p>
            </div>
          </div>
        )}
        
        {additionalInfo.special_needs && (
          <div className="flex items-start">
            <div className="w-10 h-10 rounded-full bg-[#FD1361]/10 flex items-center justify-center text-[#FD1361] ml-4">
              <FaAccessibleIcon size={18} />
            </div>
            <div>
              <h3 className="text-sm text-gray-500">الاحتياجات الخاصة</h3>
              <p className="font-medium">{additionalInfo.special_needs}</p>
            </div>
          </div>
        )}
        
        {additionalInfo.transportation_method && (
          <div className="flex items-start">
            <div className="w-10 h-10 rounded-full bg-[#21ADE7]/10 flex items-center justify-center text-[#21ADE7] ml-4">
              <FaBus size={18} />
            </div>
            <div>
              <h3 className="text-sm text-gray-500">وسيلة النقل</h3>
              <p className="font-medium">{translateTransportation(additionalInfo.transportation_method)}</p>
            </div>
          </div>
        )}
        
        {additionalInfo.additional_notes && (
          <div className="flex items-start col-span-1 md:col-span-2">
            <div className="w-10 h-10 rounded-full bg-[#384AD7]/10 flex items-center justify-center text-[#384AD7] ml-4">
              <FaStickyNote size={18} />
            </div>
            <div>
              <h3 className="text-sm text-gray-500">ملاحظات إضافية</h3>
              <p className="font-medium">{additionalInfo.additional_notes}</p>
            </div>
          </div>
        )}
      </div>
      
      {!additionalInfo.hobbies && 
       !additionalInfo.achievements && 
       !additionalInfo.special_needs && 
       !additionalInfo.transportation_method && 
       !additionalInfo.additional_notes && (
        <div className="p-4 bg-gray-100 border-r-4 border-gray-400 rounded-md">
          <p className="text-gray-600">لا توجد معلومات إضافية مفصلة للطالب</p>
        </div>
      )}
    </div>
  );
};

export default ViewAdditionalInfo;
