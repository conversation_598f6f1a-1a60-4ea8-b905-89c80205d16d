import { NextResponse } from 'next/server';
import { addCompleteStudent, checkStudentExists, updateStudent } from '@/lib/studentService';

export async function POST(request: Request) {
  try {
    // استخراج البيانات من الطلب
    const data = await request.json();
    
    // التحقق من وجود البيانات المطلوبة
    if (!data || !data.students || !Array.isArray(data.students)) {
      return NextResponse.json(
        { error: 'البيانات المرسلة غير صالحة. يجب إرسال مصفوفة من بيانات الطلاب.' },
        { status: 400 }
      );
    }

    // تحويل البيانات المستوردة إلى تنسيق قاعدة البيانات
    const students = data.students.map((student: any) => ({
      student: {
        full_name: student.fullName,
        id_number: student.idNumber,
        birth_date: student.birthDate,
        gender: student.gender,
        religion: student.religion || null,
        marital_status: student.maritalStatus || null,
        email: student.email || null,
        phone: student.phone || null,
      },
      // يمكن إضافة المزيد من البيانات المرتبطة هنا حسب الحاجة
    }));

    // نتائج العملية
    const results = {
      success: 0,
      failed: 0,
      updated: 0,
      errors: [] as string[],
      successIds: [] as number[],
    };

    // معالجة كل طالب
    for (const studentData of students) {
      try {
        // التحقق من وجود الطالب
        const existingStudent = await checkStudentExists(studentData.student.id_number);
        
        if (existingStudent) {
          // تحديث بيانات الطالب الموجود
          await updateStudent(existingStudent.id, studentData.student);
          results.updated++;
          results.successIds.push(existingStudent.id);
        } else {
          // إضافة طالب جديد
          const studentId = await addCompleteStudent(studentData);
          results.success++;
          results.successIds.push(studentId);
        }
      } catch (error) {
        // تسجيل الخطأ واستمرار المعالجة
        results.failed++;
        results.errors.push(`خطأ في معالجة الطالب ${studentData.student.full_name}: ${error instanceof Error ? error.message : 'خطأ غير معروف'}`);
      }
    }

    // إرجاع نتائج العملية
    return NextResponse.json({
      message: 'تمت معالجة البيانات',
      results: {
        totalProcessed: students.length,
        newStudents: results.success,
        updatedStudents: results.updated,
        failedStudents: results.failed,
        successIds: results.successIds,
        errors: results.errors,
      }
    });
  } catch (error) {
    console.error('Error importing students:', error);
    return NextResponse.json(
      { error: `حدث خطأ أثناء استيراد بيانات الطلاب: ${error instanceof Error ? error.message : 'خطأ غير معروف'}` },
      { status: 500 }
    );
  }
}
