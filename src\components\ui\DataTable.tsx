"use client";

import React, { useState } from 'react';
import { Icon, IconType, COLORS } from './IconProvider';
import Button from './Button';

// تعريف خصائص العمود
export interface Column<T> {
  key: string;
  header: string;
  render?: (item: T) => React.ReactNode;
  sortable?: boolean;
  width?: string;
}

// تعريف خصائص الإجراء
export interface Action<T> {
  icon: IconType;
  label: string;
  color?: keyof typeof COLORS | string;
  onClick: (item: T) => void;
}

// تعريف خصائص جدول البيانات
export interface DataTableProps<T> {
  data: T[];
  columns: Column<T>[];
  actions?: Action<T>[];
  keyExtractor: (item: T) => string | number;
  pagination?: boolean;
  itemsPerPage?: number;
  searchable?: boolean;
  searchKeys?: string[];
  bordered?: boolean;
  striped?: boolean;
  hoverable?: boolean;
  className?: string;
  emptyMessage?: string;
}

// مكون جدول البيانات
export function DataTable<T>({
  data,
  columns,
  actions,
  keyExtractor,
  pagination = true,
  itemsPerPage = 10,
  searchable = true,
  searchKeys = [],
  bordered = true,
  striped = true,
  hoverable = true,
  className = '',
  emptyMessage = 'لا توجد بيانات',
}: DataTableProps<T>) {
  const [currentPage, setCurrentPage] = useState(1);
  const [sortKey, setSortKey] = useState<string | null>(null);
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');
  const [searchTerm, setSearchTerm] = useState('');

  // البحث في البيانات
  const filteredData = searchTerm && searchKeys.length > 0
    ? data.filter(item => {
        return searchKeys.some(key => {
          const value = getNestedValue(item, key);
          return value && value.toString().toLowerCase().includes(searchTerm.toLowerCase());
        });
      })
    : data;

  // فرز البيانات
  const sortedData = sortKey
    ? [...filteredData].sort((a, b) => {
        const aValue = getNestedValue(a, sortKey);
        const bValue = getNestedValue(b, sortKey);

        if (aValue === bValue) return 0;
        
        const result = aValue < bValue ? -1 : 1;
        return sortDirection === 'asc' ? result : -result;
      })
    : filteredData;

  // تقسيم البيانات إلى صفحات
  const totalPages = Math.ceil(sortedData.length / itemsPerPage);
  const paginatedData = pagination
    ? sortedData.slice((currentPage - 1) * itemsPerPage, currentPage * itemsPerPage)
    : sortedData;

  // الحصول على قيمة متداخلة من كائن
  function getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((prev, curr) => {
      return prev ? prev[curr] : null;
    }, obj);
  }

  // تبديل اتجاه الفرز
  const toggleSort = (key: string) => {
    if (sortKey === key) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortKey(key);
      setSortDirection('asc');
    }
  };

  // تغيير الصفحة
  const changePage = (page: number) => {
    setCurrentPage(page);
  };

  // تحديد فئات الجدول
  const tableClasses = `
    min-w-full
    ${bordered ? 'border border-gray-200' : ''}
    ${className}
  `;

  // تحديد فئات الصف
  const rowClasses = `
    ${striped ? 'even:bg-gray-50' : ''}
    ${hoverable ? 'hover:bg-gray-100' : ''}
  `;

  return (
    <div className="overflow-hidden">
      {searchable && searchKeys.length > 0 && (
        <div className="mb-4 flex">
          <div className="relative flex-1">
            <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
              <Icon name="FaSearch" color="GRAY" size="SM" />
            </div>
            <input
              type="text"
              className="block w-full p-2 pr-10 text-sm text-gray-900 border border-gray-300 rounded-lg bg-white"
              placeholder="بحث..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
        </div>
      )}

      <div className="overflow-x-auto">
        <table className={tableClasses}>
          <thead className="bg-gray-50 text-gray-700">
            <tr>
              {columns.map((column) => (
                <th
                  key={column.key}
                  className={`py-3 px-4 text-right text-sm font-semibold ${column.width || ''}`}
                  onClick={() => column.sortable && toggleSort(column.key)}
                >
                  <div className="flex items-center cursor-pointer">
                    {column.header}
                    {column.sortable && sortKey === column.key && (
                      <span className="mr-1">
                        <Icon
                          name={sortDirection === 'asc' ? 'FaSortUp' : 'FaSortDown'}
                          color="GRAY"
                          size="SM"
                        />
                      </span>
                    )}
                  </div>
                </th>
              ))}
              {actions && actions.length > 0 && (
                <th className="py-3 px-4 text-right text-sm font-semibold">الإجراءات</th>
              )}
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {paginatedData.length > 0 ? (
              paginatedData.map((item) => (
                <tr key={keyExtractor(item)} className={rowClasses}>
                  {columns.map((column) => (
                    <td key={column.key} className="py-3 px-4 text-sm text-gray-900">
                      {column.render
                        ? column.render(item)
                        : getNestedValue(item, column.key)}
                    </td>
                  ))}
                  {actions && actions.length > 0 && (
                    <td className="py-3 px-4 text-sm text-gray-900">
                      <div className="flex space-x-2 space-x-reverse">
                        {actions.map((action, index) => (
                          <button
                            key={index}
                            onClick={() => action.onClick(item)}
                            className="p-1 rounded-full hover:bg-gray-100"
                            title={action.label}
                          >
                            <Icon
                              name={action.icon}
                              color={action.color || 'PRIMARY'}
                              size="SM"
                            />
                          </button>
                        ))}
                      </div>
                    </td>
                  )}
                </tr>
              ))
            ) : (
              <tr>
                <td
                  colSpan={columns.length + (actions ? 1 : 0)}
                  className="py-4 px-4 text-center text-gray-500"
                >
                  {emptyMessage}
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      {pagination && totalPages > 1 && (
        <div className="flex justify-between items-center mt-4">
          <div className="text-sm text-gray-700">
            عرض {(currentPage - 1) * itemsPerPage + 1} إلى{' '}
            {Math.min(currentPage * itemsPerPage, sortedData.length)} من{' '}
            {sortedData.length} عنصر
          </div>
          <div className="flex space-x-1 space-x-reverse">
            <Button
              variant="outline-primary"
              size="sm"
              icon="FaAngleRight"
              onClick={() => changePage(Math.max(1, currentPage - 1))}
              disabled={currentPage === 1}
            >
              السابق
            </Button>
            {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
              <Button
                key={page}
                variant={page === currentPage ? 'primary' : 'outline-primary'}
                size="sm"
                onClick={() => changePage(page)}
              >
                {page}
              </Button>
            ))}
            <Button
              variant="outline-primary"
              size="sm"
              icon="FaAngleLeft"
              iconPosition="right"
              onClick={() => changePage(Math.min(totalPages, currentPage + 1))}
              disabled={currentPage === totalPages}
            >
              التالي
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}

export default DataTable;
