import { NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const gradeId = searchParams.get('gradeId');
    const academicYear = searchParams.get('academicYear') || '2024-2025';

    if (!gradeId) {
      return NextResponse.json(
        { error: 'معرف المرحلة مطلوب' },
        { status: 400 }
      );
    }

    // الحصول على رسوم المرحلة للسنة الدراسية المحددة
    const { data: gradeFee, error } = await supabase
      .from('grade_fees')
      .select(`
        *,
        school_grades!grade_id (
          id,
          name,
          level
        )
      `)
      .eq('grade_id', gradeId)
      .eq('academic_year', academicYear)
      .eq('is_active', true)
      .single();

    if (error) {
      console.error('Error fetching grade fee:', error);
      
      // إذا لم توجد رسوم للمرحلة، إرجاع رسوم افتراضية
      if (error.code === 'PGRST116') {
        return NextResponse.json({
          tuition_fee: 5000, // رسوم افتراضية
          grade_id: parseInt(gradeId),
          academic_year: academicYear,
          message: 'لم يتم تحديد رسوم لهذه المرحلة، سيتم استخدام الرسوم الافتراضية'
        });
      }
      
      return NextResponse.json(
        { error: 'حدث خطأ أثناء جلب رسوم المرحلة' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      tuition_fee: gradeFee.tuition_fee,
      grade_id: gradeFee.grade_id,
      academic_year: gradeFee.academic_year,
      grade_name: gradeFee.school_grades?.name || 'غير محدد'
    });

  } catch (error: any) {
    console.error('Error in grade fees API:', error);
    return NextResponse.json(
      { error: 'حدث خطأ أثناء معالجة الطلب: ' + error.message },
      { status: 500 }
    );
  }
}
