# دليل استخدام مكون الجدول (DataTable)

هذا الدليل يشرح كيفية استخدام مكون الجدول المبني على مكتبة React Table v8 (TanStack Table) في مشروع نظام إدارة المدرسة.

## المميزات

مكون الجدول يوفر العديد من الميزات:

1. **ترقيم الصفوف تلقائيًا**: يضيف عمود ترقيم تلقائي للصفوف.
2. **دعم الفرز**: يمكن فرز البيانات بالنقر على رأس العمود.
3. **دعم البحث**: يمكن البحث في جميع الأعمدة باستخدام حقل البحث.
4. **دعم الترقيم**: يمكن التنقل بين الصفحات باستخدام أزرار الترقيم.
5. **دعم العرض المتجاوب**: يتكيف الجدول مع أحجام الشاشات المختلفة.
6. **تصميم حديث وجذاب**: يتميز الجدول بتصميم حديث وجذاب مع تأثيرات حركية وألوان متناسقة.
7. **أزرار وأيقونات تفاعلية**: جميع الأزرار والأيقونات حقيقية وذات تأثير حقيقي.
8. **تلوين الأعمدة**: يمكن تلوين كل عمود في الجدول وفقًا لاستخدامه.

## الخصائص

| الخاصية | النوع | الوصف | القيمة الافتراضية |
|---------|------|-------|-----------------|
| `columns` | `ColumnDef<TData, TValue>[]` | تعريف أعمدة الجدول | **مطلوب** |
| `data` | `TData[]` | البيانات التي سيتم عرضها في الجدول | **مطلوب** |
| `onRowClick` | `(row: TData) => void` | دالة تنفذ عند النقر على صف | `undefined` |
| `isLoading` | `boolean` | حالة تحميل الجدول | `false` |
| `showSearch` | `boolean` | عرض حقل البحث | `true` |
| `searchPlaceholder` | `string` | نص العنصر النائب لحقل البحث | `"بحث..."` |
| `emptyMessage` | `string` | رسالة تظهر عندما لا توجد بيانات | `"لا توجد بيانات"` |
| `className` | `string` | فئة CSS إضافية للجدول | `""` |
| `rowsPerPageOptions` | `number[]` | خيارات عدد الصفوف في الصفحة | `[10, 25, 50, 100]` |
| `defaultPageSize` | `number` | عدد الصفوف الافتراضي في الصفحة | `10` |

## كيفية الاستخدام

### 1. استيراد المكون

```tsx
import { DataTable } from '@/components/table';
```

### 2. تعريف الأعمدة

```tsx
import { ColumnDef } from '@tanstack/react-table';

const columns: ColumnDef<MyDataType, any>[] = [
  {
    id: 'name',
    header: 'الاسم',
    accessorKey: 'name',
    meta: {
      color: '#21ADE7', // لون العمود
    },
  },
  {
    id: 'email',
    header: 'البريد الإلكتروني',
    accessorKey: 'email',
    meta: {
      color: '#5578EB', // لون العمود
    },
  },
  // المزيد من الأعمدة...
];
```

### 3. استخدام المكون

```tsx
<DataTable
  columns={columns}
  data={myData}
  isLoading={isLoading}
  showSearch={true}
  searchPlaceholder="البحث..."
  emptyMessage="لا توجد بيانات"
  className="border-r-4 border-primary"
/>
```

## مكونات الجداول المتخصصة

تم إنشاء مكونات جداول متخصصة لأنواع البيانات المختلفة:

### 1. جدول الطلاب (StudentTable)

```tsx
import { StudentTable } from '@/components/students';

<StudentTable
  data={students}
  isLoading={isLoading}
  onView={handleViewStudent}
  onEdit={handleEditStudent}
  onDelete={handleDeleteStudent}
/>
```

### 2. جدول الفصول الدراسية (ClassTable)

```tsx
import { ClassTable } from '@/components/students';

<ClassTable
  data={classes}
  isLoading={isLoading}
  onView={handleViewClass}
  onEdit={handleEditClass}
  onDelete={handleDeleteClass}
/>
```

### 3. جدول المراحل الدراسية (GradeTable)

```tsx
import { GradeTable } from '@/components/students';

<GradeTable
  data={grades}
  isLoading={isLoading}
  onView={handleViewGrade}
  onEdit={handleEditGrade}
  onDelete={handleDeleteGrade}
/>
```

## مكون أزرار الإجراءات (ActionButtons)

يمكن استخدام مكون أزرار الإجراءات لإضافة أزرار العرض والتعديل والحذف:

```tsx
import { ActionButtons } from '@/components/table';

<ActionButtons
  onView={() => handleView(item)}
  onEdit={() => handleEdit(item)}
  onDelete={() => handleDelete(item)}
  viewTooltip="عرض التفاصيل"
  editTooltip="تعديل البيانات"
  deleteTooltip="حذف العنصر"
  hideView={false}
  hideEdit={false}
  hideDelete={false}
/>
```

## تخصيص الألوان

يمكن تخصيص ألوان الأعمدة باستخدام خاصية `meta.color` في تعريف العمود:

```tsx
{
  id: 'name',
  header: 'الاسم',
  accessorKey: 'name',
  meta: {
    color: '#21ADE7', // أزرق فاتح
  },
}
```

الألوان المستخدمة في المشروع:

- أزرق فاتح (Primary): `#21ADE7`
- أزرق داكن (Secondary): `#5578EB`
- أخضر (Success): `#0ABB87`
- أحمر (Danger): `#FD1361`
- أزرق غامق (Info): `#384AD7`

## إضافة أعمدة مخصصة

يمكن إضافة أعمدة مخصصة باستخدام خاصية `cell`:

```tsx
{
  id: 'status',
  header: 'الحالة',
  accessorKey: 'status',
  cell: ({ row }) => (
    <div className={`px-2 py-1 rounded-full text-white text-xs text-center ${
      row.original.status === 'active' ? 'bg-success' : 'bg-danger'
    }`}>
      {row.original.status === 'active' ? 'نشط' : 'غير نشط'}
    </div>
  ),
  meta: {
    color: '#0ABB87', // أخضر
  },
}
```

## التعامل مع الأحداث

يمكن التعامل مع أحداث النقر على الصفوف والأزرار:

```tsx
const handleRowClick = (row: MyDataType) => {
  console.log('Row clicked:', row);
};

<DataTable
  columns={columns}
  data={myData}
  onRowClick={handleRowClick}
/>
```
