# دليل الاتصال بقاعدة بيانات Supabase

## معلومات الاتصال

يستخدم النظام قاعدة بيانات Supabase للتخزين. تم تكوين الاتصال بالفعل في المشروع باستخدام المعلومات التالية:

```
URL: https://bbigwqwtmhctqrptkuni.supabase.co
API Key: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJiaWd3cXd0bWhjdHFycHRrdW5pIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYyOTg3MjYsImV4cCI6MjA2MTg3NDcyNn0.k6_MpI7ohitVroAuc2e5nYpAdFRphn7185_u_sm_ZVM
```

## كيفية تنفيذ سكريبت إنشاء الجداول

لقد قمنا بإنشاء ملف `supabase-schema.sql` يحتوي على تعليمات SQL لإنشاء جداول المراحل الدراسية والفصول. لتنفيذ هذا السكريبت، يمكنك اتباع الخطوات التالية:

### الطريقة 1: استخدام واجهة Supabase الرسومية

1. قم بتسجيل الدخول إلى [لوحة تحكم Supabase](https://app.supabase.io)
2. اختر المشروع الخاص بك
3. انتقل إلى قسم "SQL Editor"
4. انسخ محتوى ملف `supabase-schema.sql` والصقه في محرر SQL
5. انقر على زر "Run" لتنفيذ الاستعلام

### الطريقة 2: استخدام Supabase CLI

إذا كنت تفضل استخدام سطر الأوامر:

1. تأكد من تثبيت Supabase CLI:
   ```
   npm install -g supabase
   ```

2. قم بتسجيل الدخول إلى Supabase:
   ```
   supabase login
   ```

3. قم بتنفيذ السكريبت:
   ```
   supabase db execute --file=supabase-schema.sql
   ```

## هيكل الجداول

### جدول المراحل الدراسية (grades)

```sql
CREATE TABLE IF NOT EXISTS grades (
  id SERIAL PRIMARY KEY,
  name TEXT NOT NULL,
  level TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

### جدول الفصول الدراسية (classes)

```sql
CREATE TABLE IF NOT EXISTS classes (
  id SERIAL PRIMARY KEY,
  name TEXT NOT NULL,
  grade_id INTEGER REFERENCES grades(id) ON DELETE CASCADE,
  capacity INTEGER DEFAULT 30,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

## استخدام Supabase في التطبيق

يتم استخدام Supabase في التطبيق من خلال مكتبة `@supabase/supabase-js`. تم تكوين الاتصال في الملف `src/lib/supabase.ts`:

```typescript
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://bbigwqwtmhctqrptkuni.supabase.co';
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJiaWd3cXd0bWhjdHFycHRrdW5pIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYyOTg3MjYsImV4cCI6MjA2MTg3NDcyNn0.k6_MpI7ohitVroAuc2e5nYpAdFRphn7185_u_sm_ZVM';

export const supabase = createClient(supabaseUrl, supabaseAnonKey);
```

يمكنك استخدام كائن `supabase` للتفاعل مع قاعدة البيانات في أي مكان في التطبيق:

```typescript
// مثال لاسترجاع المراحل الدراسية
const { data, error } = await supabase
  .from('grades')
  .select('id, name, level')
  .order('id');

// مثال لاسترجاع الفصول الدراسية لمرحلة معينة
const { data, error } = await supabase
  .from('classes')
  .select('id, name, grade_id, capacity')
  .eq('grade_id', gradeId)
  .order('name');
```

## استكشاف الأخطاء وإصلاحها

إذا واجهت مشاكل في الاتصال بقاعدة البيانات:

1. تأكد من صحة معلومات الاتصال في ملف `.env.local`
2. تحقق من وجود الجداول في قاعدة البيانات من خلال لوحة تحكم Supabase
3. تأكد من أن لديك الصلاحيات المناسبة للوصول إلى الجداول
4. راجع سجلات الخطأ في وحدة تحكم المتصفح للحصول على مزيد من المعلومات

## ملاحظات إضافية

- تم تكوين النظام للبحث عن الجداول باسم `grades` و `classes` أو `school_grades` و `school_classes`
- إذا كنت ترغب في استخدام أسماء جداول مختلفة، فستحتاج إلى تعديل الكود في الملفات التالية:
  - `src/components/registration/steps/financial/GradeSelector.tsx`
  - `src/components/registration/steps/financial/ClassSelector.tsx`