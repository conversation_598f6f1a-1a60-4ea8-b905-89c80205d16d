"use client";

import React, { useState } from 'react';
import { Icon, IconType, COLORS } from './IconProvider';

// تعريف أنواع التنبيهات
export type AlertVariant = 'primary' | 'secondary' | 'success' | 'danger' | 'info' | 'warning' | 'light' | 'dark';

// تعريف خصائص التنبيه
export interface AlertProps {
  children: React.ReactNode;
  variant?: AlertVariant;
  title?: string;
  icon?: IconType;
  dismissible?: boolean;
  bordered?: boolean;
  rounded?: boolean;
  className?: string;
}

// مكون التنبيه
export const Alert: React.FC<AlertProps> = ({
  children,
  variant = 'primary',
  title,
  icon,
  dismissible = false,
  bordered = true,
  rounded = true,
  className = '',
}) => {
  const [visible, setVisible] = useState(true);

  if (!visible) {
    return null;
  }

  // تحديد الأيقونة الافتراضية بناءً على النوع
  const getDefaultIcon = (): IconType => {
    const defaultIcons: Record<AlertVariant, IconType> = {
      'primary': 'FaInfoCircle',
      'secondary': 'FaInfoCircle',
      'success': 'FaCheckCircle',
      'danger': 'FaExclamationTriangle',
      'info': 'FaInfoCircle',
      'warning': 'FaExclamationCircle',
      'light': 'FaInfoCircle',
      'dark': 'FaInfoCircle',
    };

    return defaultIcons[variant];
  };

  // تحديد الألوان بناءً على النوع
  const getVariantClasses = (): string => {
    const variantClasses: Record<AlertVariant, string> = {
      'primary': `bg-[${COLORS.PRIMARY}]/10 text-[${COLORS.PRIMARY}]`,
      'secondary': `bg-[${COLORS.SECONDARY}]/10 text-[${COLORS.SECONDARY}]`,
      'success': `bg-[${COLORS.SUCCESS}]/10 text-[${COLORS.SUCCESS}]`,
      'danger': `bg-[${COLORS.DANGER}]/10 text-[${COLORS.DANGER}]`,
      'info': `bg-[${COLORS.INFO}]/10 text-[${COLORS.INFO}]`,
      'warning': `bg-[${COLORS.WARNING}]/10 text-[${COLORS.WARNING}]`,
      'light': `bg-[${COLORS.LIGHT}] text-[${COLORS.DARK}]`,
      'dark': `bg-[${COLORS.DARK}]/10 text-[${COLORS.DARK}]`,
    };

    return variantClasses[variant];
  };

  // تحديد لون الحدود بناءً على النوع
  const getBorderClasses = (): string => {
    if (!bordered) {
      return '';
    }

    const borderClasses: Record<AlertVariant, string> = {
      'primary': `border border-[${COLORS.PRIMARY}]/30`,
      'secondary': `border border-[${COLORS.SECONDARY}]/30`,
      'success': `border border-[${COLORS.SUCCESS}]/30`,
      'danger': `border border-[${COLORS.DANGER}]/30`,
      'info': `border border-[${COLORS.INFO}]/30`,
      'warning': `border border-[${COLORS.WARNING}]/30`,
      'light': `border border-gray-200`,
      'dark': `border border-[${COLORS.DARK}]/30`,
    };

    return borderClasses[variant];
  };

  // تجميع الفئات
  const alertClasses = `
    p-4
    ${getVariantClasses()}
    ${getBorderClasses()}
    ${rounded ? 'rounded-lg' : ''}
    ${className}
  `;

  return (
    <div className={alertClasses} role="alert">
      <div className="flex">
        {(icon || getDefaultIcon()) && (
          <div className="flex-shrink-0 ml-2">
            <Icon
              name={icon || getDefaultIcon()}
              color={variant.toUpperCase()}
              size="MD"
            />
          </div>
        )}
        <div className="flex-1">
          {title && (
            <h4 className="text-lg font-semibold mb-1">{title}</h4>
          )}
          <div className="text-sm">{children}</div>
        </div>
        {dismissible && (
          <button
            type="button"
            className="flex-shrink-0 mr-2 focus:outline-none"
            onClick={() => setVisible(false)}
          >
            <Icon
              name="FaTimes"
              color={variant.toUpperCase()}
              size="SM"
            />
          </button>
        )}
      </div>
    </div>
  );
};

export default Alert;
