"use client";

import React, { useEffect, useState } from 'react';
import { format, addMonths } from 'date-fns';
import { ar } from 'date-fns/locale';

interface Installment {
  id: number;
  dueDate: string;
  amount: number;
  paid: number;
  discount: number;
  remaining: number;
  status: 'pending' | 'paid' | 'partial' | 'late';
}

interface Step4Props {
  onComplete: (data: any, isValid: boolean) => void;
  formData: Record<string, any>;
}

const Step4Installments: React.FC<Step4Props> = ({ onComplete, formData }) => {
  const [installments, setInstallments] = useState<Installment[]>([]);
  const [isValid, setIsValid] = useState(true);

  // إنشاء جدول الأقساط بناءً على البيانات المالية
  useEffect(() => {
    if (
      formData.paymentMethod === 'installments' &&
      formData.installmentsCount &&
      formData.totalAfterDiscount &&
      formData.paidAmount
    ) {
      const count = parseInt(formData.installmentsCount);
      const totalAfterDiscount = parseFloat(formData.totalAfterDiscount);
      const paidAmount = parseFloat(formData.paidAmount);
      const remainingAmount = totalAfterDiscount - paidAmount;
      const installmentAmount = remainingAmount / count;

      const today = new Date();
      const newInstallments: Installment[] = [];

      // إنشاء أقساط جديدة بناءً على العدد المحدد
      // مع الحفاظ على بيانات الأقساط الموجودة إذا كانت متاحة
      for (let i = 0; i < count; i++) {
        const dueDate = addMonths(today, i + 1);
        // الاحتفاظ بالقسط الموجود إذا كان متاحًا
        const existingInstallment = i < installments.length ? installments[i] : null;

        newInstallments.push({
          id: i + 1,
          dueDate: existingInstallment?.dueDate || format(dueDate, 'yyyy-MM-dd'),
          amount: installmentAmount,
          paid: existingInstallment?.paid || 0,
          discount: existingInstallment?.discount || 0,
          remaining: installmentAmount - (existingInstallment?.paid || 0) - (existingInstallment?.discount || 0),
          status: existingInstallment?.status || 'pending',
        });
      }

      console.log(`تم تحديث الأقساط: العدد الجديد ${newInstallments.length}`);
      setInstallments(newInstallments);
      setIsValid(true);
    } else {
      setInstallments([]);
      setIsValid(formData.paymentMethod !== 'installments');
    }
  }, [formData.paymentMethod, formData.installmentsCount, formData.totalAfterDiscount, formData.paidAmount]);

  // تحديث البيانات عند تغييرها
  useEffect(() => {
    onComplete({ installments }, isValid);
  }, [installments, isValid, onComplete]);

  // تحديث قيمة القسط
  const handleAmountChange = (id: number, value: string) => {
    const amount = parseFloat(value) || 0;
    setInstallments(
      installments.map((installment) => {
        if (installment.id === id) {
          return {
            ...installment,
            amount,
            remaining: amount - installment.paid - installment.discount,
          };
        }
        return installment;
      })
    );
  };

  // تحديث المدفوع
  const handlePaidChange = (id: number, value: string) => {
    const paid = parseFloat(value) || 0;
    setInstallments(
      installments.map((installment) => {
        if (installment.id === id) {
          const remaining = installment.amount - paid - installment.discount;
          const status = remaining <= 0 ? 'paid' : paid > 0 ? 'partial' : 'pending';
          return {
            ...installment,
            paid,
            remaining,
            status,
          };
        }
        return installment;
      })
    );
  };

  // تحديث الخصم
  const handleDiscountChange = (id: number, value: string) => {
    const discount = parseFloat(value) || 0;
    setInstallments(
      installments.map((installment) => {
        if (installment.id === id) {
          const remaining = installment.amount - installment.paid - discount;
          const status = remaining <= 0 ? 'paid' : installment.paid > 0 ? 'partial' : 'pending';
          return {
            ...installment,
            discount,
            remaining,
            status,
          };
        }
        return installment;
      })
    );
  };

  // تحديث تاريخ الاستحقاق
  const handleDueDateChange = (id: number, value: string) => {
    setInstallments(
      installments.map((installment) => {
        if (installment.id === id) {
          return {
            ...installment,
            dueDate: value,
          };
        }
        return installment;
      })
    );
  };

  // الحصول على لون الحالة
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'paid':
        return 'bg-success/10 text-success';
      case 'partial':
        return 'bg-info/10 text-info';
      case 'late':
        return 'bg-danger/10 text-danger';
      default:
        return 'bg-gray-100 text-gray-600';
    }
  };

  // الحصول على نص الحالة
  const getStatusText = (status: string) => {
    switch (status) {
      case 'paid':
        return 'مدفوع';
      case 'partial':
        return 'مدفوع جزئياً';
      case 'late':
        return 'متأخر';
      default:
        return 'قيد الانتظار';
    }
  };

  if (formData.paymentMethod !== 'installments') {
    return (
      <div className="bg-white rounded-lg p-6">
        <h2 className="text-xl font-bold text-primary mb-6 border-r-4 border-primary pr-3">
          جدول الأقساط
        </h2>
        <div className="p-8 text-center text-gray-500">
          <p className="text-lg">لا يوجد أقساط لعرضها. تم اختيار طريقة الدفع نقداً.</p>
          <p className="mt-2">يرجى العودة إلى الخطوة السابقة إذا كنت ترغب في تغيير طريقة الدفع.</p>
        </div>
      </div>
    );
  }

  if (installments.length === 0) {
    return (
      <div className="bg-white rounded-lg p-6">
        <h2 className="text-xl font-bold text-primary mb-6 border-r-4 border-primary pr-3">
          جدول الأقساط
        </h2>
        <div className="p-8 text-center text-gray-500">
          <p className="text-lg">يرجى إكمال البيانات المالية في الخطوة السابقة لعرض جدول الأقساط.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg p-6">
      <h2 className="text-xl font-bold text-primary mb-6 border-r-4 border-primary pr-3">
        جدول الأقساط
      </h2>

      <div className="overflow-x-auto">
        <table className="w-full border-collapse">
          <thead>
            <tr className="bg-primary text-black">
              <th className="p-3 text-right border border-gray-300">رقم القسط</th>
              <th className="p-3 text-right border border-gray-300">تاريخ الاستحقاق</th>
              <th className="p-3 text-right border border-gray-300">المبلغ المستحق</th>
              <th className="p-3 text-right border border-gray-300">المدفوع</th>
              <th className="p-3 text-right border border-gray-300">الخصم</th>
              <th className="p-3 text-right border border-gray-300">المتبقي</th>
              <th className="p-3 text-right border border-gray-300">الحالة</th>
            </tr>
          </thead>
          <tbody>
            {installments.map((installment, index) => (
              <tr key={installment.id} className={index % 2 === 0 ? 'bg-gray-50' : 'bg-white'}>
                <td className="p-3 text-right border border-gray-300">{installment.id}</td>
                <td className="p-3 text-right border border-gray-300">
                  <input
                    type="date"
                    value={installment.dueDate}
                    onChange={(e) => handleDueDateChange(installment.id, e.target.value)}
                    className="w-full p-1 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary"
                  />
                </td>
                <td className="p-3 text-right border border-gray-300">
                  <input
                    type="number"
                    value={installment.amount}
                    onChange={(e) => handleAmountChange(installment.id, e.target.value)}
                    className="w-full p-1 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary"
                  />
                </td>
                <td className="p-3 text-right border border-gray-300">
                  <input
                    type="number"
                    value={installment.paid}
                    onChange={(e) => handlePaidChange(installment.id, e.target.value)}
                    className="w-full p-1 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary"
                  />
                </td>
                <td className="p-3 text-right border border-gray-300">
                  <input
                    type="number"
                    value={installment.discount}
                    onChange={(e) => handleDiscountChange(installment.id, e.target.value)}
                    className="w-full p-1 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary"
                  />
                </td>
                <td className="p-3 text-right border border-gray-300 font-bold text-danger">
                  {installment.remaining.toFixed(2)}
                </td>
                <td className="p-3 text-right border border-gray-300">
                  <span className={`px-2 py-1 rounded-full text-xs ${getStatusColor(installment.status)}`}>
                    {getStatusText(installment.status)}
                  </span>
                </td>
              </tr>
            ))}
          </tbody>
          <tfoot>
            <tr className="bg-gray-100 font-bold">
              <td colSpan={2} className="p-3 text-right border border-gray-300">
                الإجمالي
              </td>
              <td className="p-3 text-right border border-gray-300">
                {installments.reduce((sum, item) => sum + item.amount, 0).toFixed(2)}
              </td>
              <td className="p-3 text-right border border-gray-300">
                {installments.reduce((sum, item) => sum + item.paid, 0).toFixed(2)}
              </td>
              <td className="p-3 text-right border border-gray-300">
                {installments.reduce((sum, item) => sum + item.discount, 0).toFixed(2)}
              </td>
              <td className="p-3 text-right border border-gray-300 text-danger">
                {installments.reduce((sum, item) => sum + item.remaining, 0).toFixed(2)}
              </td>
              <td className="p-3 text-right border border-gray-300"></td>
            </tr>
          </tfoot>
        </table>
      </div>

      <div className="mt-6 p-4 bg-gray-50 rounded-lg border border-gray-200">
        <h3 className="text-lg font-semibold text-primary mb-2">ملاحظات</h3>
        <ul className="list-disc list-inside space-y-1 text-gray-600">
          <li>يمكنك تعديل تاريخ الاستحقاق والمبلغ المستحق لكل قسط حسب الحاجة.</li>
          <li>يتم احتساب المتبقي تلقائياً بناءً على المبلغ المستحق والمدفوع والخصم.</li>
          <li>يتم تحديث حالة القسط تلقائياً بناءً على المبلغ المدفوع والمتبقي.</li>
        </ul>
      </div>
    </div>
  );
};

export default Step4Installments;
