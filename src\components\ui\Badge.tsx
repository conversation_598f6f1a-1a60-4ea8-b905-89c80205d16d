"use client";

import React from 'react';
import { Icon, IconType, COLORS } from './IconProvider';

// تعريف أنواع الشارات
export type BadgeVariant = 'primary' | 'secondary' | 'success' | 'danger' | 'info' | 'warning' | 'light' | 'dark';

// تعريف أحجام الشارات
export type BadgeSize = 'sm' | 'md' | 'lg';

// تعريف خصائص الشارة
export interface BadgeProps {
  children: React.ReactNode;
  variant?: BadgeVariant;
  size?: BadgeSize;
  icon?: IconType;
  rounded?: boolean;
  pill?: boolean;
  className?: string;
}

// مكون الشارة
export const Badge: React.FC<BadgeProps> = ({
  children,
  variant = 'primary',
  size = 'md',
  icon,
  rounded = true,
  pill = false,
  className = '',
}) => {
  // تحديد الألوان بناءً على النوع
  const getVariantClasses = (): string => {
    const variantClasses: Record<BadgeVariant, string> = {
      'primary': `bg-[${COLORS.PRIMARY}]/20 text-[${COLORS.PRIMARY}]`,
      'secondary': `bg-[${COLORS.SECONDARY}]/20 text-[${COLORS.SECONDARY}]`,
      'success': `bg-[${COLORS.SUCCESS}]/20 text-[${COLORS.SUCCESS}]`,
      'danger': `bg-[${COLORS.DANGER}]/20 text-[${COLORS.DANGER}]`,
      'info': `bg-[${COLORS.INFO}]/20 text-[${COLORS.INFO}]`,
      'warning': `bg-[${COLORS.WARNING}]/20 text-[${COLORS.WARNING}]`,
      'light': `bg-[${COLORS.LIGHT}] text-[${COLORS.DARK}] border border-gray-200`,
      'dark': `bg-[${COLORS.DARK}]/20 text-[${COLORS.DARK}]`,
    };

    return variantClasses[variant];
  };

  // تحديد الحجم
  const getSizeClasses = (): string => {
    const sizeClasses: Record<BadgeSize, string> = {
      'sm': 'text-xs py-0.5 px-2',
      'md': 'text-sm py-1 px-2.5',
      'lg': 'text-base py-1.5 px-3',
    };

    return sizeClasses[size];
  };

  // تحديد الشكل
  const getShapeClasses = (): string => {
    if (pill) {
      return 'rounded-full';
    }
    
    return rounded ? 'rounded-md' : '';
  };

  // تجميع الفئات
  const badgeClasses = `
    inline-flex items-center justify-center
    font-medium
    ${getVariantClasses()}
    ${getSizeClasses()}
    ${getShapeClasses()}
    ${className}
  `;

  return (
    <span className={badgeClasses}>
      {icon && (
        <span className="ml-1">
          <Icon name={icon} color={variant.toUpperCase()} size="XS" />
        </span>
      )}
      {children}
    </span>
  );
};

export default Badge;
