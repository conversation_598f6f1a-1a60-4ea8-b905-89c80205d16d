"use client";

import React from 'react';
import { FaCheckCircle, FaExclamationTriangle } from 'react-icons/fa';

interface ImportedDataPreviewProps {
  data: Record<string, any>;
  isVisible: boolean;
}

const ImportedDataPreview: React.FC<ImportedDataPreviewProps> = ({ data, isVisible }) => {
  if (!isVisible || !data) return null;

  // تحويل أسماء الحقول إلى أسماء عربية للعرض
  const fieldLabels: Record<string, string> = {
    fullName: 'الاسم الكامل',
    idNumber: 'رقم الهوية',
    birthDate: 'تاريخ الميلاد',
    gender: 'الجنس',
    religion: 'الديانة',
    maritalStatus: 'الحالة الاجتماعية',
    email: 'البريد الإلكتروني',
    phone: 'رقم الهاتف',
  };

  // تحويل قيم الجنس إلى العربية
  const genderLabels: Record<string, string> = {
    male: 'ذكر',
    female: 'أنثى',
  };

  // تحويل قيم الديانة إلى العربية
  const religionLabels: Record<string, string> = {
    islam: 'الإسلام',
    christianity: 'المسيحية',
  };

  // التحقق من اكتمال البيانات الأساسية
  const requiredFields = ['fullName', 'idNumber', 'birthDate', 'gender'];
  const missingFields = requiredFields.filter(field => !data[field]);
  const isComplete = missingFields.length === 0;

  return (
    <div className="mt-4 mb-6 p-4 bg-gray-50 rounded-md border border-gray-200">
      <div className="flex items-center mb-4">
        {isComplete ? (
          <div className="flex items-center text-green-600">
            <FaCheckCircle className="ml-2" />
            <h3 className="text-lg font-bold">تم استيراد البيانات بنجاح</h3>
          </div>
        ) : (
          <div className="flex items-center text-amber-600">
            <FaExclamationTriangle className="ml-2" />
            <h3 className="text-lg font-bold">تم استيراد البيانات مع وجود حقول ناقصة</h3>
          </div>
        )}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {Object.entries(fieldLabels).map(([field, label]) => (
          <div key={field} className="flex flex-col">
            <span className="text-sm text-gray-500">{label}</span>
            <span className={`font-medium ${!data[field] && requiredFields.includes(field) ? 'text-red-500' : ''}`}>
              {field === 'gender' && data[field] ? genderLabels[data[field]] || data[field] :
               field === 'religion' && data[field] ? religionLabels[data[field]] || data[field] :
               data[field] || <span className="text-red-500">غير متوفر</span>}
            </span>
          </div>
        ))}
      </div>

      {!isComplete && (
        <div className="mt-4 p-3 bg-amber-50 text-amber-700 rounded-md border border-amber-200">
          <p className="text-sm">
            <FaExclamationTriangle className="inline-block ml-1" />
            يرجى إكمال الحقول الإلزامية الناقصة: {missingFields.map(field => fieldLabels[field]).join('، ')}
          </p>
        </div>
      )}
    </div>
  );
};

export default ImportedDataPreview;
