# دليل استخدام Supabase REST API

## مقدمة

يمكنك التفاعل مع قاعدة بيانات Supabase من خارج التطبيق باستخدام REST API. هذا الدليل يشرح كيفية استخدام Supabase REST API للوصول إلى بيانات المراحل الدراسية والفصول.

## معلومات الاتصال

للاتصال بـ Supabase REST API، ستحتاج إلى المعلومات التالية:

```
URL: https://bbigwqwtmhctqrptkuni.supabase.co
API Key: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJiaWd3cXd0bWhjdHFycHRrdW5pIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYyOTg3MjYsImV4cCI6MjA2MTg3NDcyNn0.k6_MpI7ohitVroAuc2e5nYpAdFRphn7185_u_sm_ZVM
```

## أمثلة على استخدام REST API

### الحصول على جميع المراحل الدراسية

```bash
curl 'https://bbigwqwtmhctqrptkuni.supabase.co/rest/v1/grades?select=*&order=id.asc' \
  -H "apikey: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJiaWd3cXd0bWhjdHFycHRrdW5pIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYyOTg3MjYsImV4cCI6MjA2MTg3NDcyNn0.k6_MpI7ohitVroAuc2e5nYpAdFRphn7185_u_sm_ZVM" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJiaWd3cXd0bWhjdHFycHRrdW5pIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYyOTg3MjYsImV4cCI6MjA2MTg3NDcyNn0.k6_MpI7ohitVroAuc2e5nYpAdFRphn7185_u_sm_ZVM"
```

### الحصول على الفصول الدراسية لمرحلة معينة

```bash
curl 'https://bbigwqwtmhctqrptkuni.supabase.co/rest/v1/classes?select=*&grade_id=eq.1&order=name.asc' \
  -H "apikey: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJiaWd3cXd0bWhjdHFycHRrdW5pIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYyOTg3MjYsImV4cCI6MjA2MTg3NDcyNn0.k6_MpI7ohitVroAuc2e5nYpAdFRphn7185_u_sm_ZVM" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJiaWd3cXd0bWhjdHFycHRrdW5pIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYyOTg3MjYsImV4cCI6MjA2MTg3NDcyNn0.k6_MpI7ohitVroAuc2e5nYpAdFRphn7185_u_sm_ZVM"
```

### إضافة مرحلة دراسية جديدة

```bash
curl -X POST 'https://bbigwqwtmhctqrptkuni.supabase.co/rest/v1/grades' \
  -H "apikey: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJiaWd3cXd0bWhjdHFycHRrdW5pIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYyOTg3MjYsImV4cCI6MjA2MTg3NDcyNn0.k6_MpI7ohitVroAuc2e5nYpAdFRphn7185_u_sm_ZVM" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJiaWd3cXd0bWhjdHFycHRrdW5pIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYyOTg3MjYsImV4cCI6MjA2MTg3NDcyNn0.k6_MpI7ohitVroAuc2e5nYpAdFRphn7185_u_sm_ZVM" \
  -H "Content-Type: application/json" \
  -d '{"name":"الصف الأول الثانوي","level":"ثانوي"}'
```

### إضافة فصل دراسي جديد

```bash
curl -X POST 'https://bbigwqwtmhctqrptkuni.supabase.co/rest/v1/classes' \
  -H "apikey: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJiaWd3cXd0bWhjdHFycHRrdW5pIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYyOTg3MjYsImV4cCI6MjA2MTg3NDcyNn0.k6_MpI7ohitVroAuc2e5nYpAdFRphn7185_u_sm_ZVM" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJiaWd3cXd0bWhjdHFycHRrdW5pIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYyOTg3MjYsImV4cCI6MjA2MTg3NDcyNn0.k6_MpI7ohitVroAuc2e5nYpAdFRphn7185_u_sm_ZVM" \
  -H "Content-Type: application/json" \
  -d '{"name":"1A","grade_id":10,"capacity":30}'
```

### تحديث مرحلة دراسية

```bash
curl -X PATCH 'https://bbigwqwtmhctqrptkuni.supabase.co/rest/v1/grades?id=eq.1' \
  -H "apikey: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJiaWd3cXd0bWhjdHFycHRrdW5pIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYyOTg3MjYsImV4cCI6MjA2MTg3NDcyNn0.k6_MpI7ohitVroAuc2e5nYpAdFRphn7185_u_sm_ZVM" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJiaWd3cXd0bWhjdHFycHRrdW5pIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYyOTg3MjYsImV4cCI6MjA2MTg3NDcyNn0.k6_MpI7ohitVroAuc2e5nYpAdFRphn7185_u_sm_ZVM" \
  -H "Content-Type: application/json" \
  -d '{"name":"الصف الأول الابتدائي - معدل"}'
```

### حذف فصل دراسي

```bash
curl -X DELETE 'https://bbigwqwtmhctqrptkuni.supabase.co/rest/v1/classes?id=eq.1' \
  -H "apikey: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJiaWd3cXd0bWhjdHFycHRrdW5pIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYyOTg3MjYsImV4cCI6MjA2MTg3NDcyNn0.k6_MpI7ohitVroAuc2e5nYpAdFRphn7185_u_sm_ZVM" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJiaWd3cXd0bWhjdHFycHRrdW5pIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYyOTg3MjYsImV4cCI6MjA2MTg3NDcyNn0.k6_MpI7ohitVroAuc2e5nYpAdFRphn7185_u_sm_ZVM"
```

## استخدام Supabase مع لغات البرمجة المختلفة

### JavaScript

```javascript
async function fetchGrades() {
  const response = await fetch('https://bbigwqwtmhctqrptkuni.supabase.co/rest/v1/grades?select=*&order=id.asc', {
    headers: {
      'apikey': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJiaWd3cXd0bWhjdHFycHRrdW5pIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYyOTg3MjYsImV4cCI6MjA2MTg3NDcyNn0.k6_MpI7ohitVroAuc2e5nYpAdFRphn7185_u_sm_ZVM',
      'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJiaWd3cXd0bWhjdHFycHRrdW5pIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYyOTg3MjYsImV4cCI6MjA2MTg3NDcyNn0.k6_MpI7ohitVroAuc2e5nYpAdFRphn7185_u_sm_ZVM'
    }
  });
  const data = await response.json();
  return data;
}
```

### Python

```python
import requests

def fetch_grades():
    url = 'https://bbigwqwtmhctqrptkuni.supabase.co/rest/v1/grades'
    headers = {
        'apikey': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJiaWd3cXd0bWhjdHFycHRrdW5pIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYyOTg3MjYsImV4cCI6MjA2MTg3NDcyNn0.k6_MpI7ohitVroAuc2e5nYpAdFRphn7185_u_sm_ZVM',
        'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJiaWd3cXd0bWhjdHFycHRrdW5pIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYyOTg3MjYsImV4cCI6MjA2MTg3NDcyNn0.k6_MpI7ohitVroAuc2e5nYpAdFRphn7185_u_sm_ZVM'
    }
    params = {
        'select': '*',
        'order': 'id.asc'
    }
    response = requests.get(url, headers=headers, params=params)
    return response.json()
```

## ملاحظات هامة

1. **الأمان**: احرص على عدم مشاركة مفتاح API الخاص بك مع أي شخص غير موثوق به.

2. **التحكم في الوصول**: يمكنك إعداد سياسات الأمان في Supabase للتحكم في من يمكنه الوصول إلى البيانات وتعديلها.

3. **التوثيق الرسمي**: للحصول على معلومات أكثر تفصيلاً، يمكنك الرجوع إلى [التوثيق الرسمي لـ Supabase REST API](https://supabase.com/docs/reference/javascript/select).

4. **مكتبات العميل**: بدلاً من استخدام طلبات HTTP المباشرة، يمكنك استخدام مكتبات العميل الرسمية لـ Supabase المتوفرة للعديد من لغات البرمجة مثل JavaScript و Python و Dart وغيرها.

## استكشاف الأخطاء وإصلاحها

إذا واجهت مشاكل في استخدام REST API:

1. تأكد من صحة مفتاح API والـ URL.
2. تحقق من أن الجداول موجودة في قاعدة البيانات.
3. تأكد من أن لديك الصلاحيات المناسبة للوصول إلى الجداول.
4. تحقق من استجابة الخطأ للحصول على مزيد من المعلومات حول المشكلة.

## الخلاصة

باستخدام Supabase REST API، يمكنك التفاعل مع قاعدة بيانات المدرسة من أي تطبيق أو خدمة خارجية. هذا يتيح لك إمكانية دمج نظام إدارة المدرسة مع أنظمة أخرى أو إنشاء تطبيقات مخصصة تستخدم نفس البيانات.