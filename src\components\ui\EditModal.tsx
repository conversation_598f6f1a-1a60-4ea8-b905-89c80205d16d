"use client";

import React, { ReactNode } from 'react';
import { FaTimes, FaSave } from 'react-icons/fa';

interface EditModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: () => void;
  title: string;
  children: ReactNode;
  isSaving?: boolean;
  color?: string;
  icon?: ReactNode;
}

/**
 * مكون النافذة المنبثقة للتعديل
 * يستخدم لعرض نماذج تعديل البيانات المختلفة
 */
const EditModal: React.FC<EditModalProps> = ({
  isOpen,
  onClose,
  onSave,
  title,
  children,
  isSaving = false,
  color = '#5578EB',
  icon
}) => {
  if (!isOpen) return null;

  // منع انتشار النقرات للخلفية
  const handleContentClick = (e: React.MouseEvent) => {
    e.stopPropagation();
  };

  return (
    <div 
      className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
      onClick={onClose}
    >
      <div 
        className="bg-white rounded-lg shadow-xl w-full max-w-3xl max-h-[90vh] overflow-hidden"
        onClick={handleContentClick}
      >
        {/* رأس النافذة */}
        <div 
          className="flex justify-between items-center p-4 border-b"
          style={{ borderColor: `${color}40` }}
        >
          <div className="flex items-center">
            {icon && (
              <div 
                className="w-10 h-10 rounded-full flex items-center justify-center text-white ml-3"
                style={{ backgroundColor: color }}
              >
                {icon}
              </div>
            )}
            <h2 className="text-xl font-bold" style={{ color }}>
              {title}
            </h2>
          </div>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 transition-colors duration-200"
          >
            <FaTimes size={20} />
          </button>
        </div>

        {/* محتوى النافذة */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-130px)]">
          {children}
        </div>

        {/* أزرار الإجراءات */}
        <div className="p-4 border-t flex justify-end gap-2" style={{ borderColor: `${color}40` }}>
          <button
            onClick={onClose}
            className="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition-colors duration-200"
          >
            إلغاء
          </button>
          <button
            onClick={onSave}
            disabled={isSaving}
            className="px-4 py-2 text-white rounded-md flex items-center transition-colors duration-200"
            style={{ 
              backgroundColor: isSaving ? `${color}80` : color,
              opacity: isSaving ? 0.8 : 1
            }}
          >
            {isSaving ? (
              <>
                <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin ml-2"></div>
                جاري الحفظ...
              </>
            ) : (
              <>
                <FaSave className="ml-2" />
                حفظ التغييرات
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default EditModal;
