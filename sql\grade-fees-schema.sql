-- إن<PERSON><PERSON>ء جدول الرسوم الدراسية لكل مرحلة وسنة دراسية
CREATE TABLE IF NOT EXISTS grade_fees (
  id SERIAL PRIMARY KEY,
  grade_id INTEGER REFERENCES school_grades(id) ON DELETE CASCADE,
  academic_year VARCHAR(20) NOT NULL, -- مثل "2024-2025"
  tuition_fee NUMERIC(10,2) NOT NULL DEFAULT 0,
  registration_fee NUMERIC(10,2) DEFAULT 0,
  books_fee NUMERIC(10,2) DEFAULT 0,
  activities_fee NUMERIC(10,2) DEFAULT 0,
  transportation_fee NUMERIC(10,2) DEFAULT 0,
  uniform_fee NUMERIC(10,2) DEFAULT 0,
  total_fee NUMERIC(10,2) GENERATED ALWAYS AS (
    tuition_fee + COALESCE(registration_fee, 0) + COALESCE(books_fee, 0) + 
    COALESCE(activities_fee, 0) + COALESCE(transportation_fee, 0) + COALESCE(uniform_fee, 0)
  ) STORED,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  
  -- فهرس فريد لضمان عدم تكرار الرسوم لنفس المرحلة والسنة
  UNIQUE(grade_id, academic_year)
);

-- إنشاء جدول تاريخ تحديث الرسوم (لتتبع التغييرات)
CREATE TABLE IF NOT EXISTS grade_fees_history (
  id SERIAL PRIMARY KEY,
  grade_fee_id INTEGER REFERENCES grade_fees(id) ON DELETE CASCADE,
  old_tuition_fee NUMERIC(10,2),
  new_tuition_fee NUMERIC(10,2),
  old_total_fee NUMERIC(10,2),
  new_total_fee NUMERIC(10,2),
  updated_by VARCHAR(255),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  reason TEXT
);

-- إنشاء دالة لتحديث الرسوم المالية للطلاب تلقائياً
CREATE OR REPLACE FUNCTION update_student_fees_on_grade_change()
RETURNS TRIGGER AS $$
BEGIN
  -- تحديث الرسوم المالية لجميع الطلاب في هذه المرحلة والسنة الدراسية
  UPDATE financial_info 
  SET 
    tuition_fee = NEW.tuition_fee,
    updated_at = CURRENT_TIMESTAMP
  FROM students s
  JOIN academic_records ar ON s.id = ar.student_id
  WHERE financial_info.student_id = s.id
    AND ar.grade_id = NEW.grade_id
    AND ar.academic_year = NEW.academic_year
    AND ar.status = 'active';
    
  -- إضافة سجل في تاريخ التحديثات
  IF TG_OP = 'UPDATE' AND OLD.tuition_fee != NEW.tuition_fee THEN
    INSERT INTO grade_fees_history (
      grade_fee_id, 
      old_tuition_fee, 
      new_tuition_fee,
      old_total_fee,
      new_total_fee,
      reason
    ) VALUES (
      NEW.id,
      OLD.tuition_fee,
      NEW.tuition_fee,
      OLD.total_fee,
      NEW.total_fee,
      'تحديث تلقائي للرسوم'
    );
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- إنشاء المشغل للتحديث التلقائي
DROP TRIGGER IF EXISTS trigger_update_student_fees ON grade_fees;
CREATE TRIGGER trigger_update_student_fees
  AFTER INSERT OR UPDATE ON grade_fees
  FOR EACH ROW
  EXECUTE FUNCTION update_student_fees_on_grade_change();

-- إنشاء دالة لتحديث الأقساط تلقائياً عند تغيير الرسوم
CREATE OR REPLACE FUNCTION recalculate_installments_on_fee_change()
RETURNS TRIGGER AS $$
DECLARE
  financial_record RECORD;
  installment_amount NUMERIC;
  remaining_amount NUMERIC;
BEGIN
  -- البحث عن جميع السجلات المالية المتأثرة
  FOR financial_record IN 
    SELECT fi.id, fi.student_id, fi.discount_amount, fi.paid_amount, fi.installments_count
    FROM financial_info fi
    JOIN students s ON fi.student_id = s.id
    JOIN academic_records ar ON s.id = ar.student_id
    WHERE ar.grade_id = NEW.grade_id
      AND ar.academic_year = NEW.academic_year
      AND ar.status = 'active'
      AND fi.payment_method = 'installments'
      AND fi.installments_count > 0
  LOOP
    -- حساب المبلغ المتبقي بعد الخصم والمبلغ المدفوع
    remaining_amount := NEW.tuition_fee - COALESCE(financial_record.discount_amount, 0) - COALESCE(financial_record.paid_amount, 0);
    
    -- حساب قيمة القسط الواحد
    IF financial_record.installments_count > 0 AND remaining_amount > 0 THEN
      installment_amount := remaining_amount / financial_record.installments_count;
      
      -- تحديث الأقساط الموجودة
      UPDATE installments 
      SET 
        amount = installment_amount,
        remaining = CASE 
          WHEN paid > 0 THEN GREATEST(0, installment_amount - paid)
          ELSE installment_amount
        END,
        updated_at = CURRENT_TIMESTAMP
      WHERE financial_info_id = financial_record.id
        AND status != 'paid';
    END IF;
  END LOOP;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- إنشاء المشغل لإعادة حساب الأقساط
DROP TRIGGER IF EXISTS trigger_recalculate_installments ON grade_fees;
CREATE TRIGGER trigger_recalculate_installments
  AFTER UPDATE ON grade_fees
  FOR EACH ROW
  WHEN (OLD.tuition_fee != NEW.tuition_fee)
  EXECUTE FUNCTION recalculate_installments_on_fee_change();

-- إنشاء فهارس لتحسين الأداء
CREATE INDEX IF NOT EXISTS idx_grade_fees_grade_year ON grade_fees(grade_id, academic_year);
CREATE INDEX IF NOT EXISTS idx_grade_fees_active ON grade_fees(is_active);
CREATE INDEX IF NOT EXISTS idx_grade_fees_history_grade_fee ON grade_fees_history(grade_fee_id);

-- إدراج بيانات نموذجية للسنة الدراسية الحالية
INSERT INTO grade_fees (grade_id, academic_year, tuition_fee, registration_fee, books_fee, activities_fee) 
SELECT 
  id,
  '2024-2025',
  5000, -- رسوم دراسية افتراضية
  500,  -- رسوم تسجيل
  300,  -- رسوم كتب
  200   -- رسوم أنشطة
FROM school_grades 
ON CONFLICT (grade_id, academic_year) DO NOTHING;
