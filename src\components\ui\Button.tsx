"use client";

import React from 'react';
import { Icon, IconType, COLORS } from './IconProvider';

// تعريف أنواع الأزرار
export type ButtonVariant = 'primary' | 'secondary' | 'success' | 'danger' | 'info' | 'warning' | 'light' | 'dark' | 'outline-primary' | 'outline-secondary' | 'outline-success' | 'outline-danger' | 'outline-info' | 'outline-warning' | 'outline-light' | 'outline-dark';

// تعريف أحجام الأزرار
export type ButtonSize = 'sm' | 'md' | 'lg';

// تعريف خصائص الزر
export interface ButtonProps {
  children: React.ReactNode;
  variant?: ButtonVariant;
  size?: ButtonSize;
  icon?: IconType;
  iconPosition?: 'left' | 'right';
  fullWidth?: boolean;
  rounded?: boolean;
  disabled?: boolean;
  className?: string;
  onClick?: () => void;
}

// مكون الزر
export const Button: React.FC<ButtonProps> = ({
  children,
  variant = 'primary',
  size = 'md',
  icon,
  iconPosition = 'left',
  fullWidth = false,
  rounded = false,
  disabled = false,
  className = '',
  onClick,
}) => {
  // تحديد الألوان بناءً على النوع
  const getVariantClasses = (): string => {
    const baseClasses = 'transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-opacity-50';
    
    const variantClasses: Record<ButtonVariant, string> = {
      'primary': `bg-[${COLORS.PRIMARY}] hover:bg-[${COLORS.PRIMARY}]/80 text-white focus:ring-[${COLORS.PRIMARY}]/50`,
      'secondary': `bg-[${COLORS.SECONDARY}] hover:bg-[${COLORS.SECONDARY}]/80 text-white focus:ring-[${COLORS.SECONDARY}]/50`,
      'success': `bg-[${COLORS.SUCCESS}] hover:bg-[${COLORS.SUCCESS}]/80 text-white focus:ring-[${COLORS.SUCCESS}]/50`,
      'danger': `bg-[${COLORS.DANGER}] hover:bg-[${COLORS.DANGER}]/80 text-white focus:ring-[${COLORS.DANGER}]/50`,
      'info': `bg-[${COLORS.INFO}] hover:bg-[${COLORS.INFO}]/80 text-white focus:ring-[${COLORS.INFO}]/50`,
      'warning': `bg-[${COLORS.WARNING}] hover:bg-[${COLORS.WARNING}]/80 text-black focus:ring-[${COLORS.WARNING}]/50`,
      'light': `bg-[${COLORS.LIGHT}] hover:bg-[${COLORS.LIGHT}]/80 text-black focus:ring-[${COLORS.LIGHT}]/50`,
      'dark': `bg-[${COLORS.DARK}] hover:bg-[${COLORS.DARK}]/80 text-white focus:ring-[${COLORS.DARK}]/50`,
      'outline-primary': `border border-[${COLORS.PRIMARY}] text-[${COLORS.PRIMARY}] hover:bg-[${COLORS.PRIMARY}]/10 focus:ring-[${COLORS.PRIMARY}]/50`,
      'outline-secondary': `border border-[${COLORS.SECONDARY}] text-[${COLORS.SECONDARY}] hover:bg-[${COLORS.SECONDARY}]/10 focus:ring-[${COLORS.SECONDARY}]/50`,
      'outline-success': `border border-[${COLORS.SUCCESS}] text-[${COLORS.SUCCESS}] hover:bg-[${COLORS.SUCCESS}]/10 focus:ring-[${COLORS.SUCCESS}]/50`,
      'outline-danger': `border border-[${COLORS.DANGER}] text-[${COLORS.DANGER}] hover:bg-[${COLORS.DANGER}]/10 focus:ring-[${COLORS.DANGER}]/50`,
      'outline-info': `border border-[${COLORS.INFO}] text-[${COLORS.INFO}] hover:bg-[${COLORS.INFO}]/10 focus:ring-[${COLORS.INFO}]/50`,
      'outline-warning': `border border-[${COLORS.WARNING}] text-[${COLORS.WARNING}] hover:bg-[${COLORS.WARNING}]/10 focus:ring-[${COLORS.WARNING}]/50`,
      'outline-light': `border border-[${COLORS.LIGHT}] text-[${COLORS.LIGHT}] hover:bg-[${COLORS.LIGHT}]/10 focus:ring-[${COLORS.LIGHT}]/50`,
      'outline-dark': `border border-[${COLORS.DARK}] text-[${COLORS.DARK}] hover:bg-[${COLORS.DARK}]/10 focus:ring-[${COLORS.DARK}]/50`,
    };

    return `${baseClasses} ${variantClasses[variant]}`;
  };

  // تحديد الحجم
  const getSizeClasses = (): string => {
    const sizeClasses: Record<ButtonSize, string> = {
      'sm': 'py-1 px-3 text-sm',
      'md': 'py-2 px-4 text-base',
      'lg': 'py-3 px-6 text-lg',
    };

    return sizeClasses[size];
  };

  // تحديد الشكل
  const getRoundedClasses = (): string => {
    return rounded ? 'rounded-full' : 'rounded-md';
  };

  // تحديد العرض
  const getWidthClasses = (): string => {
    return fullWidth ? 'w-full' : '';
  };

  // تجميع الفئات
  const buttonClasses = `
    ${getVariantClasses()}
    ${getSizeClasses()}
    ${getRoundedClasses()}
    ${getWidthClasses()}
    ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
    flex items-center justify-center
    ${className}
  `;

  // تحديد لون الأيقونة بناءً على نوع الزر
  const getIconColor = (): string => {
    if (variant.startsWith('outline-')) {
      return variant.replace('outline-', '').toUpperCase();
    }
    
    if (variant === 'light' || variant === 'warning') {
      return 'DARK';
    }
    
    return 'LIGHT';
  };

  return (
    <button
      className={buttonClasses}
      disabled={disabled}
      onClick={onClick}
    >
      {icon && iconPosition === 'left' && (
        <span className="ml-2">
          <Icon name={icon} color={getIconColor()} size="SM" />
        </span>
      )}
      {children}
      {icon && iconPosition === 'right' && (
        <span className="mr-2">
          <Icon name={icon} color={getIconColor()} size="SM" />
        </span>
      )}
    </button>
  );
};

export default Button;
