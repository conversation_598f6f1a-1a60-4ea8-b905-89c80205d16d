"use client";

import React, { useEffect, useState } from 'react';
import { useFormik } from 'formik';
import * as Yup from 'yup';

interface Guardian {
  id: number;
  fullName: string;
  relationship: string;
  idNumber: string;
  phone: string;
  email: string;
  occupation: string;
  workplace: string;
}

interface Step2Props {
  onComplete: (data: any, isValid: boolean) => void;
  formData: Record<string, any>;
}

const Step2GuardianInfo: React.FC<Step2Props> = ({ onComplete, formData }) => {
  const [guardians, setGuardians] = useState<Guardian[]>(
    formData.guardians || [
      {
        id: 1,
        fullName: '',
        relationship: '',
        idNumber: '',
        phone: '',
        email: '',
        occupation: '',
        workplace: '',
      },
    ]
  );

  // التحقق من صحة البيانات باستخدام Yup
  const guardianSchema = Yup.object({
    fullName: Yup.string().required('اسم ولي الأمر مطلوب'),
    relationship: Yup.string().required('العلاقة بالطالب مطلوبة'),
    idNumber: Yup.string().required('رقم الهوية مطلوب'),
    phone: Yup.string().required('رقم الهاتف مطلوب'),
    email: Yup.string().email('البريد الإلكتروني غير صالح'),
    occupation: Yup.string(),
    workplace: Yup.string(),
  });

  // إعداد نموذج Formik للولي الأمر الحالي
  const formik = useFormik({
    initialValues: guardians[0],
    validationSchema: guardianSchema,
    onSubmit: (values) => {
      console.log('Form submitted with values:', values);
    },
  });

  // إضافة ولي أمر جديد
  const addGuardian = () => {
    const newGuardian: Guardian = {
      id: guardians.length + 1,
      fullName: '',
      relationship: '',
      idNumber: '',
      phone: '',
      email: '',
      occupation: '',
      workplace: '',
    };
    setGuardians([...guardians, newGuardian]);
  };

  // حذف ولي أمر
  const removeGuardian = (id: number) => {
    if (guardians.length > 1) {
      setGuardians(guardians.filter(guardian => guardian.id !== id));
    }
  };

  // تحديث بيانات ولي الأمر
  const updateGuardian = (id: number, field: string, value: string) => {
    const updatedGuardians = guardians.map(guardian => {
      if (guardian.id === id) {
        return { ...guardian, [field]: value };
      }
      return guardian;
    });
    setGuardians(updatedGuardians);
  };

  // التحقق من صحة بيانات جميع أولياء الأمور
  const validateAllGuardians = () => {
    return guardians.every(guardian => {
      return guardian.fullName && guardian.relationship && guardian.idNumber && guardian.phone;
    });
  };

  // تحديث البيانات عند تغييرها
  useEffect(() => {
    const isValid = validateAllGuardians();

    // استخدام مرجع ثابت لدالة onComplete لتجنب الحلقات اللانهائية
    const timer = setTimeout(() => {
      onComplete({ guardians }, isValid);
    }, 100);

    return () => clearTimeout(timer);
    // إزالة onComplete من مصفوفة التبعيات لتجنب الحلقات اللانهائية
  }, [guardians]);

  return (
    <div className="bg-white rounded-lg p-6">
      <h2 className="text-xl font-bold text-primary mb-6 border-r-4 border-primary pr-3">
        بيانات ولي الأمر
      </h2>

      {guardians.map((guardian, index) => (
        <div key={guardian.id} className="mb-8 p-4 border border-gray-200 rounded-lg">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-semibold text-secondary">
              ولي الأمر {index + 1}
            </h3>
            {guardians.length > 1 && (
              <button
                type="button"
                onClick={() => removeGuardian(guardian.id)}
                className="text-danger hover:text-danger/80"
              >
                <span className="material-icons">delete</span>
              </button>
            )}
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* اسم ولي الأمر */}
            <div>
              <label htmlFor={`fullName-${guardian.id}`} className="block text-gray-700 font-medium mb-2">
                اسم ولي الأمر <span className="text-danger">*</span>
              </label>
              <input
                type="text"
                id={`fullName-${guardian.id}`}
                name={`fullName-${guardian.id}`}
                className={`w-full p-3 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary
                  ${!guardian.fullName ? 'border-danger' : 'border-gray-300'}`}
                value={guardian.fullName}
                onChange={(e) => updateGuardian(guardian.id, 'fullName', e.target.value)}
                placeholder="أدخل اسم ولي الأمر"
              />
              {!guardian.fullName && (
                <p className="mt-1 text-danger text-sm">اسم ولي الأمر مطلوب</p>
              )}
            </div>

            {/* العلاقة بالطالب */}
            <div>
              <label htmlFor={`relationship-${guardian.id}`} className="block text-gray-700 font-medium mb-2">
                العلاقة بالطالب <span className="text-danger">*</span>
              </label>
              <select
                id={`relationship-${guardian.id}`}
                name={`relationship-${guardian.id}`}
                className={`w-full p-3 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary
                  ${!guardian.relationship ? 'border-danger' : 'border-gray-300'}`}
                value={guardian.relationship}
                onChange={(e) => updateGuardian(guardian.id, 'relationship', e.target.value)}
              >
                <option value="">اختر العلاقة</option>
                <option value="father">الأب</option>
                <option value="mother">الأم</option>
                <option value="brother">الأخ</option>
                <option value="sister">الأخت</option>
                <option value="grandfather">الجد</option>
                <option value="grandmother">الجدة</option>
                <option value="uncle">العم</option>
                <option value="aunt">العمة</option>
                <option value="other">أخرى</option>
              </select>
              {!guardian.relationship && (
                <p className="mt-1 text-danger text-sm">العلاقة بالطالب مطلوبة</p>
              )}
            </div>

            {/* رقم الهوية */}
            <div>
              <label htmlFor={`idNumber-${guardian.id}`} className="block text-gray-700 font-medium mb-2">
                رقم الهوية <span className="text-danger">*</span>
              </label>
              <input
                type="text"
                id={`idNumber-${guardian.id}`}
                name={`idNumber-${guardian.id}`}
                className={`w-full p-3 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary
                  ${!guardian.idNumber ? 'border-danger' : 'border-gray-300'}`}
                value={guardian.idNumber}
                onChange={(e) => updateGuardian(guardian.id, 'idNumber', e.target.value)}
                placeholder="أدخل رقم الهوية"
              />
              {!guardian.idNumber && (
                <p className="mt-1 text-danger text-sm">رقم الهوية مطلوب</p>
              )}
            </div>

            {/* رقم الهاتف */}
            <div>
              <label htmlFor={`phone-${guardian.id}`} className="block text-gray-700 font-medium mb-2">
                رقم الهاتف <span className="text-danger">*</span>
              </label>
              <input
                type="tel"
                id={`phone-${guardian.id}`}
                name={`phone-${guardian.id}`}
                className={`w-full p-3 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary
                  ${!guardian.phone ? 'border-danger' : 'border-gray-300'}`}
                value={guardian.phone}
                onChange={(e) => updateGuardian(guardian.id, 'phone', e.target.value)}
                placeholder="أدخل رقم الهاتف"
              />
              {!guardian.phone && (
                <p className="mt-1 text-danger text-sm">رقم الهاتف مطلوب</p>
              )}
            </div>

            {/* البريد الإلكتروني */}
            <div>
              <label htmlFor={`email-${guardian.id}`} className="block text-gray-700 font-medium mb-2">
                البريد الإلكتروني
              </label>
              <input
                type="email"
                id={`email-${guardian.id}`}
                name={`email-${guardian.id}`}
                className="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                value={guardian.email}
                onChange={(e) => updateGuardian(guardian.id, 'email', e.target.value)}
                placeholder="أدخل البريد الإلكتروني"
              />
            </div>

            {/* المهنة */}
            <div>
              <label htmlFor={`occupation-${guardian.id}`} className="block text-gray-700 font-medium mb-2">
                المهنة
              </label>
              <input
                type="text"
                id={`occupation-${guardian.id}`}
                name={`occupation-${guardian.id}`}
                className="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                value={guardian.occupation}
                onChange={(e) => updateGuardian(guardian.id, 'occupation', e.target.value)}
                placeholder="أدخل المهنة"
              />
            </div>

            {/* مكان العمل */}
            <div>
              <label htmlFor={`workplace-${guardian.id}`} className="block text-gray-700 font-medium mb-2">
                مكان العمل
              </label>
              <input
                type="text"
                id={`workplace-${guardian.id}`}
                name={`workplace-${guardian.id}`}
                className="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                value={guardian.workplace}
                onChange={(e) => updateGuardian(guardian.id, 'workplace', e.target.value)}
                placeholder="أدخل مكان العمل"
              />
            </div>
          </div>
        </div>
      ))}

      <button
        type="button"
        onClick={addGuardian}
        className="mt-4 flex items-center text-primary hover:text-primary/80"
      >
        <span className="material-icons ml-1">add_circle</span>
        إضافة ولي أمر آخر
      </button>

      <div className="mt-4 text-gray-500 text-sm">
        <p>الحقول المميزة بـ <span className="text-danger">*</span> إلزامية</p>
      </div>
    </div>
  );
};

export default Step2GuardianInfo;
