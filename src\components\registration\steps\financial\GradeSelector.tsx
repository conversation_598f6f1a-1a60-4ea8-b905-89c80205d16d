"use client";

import React, { useEffect, useState } from 'react';
import { supabase } from '@/lib/supabase';

interface GradeSelectorProps {
  value: string;
  onChange: (e: React.ChangeEvent<HTMLSelectElement>) => void;
  onBlur: (e: React.FocusEvent<HTMLElement>) => void;
  error?: string;
  touched?: boolean | any;
  disabled?: boolean;
}

interface Grade {
  id: number;
  name: string;
  level: string;
}

const GradeSelector: React.FC<GradeSelectorProps> = ({
  value,
  onChange,
  onBlur,
  error,
  touched,
  disabled
}) => {
  const [grades, setGrades] = useState<Grade[]>([]);
  const [loading, setLoading] = useState(true);

  // جلب المراحل الدراسية من قاعدة البيانات
  useEffect(() => {
    const fetchGrades = async () => {
      try {
        setLoading(true);
        console.log('Fetching grades from database...');

        // استخدام جدول school_grades
        const { data, error } = await supabase
          .from('school_grades')
          .select('id, name, level')
          .order('id');

        if (error) {
          console.error('Error fetching from school_grades:', error);
          throw new Error('لا يمكن الوصول إلى جدول المراحل الدراسية: ' + error.message);
        }

        console.log('Grades data:', data);

        if (data && data.length > 0) {
          setGrades(data);
          return;
        }

        console.error('لا توجد مراحل دراسية في قاعدة البيانات');
        // لا نستخدم بيانات افتراضية، بل نعرض رسالة خطأ للمستخدم
        setGrades([]);
        throw new Error('لا توجد مراحل دراسية في قاعدة البيانات. يرجى التحقق من قاعدة البيانات أو الاتصال بمسؤول النظام.');
      } catch (error: any) {
        console.error('Error in fetchGrades:', error.message || error);
        // لا نستخدم بيانات افتراضية، بل نعرض رسالة خطأ للمستخدم
        setGrades([]);
        throw new Error('حدث خطأ أثناء جلب المراحل الدراسية. يرجى التحقق من الاتصال بقاعدة البيانات أو الاتصال بمسؤول النظام.');
      } finally {
        setLoading(false);
      }
    };

    fetchGrades();
  }, []);

  return (
    <div>
      <label htmlFor="gradeId" className="block text-gray-700 font-medium mb-2">
        المرحلة الدراسية <span className="text-danger">*</span>
      </label>
      <select
        id="gradeId"
        name="gradeId"
        className={`w-full p-3 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary
          ${touched && error ? 'border-danger' : 'border-gray-300'}`}
        onChange={onChange}
        onBlur={onBlur}
        value={value}
        disabled={disabled || loading}
      >
        <option value="">اختر المرحلة الدراسية</option>
        {grades.map((grade) => (
          <option key={grade.id} value={grade.id}>
            {grade.name}
          </option>
        ))}
      </select>
      {touched && error && (
        <p className="mt-1 text-danger text-sm">{error}</p>
      )}
      {loading && (
        <p className="mt-1 text-gray-500 text-sm">جاري تحميل المراحل الدراسية...</p>
      )}
    </div>
  );
};

export default GradeSelector;
export type { Grade };
