"use client";

import { useState, useEffect } from 'react';
import Layout from "@/components/Layout";
import { GradeTable } from '@/components/students';
import { Grade } from '@/types/table.types';
import { supabase } from '@/lib/supabase';
import { FaPlus, FaGraduationCap } from 'react-icons/fa';

export default function GradeList() {
  const [grades, setGrades] = useState<Grade[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // جلب بيانات المراحل الدراسية
  useEffect(() => {
    async function fetchGrades() {
      try {
        setIsLoading(true);
        setError(null);

        const { data, error } = await supabase
          .from('school_grades')
          .select('*')
          .order('id', { ascending: true });

        if (error) {
          throw new Error(error.message);
        }

        setGrades(data || []);
      } catch (err: any) {
        console.error('Error fetching grades:', err);
        setError(err.message || 'حدث خطأ أثناء جلب بيانات المراحل الدراسية');
      } finally {
        setIsLoading(false);
      }
    }

    fetchGrades();
  }, []);

  // عرض تفاصيل المرحلة الدراسية
  const handleViewGrade = (grade: Grade) => {
    // عرض تفاصيل المرحلة في نافذة منبثقة
    alert(`تفاصيل المرحلة: ${grade.name}\nالمستوى: ${grade.level}`);
  };

  // تعديل بيانات المرحلة الدراسية
  const handleEditGrade = (grade: Grade) => {
    // تنفيذ عملية التعديل (سيتم تطويرها لاحقًا)
    alert(`سيتم تطوير صفحة تعديل المرحلة ${grade.name} قريبًا`);
  };

  // حذف المرحلة الدراسية
  const handleDeleteGrade = async (grade: Grade) => {
    try {
      setIsLoading(true);

      // التحقق من وجود فصول مرتبطة بالمرحلة
      const { data: relatedClasses, error: classesError } = await supabase
        .from('school_classes')
        .select('id')
        .eq('grade_id', grade.id);

      if (classesError) {
        throw new Error(classesError.message);
      }

      if (relatedClasses && relatedClasses.length > 0) {
        throw new Error(`لا يمكن حذف المرحلة لأنها تحتوي على ${relatedClasses.length} فصل مرتبط بها`);
      }

      // حذف المرحلة من قاعدة البيانات
      const { error } = await supabase
        .from('school_grades')
        .delete()
        .eq('id', grade.id);

      if (error) {
        throw new Error(error.message);
      }

      // تحديث قائمة المراحل بعد الحذف
      setGrades(grades.filter(g => g.id !== grade.id));

      // عرض رسالة نجاح
      setError(null);

      // إعادة تحميل البيانات
      fetchGrades();
    } catch (err: any) {
      console.error('Error deleting grade:', err);
      setError(err.message || 'حدث خطأ أثناء حذف المرحلة');
    } finally {
      setIsLoading(false);
    }
  };

  // وظيفة لجلب بيانات المراحل الدراسية
  const fetchGrades = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const { data, error } = await supabase
        .from('school_grades')
        .select('*')
        .order('id', { ascending: true });

      if (error) {
        throw new Error(error.message);
      }

      setGrades(data || []);
    } catch (err: any) {
      console.error('Error fetching grades:', err);
      setError(err.message || 'حدث خطأ أثناء جلب بيانات المراحل الدراسية');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Layout>
      <div className="p-6">
        <div className="mb-6 bg-gradient-to-l from-[#0ABB87]/10 to-transparent p-4 rounded-lg border-r-4 border-[#0ABB87]">
          <div className="flex justify-between items-center">
            <div className="flex items-center">
              <div className="w-12 h-12 rounded-full bg-[#0ABB87] flex items-center justify-center text-white ml-4">
                <FaGraduationCap size={24} />
              </div>
              <div>
                <h1 className="text-2xl font-bold" style={{ color: '#0ABB87' }}>المراحل الدراسية</h1>
                <p className="text-gray-600 mt-1">
                  عرض وإدارة المراحل الدراسية في المدرسة
                </p>
              </div>
            </div>
            <button
              className="bg-[#0ABB87] hover:bg-[#0ABB87]/90 text-white px-4 py-2 rounded-md flex items-center transition-colors duration-200 clickable"
            >
              <FaPlus className="ml-2" />
              إضافة مرحلة
            </button>
          </div>
        </div>

        {error && (
          <div className="mb-6 p-4 bg-[#FD1361]/10 border-r-4 border-[#FD1361] rounded-md">
            <p className="text-[#FD1361]">{error}</p>
          </div>
        )}

        <GradeTable
          data={grades}
          isLoading={isLoading}
          onView={handleViewGrade}
          onEdit={handleEditGrade}
          onDelete={handleDeleteGrade}
        />
      </div>
    </Layout>
  );
}
