"use client";

import React, { useEffect } from 'react';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import {
  GradeSelector,
  ClassSelector,
  TuitionFeeSection,
  PaymentMethodSection
} from './financial';

interface Step3Props {
  onComplete: (data: any, isValid: boolean) => void;
  formData: Record<string, any>;
}

const Step3FinancialInfo: React.FC<Step3Props> = ({ onComplete, formData }) => {
  // التحقق من صحة البيانات باستخدام Yup
  const validationSchema = Yup.object({
    gradeId: Yup.number().required('المرحلة الدراسية مطلوبة'),
    classId: Yup.number().required('الفصل مطلوب'),
    tuitionFee: Yup.number().required('قيمة الرسوم الدراسية مطلوبة').min(0, 'يجب أن تكون القيمة أكبر من أو تساوي صفر'),
    discountAmount: Yup.number().min(0, 'يجب أن تكون القيمة أكبر من أو تساوي صفر'),
    discountReason: Yup.string().when('discountAmount', {
      is: (val: number) => val > 0,
      then: (schema) => schema.required('سبب الخصم مطلوب عند وجود خصم'),
      otherwise: (schema) => schema,
    }),
    paidAmount: Yup.number().required('القيمة المقدمة مطلوبة').min(0, 'يجب أن تكون القيمة أكبر من أو تساوي صفر'),
    paymentMethod: Yup.string().required('طريقة الدفع مطلوبة'),
    installmentsCount: Yup.number().when('paymentMethod', {
      is: 'installments',
      then: (schema) => schema.required('عدد الأقساط مطلوب').min(2, 'يجب أن يكون عدد الأقساط 2 على الأقل'),
      otherwise: (schema) => schema.nullable(),
    }),
  });

  // إعداد نموذج Formik
  const formik = useFormik({
    initialValues: {
      gradeId: formData.gradeId || '',
      classId: formData.classId || '',
      tuitionFee: formData.tuitionFee || '',
      discountAmount: formData.discountAmount || 0,
      discountReason: formData.discountReason || '',
      paidAmount: formData.paidAmount || '',
      paymentMethod: formData.paymentMethod || '',
      installmentsCount: formData.installmentsCount || '',
    },
    validationSchema,
    onSubmit: (values) => {
      console.log('Form submitted with values:', values);
    },
  });

  // حساب إجمالي الرسوم بعد الخصم
  const calculateTotalAfterDiscount = () => {
    const tuitionFee = parseFloat(formik.values.tuitionFee) || 0;
    const discountAmount = parseFloat(formik.values.discountAmount) || 0;
    return tuitionFee - discountAmount;
  };

  // حساب قيمة القسط
  const calculateInstallmentAmount = () => {
    const totalAfterDiscount = calculateTotalAfterDiscount();
    const installmentsCount = parseInt(formik.values.installmentsCount) || 1;
    return totalAfterDiscount / installmentsCount;
  };

  // تحديث البيانات عند تغييرها
  useEffect(() => {
    const isValid = formik.isValid && formik.dirty;
    const financialData = {
      ...formik.values,
      totalAfterDiscount: calculateTotalAfterDiscount(),
      installmentAmount: formik.values.paymentMethod === 'installments' ? calculateInstallmentAmount() : 0,
    };

    // استخدام مرجع ثابت لدالة onComplete لتجنب الحلقات اللانهائية
    const timer = setTimeout(() => {
      onComplete(financialData, isValid);
    }, 100);

    return () => clearTimeout(timer);
    // إزالة onComplete من مصفوفة التبعيات لتجنب الحلقات اللانهائية
  }, [formik.values, formik.isValid, formik.dirty]);

  // تحديث قيمة الرسوم الدراسية عند تغيير المرحلة الدراسية
  const handleGradeChange = async (e: React.ChangeEvent<HTMLSelectElement>) => {
    const gradeId = e.target.value;
    formik.setFieldValue('gradeId', gradeId);

    // إعادة تعيين قيمة الفصل
    formik.setFieldValue('classId', '');

    // تحديد قيمة الرسوم بناءً على المرحلة الدراسية
    if (gradeId) {
      let tuitionFee = 0;
      const gradeIdNum = parseInt(gradeId);
      
      if (gradeIdNum >= 1 && gradeIdNum <= 6) {
        // المرحلة الابتدائية
        tuitionFee = 5000;
      } else if (gradeIdNum >= 7 && gradeIdNum <= 9) {
        // المرحلة الإعدادية
        tuitionFee = 6000;
      } else {
        // المرحلة الثانوية أو غيرها
        tuitionFee = 7000;
      }
      
      formik.setFieldValue('tuitionFee', tuitionFee);
    }
  };

  // التحقق من وجود خصم
  const hasDiscount = parseFloat(formik.values.discountAmount) > 0;
  
  // التحقق من اختيار طريقة الدفع بالتقسيط
  const isInstallments = formik.values.paymentMethod === 'installments';

  return (
    <div className="bg-white rounded-lg p-6">
      <h2 className="text-xl font-bold text-primary mb-6 border-r-4 border-primary pr-3">
        القسم المالي
      </h2>

      <form className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* المرحلة الدراسية */}
          <GradeSelector
            value={formik.values.gradeId}
            onChange={handleGradeChange}
            onBlur={formik.handleBlur}
            error={formik.touched.gradeId ? formik.errors.gradeId as string : undefined}
            touched={formik.touched.gradeId}
          />

          {/* الفصل */}
          <ClassSelector
            value={formik.values.classId}
            gradeId={formik.values.gradeId}
            onChange={formik.handleChange}
            onBlur={formik.handleBlur}
            error={formik.touched.classId ? formik.errors.classId as string : undefined}
            touched={formik.touched.classId}
          />

          {/* قسم الرسوم والخصومات */}
          <TuitionFeeSection
            tuitionFee={{
              value: formik.values.tuitionFee,
              onChange: formik.handleChange,
              onBlur: formik.handleBlur,
              error: formik.touched.tuitionFee ? formik.errors.tuitionFee as string : undefined,
              touched: formik.touched.tuitionFee
            }}
            discountAmount={{
              value: formik.values.discountAmount,
              onChange: formik.handleChange,
              onBlur: formik.handleBlur,
              error: formik.touched.discountAmount ? formik.errors.discountAmount as string : undefined,
              touched: formik.touched.discountAmount
            }}
            discountReason={{
              value: formik.values.discountReason,
              onChange: formik.handleChange,
              onBlur: formik.handleBlur,
              error: formik.touched.discountReason ? formik.errors.discountReason as string : undefined,
              touched: formik.touched.discountReason
            }}
            hasDiscount={hasDiscount}
          />

          {/* قسم طريقة الدفع */}
          <PaymentMethodSection
            paidAmount={{
              value: formik.values.paidAmount,
              onChange: formik.handleChange,
              onBlur: formik.handleBlur,
              error: formik.touched.paidAmount ? formik.errors.paidAmount as string : undefined,
              touched: formik.touched.paidAmount
            }}
            paymentMethod={{
              value: formik.values.paymentMethod,
              onChange: formik.handleChange,
              onBlur: formik.handleBlur,
              error: formik.touched.paymentMethod ? formik.errors.paymentMethod as string : undefined,
              touched: formik.touched.paymentMethod
            }}
            installmentsCount={{
              value: formik.values.installmentsCount,
              onChange: formik.handleChange,
              onBlur: formik.handleBlur,
              error: formik.touched.installmentsCount ? formik.errors.installmentsCount as string : undefined,
              touched: formik.touched.installmentsCount
            }}
            isInstallments={isInstallments}
          />
        </div>

        {/* عرض ملخص المعلومات المالية */}
        <div className="mt-8 p-4 bg-gray-50 rounded-lg border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-700 mb-3">ملخص المعلومات المالية</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <p className="text-gray-600">إجمالي الرسوم: <span className="font-bold">{parseFloat(formik.values.tuitionFee) || 0} جنيه</span></p>
              <p className="text-gray-600">قيمة الخصم: <span className="font-bold">{parseFloat(formik.values.discountAmount) || 0} جنيه</span></p>
              <p className="text-gray-600">الإجمالي بعد الخصم: <span className="font-bold">{calculateTotalAfterDiscount()} جنيه</span></p>
            </div>
            <div>
              <p className="text-gray-600">القيمة المقدمة: <span className="font-bold">{parseFloat(formik.values.paidAmount) || 0} جنيه</span></p>
              {isInstallments && (
                <>
                  <p className="text-gray-600">عدد الأقساط: <span className="font-bold">{parseInt(formik.values.installmentsCount) || 0}</span></p>
                  <p className="text-gray-600">قيمة القسط: <span className="font-bold">{calculateInstallmentAmount().toFixed(2)} جنيه</span></p>
                </>
              )}
            </div>
          </div>
        </div>
      </form>
    </div>
  );
};

export default Step3FinancialInfo;
