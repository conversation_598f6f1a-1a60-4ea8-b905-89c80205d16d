# دليل إنشاء جداول تفاعلية باستخدام Tailwind CSS وDataTables

هذا الدليل يشرح كيفية إنشاء جداول تفاعلية تدعم عمليات CRUD بشكل كامل في مشروع نظام إدارة المدرسة.

## المتطلبات الأساسية

1. **Tailwind CSS**: لتصميم الجدول وجعله متجاوبًا.
2. **DataTables**: لإضافة ميزات التفاعل مثل البحث والترقيم والتصفية.
3. **jQuery**: مطلوبة لتشغيل DataTables.

## خطوات إنشاء جدول تفاعلي

### 1. تضمين الأدوات اللازمة

يمكنك إضافة المكتبات المطلوبة عبر CDN أو تثبيتها محليًا:

```html
<!-- jQuery -->
<script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>

<!-- DataTables CSS -->
<link rel="stylesheet" href="https://cdn.datatables.net/1.13.7/css/jquery.dataTables.min.css">

<!-- DataTables JS -->
<script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>

<!-- DataTables Responsive -->
<link rel="stylesheet" href="https://cdn.datatables.net/responsive/2.5.0/css/responsive.dataTables.min.css">
<script src="https://cdn.datatables.net/responsive/2.5.0/js/dataTables.responsive.min.js"></script>
```

### 2. تصميم هيكل الجدول

```html
<div class="overflow-x-auto bg-white rounded-lg shadow-md p-6">
  <div class="flex justify-between items-center mb-4">
    <h2 class="text-xl font-bold text-primary">عنوان الجدول</h2>
    <button class="bg-success hover:bg-success/90 text-white px-4 py-2 rounded-md flex items-center">
      <span class="material-icons ml-1">add</span>
      إضافة جديد
    </button>
  </div>
  
  <table id="dataTable" class="w-full text-right border-collapse">
    <thead class="bg-gray-100">
      <tr>
        <th class="p-3 border-b-2 border-gray-200">العمود 1</th>
        <th class="p-3 border-b-2 border-gray-200">العمود 2</th>
        <th class="p-3 border-b-2 border-gray-200">العمود 3</th>
        <th class="p-3 border-b-2 border-gray-200">الإجراءات</th>
      </tr>
    </thead>
    <tbody>
      <!-- سيتم ملء البيانات ديناميكيًا -->
    </tbody>
  </table>
</div>
```

### 3. تفعيل ميزات DataTables

```javascript
$(document).ready(function() {
  $('#dataTable').DataTable({
    responsive: true,
    language: {
      url: '//cdn.datatables.net/plug-ins/1.13.7/i18n/ar.json',
    },
    columnDefs: [
      {
        targets: -1,
        orderable: false,
        searchable: false
      }
    ]
  });
});
```

### 4. تنسيق الجدول باستخدام Tailwind CSS

يمكنك تخصيص مظهر الجدول باستخدام فئات Tailwind:

```css
/* تخصيص ألوان الرؤوس */
.dataTables_wrapper thead th {
  @apply bg-primary text-white font-bold;
}

/* تخصيص الصفوف المتناوبة */
.dataTables_wrapper tbody tr:nth-child(odd) {
  @apply bg-gray-50;
}

.dataTables_wrapper tbody tr:hover {
  @apply bg-gray-100;
}

/* تنسيق أزرار الإجراءات */
.action-btn {
  @apply inline-flex items-center justify-center p-2 rounded-full mx-1;
}

.edit-btn {
  @apply bg-secondary/10 text-secondary hover:bg-secondary hover:text-white;
}

.delete-btn {
  @apply bg-danger/10 text-danger hover:bg-danger hover:text-white;
}

.view-btn {
  @apply bg-info/10 text-info hover:bg-info hover:text-white;
}
```

### 5. جعل الأزرار والأيقونات وظيفية

```javascript
// إضافة سجل جديد
$('#addNewBtn').on('click', function() {
  // فتح نموذج الإضافة
  $('#addModal').removeClass('hidden');
});

// تعديل سجل
function editRecord(id) {
  // استرجاع بيانات السجل من الخادم
  fetch(`/api/records/${id}`)
    .then(response => response.json())
    .then(data => {
      // ملء النموذج بالبيانات
      $('#editForm #name').val(data.name);
      $('#editForm #email').val(data.email);
      // فتح نموذج التعديل
      $('#editModal').removeClass('hidden');
    });
}

// حذف سجل
function deleteRecord(id) {
  if (confirm('هل أنت متأكد من رغبتك في حذف هذا السجل؟')) {
    fetch(`/api/records/${id}`, {
      method: 'DELETE',
    })
      .then(response => response.json())
      .then(data => {
        // تحديث الجدول
        $('#dataTable').DataTable().ajax.reload();
        // عرض رسالة نجاح
        showNotification('تم حذف السجل بنجاح', 'success');
      })
      .catch(error => {
        showNotification('حدث خطأ أثناء الحذف', 'error');
      });
  }
}
```

### 6. ربط الجدول ببيانات ديناميكية

```javascript
$('#dataTable').DataTable({
  processing: true,
  serverSide: true,
  ajax: {
    url: '/api/records',
    type: 'GET'
  },
  columns: [
    { data: 'name' },
    { data: 'email' },
    { data: 'phone' },
    {
      data: null,
      render: function(data, type, row) {
        return `
          <button onclick="viewRecord(${row.id})" class="action-btn view-btn">
            <span class="material-icons">visibility</span>
          </button>
          <button onclick="editRecord(${row.id})" class="action-btn edit-btn">
            <span class="material-icons">edit</span>
          </button>
          <button onclick="deleteRecord(${row.id})" class="action-btn delete-btn">
            <span class="material-icons">delete</span>
          </button>
        `;
      }
    }
  ]
});
```

## تكامل مع نظام CRUD

### الإنشاء (Create)

```javascript
$('#addForm').on('submit', function(e) {
  e.preventDefault();
  
  const formData = {
    name: $('#addForm #name').val(),
    email: $('#addForm #email').val(),
    phone: $('#addForm #phone').val(),
  };
  
  fetch('/api/records', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(formData),
  })
    .then(response => response.json())
    .then(data => {
      // إغلاق النموذج
      $('#addModal').addClass('hidden');
      // إعادة تحميل الجدول
      $('#dataTable').DataTable().ajax.reload();
      // عرض رسالة نجاح
      showNotification('تمت الإضافة بنجاح', 'success');
    })
    .catch(error => {
      showNotification('حدث خطأ أثناء الإضافة', 'error');
    });
});
```

### التحديث (Update)

```javascript
$('#editForm').on('submit', function(e) {
  e.preventDefault();
  
  const id = $('#editForm #recordId').val();
  const formData = {
    name: $('#editForm #name').val(),
    email: $('#editForm #email').val(),
    phone: $('#editForm #phone').val(),
  };
  
  fetch(`/api/records/${id}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(formData),
  })
    .then(response => response.json())
    .then(data => {
      // إغلاق النموذج
      $('#editModal').addClass('hidden');
      // إعادة تحميل الجدول
      $('#dataTable').DataTable().ajax.reload();
      // عرض رسالة نجاح
      showNotification('تم التحديث بنجاح', 'success');
    })
    .catch(error => {
      showNotification('حدث خطأ أثناء التحديث', 'error');
    });
});
```

## نصائح لتحسين التجربة

1. **التحقق من الصلاحيات**: تأكد أن المستخدم لديه الإذن لتنفيذ كل عملية.
2. **معالجة الأخطاء**: أظهر رسائل واضحة للمستخدم إذا فشلت أي عملية.
3. **التحقق من المدخلات**: تحقق من صحة البيانات قبل إرسالها إلى الخادم.
4. **تحسين الأداء**: استخدم التحميل الكسول (Lazy Loading) للبيانات الكبيرة.
5. **دعم الأجهزة المحمولة**: تأكد من أن الجدول متجاوب ويعمل بشكل جيد على الأجهزة المختلفة.
