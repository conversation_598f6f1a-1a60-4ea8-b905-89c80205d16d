// تحويل بيانات CSV إلى تنسيق نموذج التسجيل
export const mapCSVDataToStudentForm = (rowData: Record<string, any>) => {
  // تعيين الحقول من CSV إلى حقول نموذج التسجيل
  const mappings: Record<string, string> = {
    'الاسم الكامل': 'fullName',
    'رقم الهوية': 'idNumber',
    'تاريخ الميلاد': 'birthDate',
    'الجنس': 'gender',
    'الديانة': 'religion',
    'الحالة الاجتماعية': 'maritalStatus',
    'البريد الإلكتروني': 'email',
    'رقم الهاتف': 'phone',
    // يمكن إضافة المزيد من التعيينات حسب الحاجة
  };

  // تحويل قيم الجنس إلى القيم المتوقعة في النموذج
  const genderMapping: Record<string, string> = {
    'ذكر': 'male',
    'أنثى': 'female',
    'male': 'male',
    'female': 'female',
  };

  // تحويل قيم الديانة إلى القيم المتوقعة في النموذج
  const religionMapping: Record<string, string> = {
    'الإسلام': 'islam',
    'المسيحية': 'christianity',
    'islam': 'islam',
    'christianity': 'christianity',
  };

  // إنشاء كائن البيانات المحولة
  const formData: Record<string, any> = {};

  // تعيين البيانات باستخدام التعيينات
  Object.entries(rowData).forEach(([key, value]) => {
    const formField = mappings[key];
    if (formField) {
      if (formField === 'gender' && value) {
        formData[formField] = genderMapping[value.toString()] || value;
      } else if (formField === 'religion' && value) {
        formData[formField] = religionMapping[value.toString()] || value;
      } else {
        formData[formField] = value;
      }
    }
  });

  return formData;
};
