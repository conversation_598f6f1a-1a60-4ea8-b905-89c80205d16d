"use client";

import React, { useState, useEffect } from 'react';
import { supabase } from '@/lib/supabase';
import { Attachment } from '@/types/table.types';
import { FaPaperclip, FaImage, FaIdCard, FaFileAlt, FaFileMedical, FaDownload, FaEye } from 'react-icons/fa';

interface ViewAttachmentsProps {
  studentId: string | number;
}

const ViewAttachments: React.FC<ViewAttachmentsProps> = ({ studentId }) => {
  const [attachments, setAttachments] = useState<Attachment | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchAttachments() {
      try {
        setIsLoading(true);
        setError(null);
        
        if (!studentId) {
          throw new Error('معرف الطالب غير موجود');
        }
        
        const { data, error } = await supabase
          .from('attachments')
          .select('*')
          .eq('student_id', studentId)
          .single();
        
        if (error && error.code !== 'PGRST116') {
          throw new Error(error.message);
        }
        
        setAttachments(data);
      } catch (err: any) {
        console.error('Error fetching attachments:', err);
        setError(err.message || 'حدث خطأ أثناء جلب المرفقات');
      } finally {
        setIsLoading(false);
      }
    }
    
    fetchAttachments();
  }, [studentId]);

  // فتح الملف في نافذة جديدة
  const openFile = (url: string) => {
    if (!url) return;
    window.open(url, '_blank');
  };

  // تنزيل الملف
  const downloadFile = async (url: string, fileName: string) => {
    if (!url) return;
    
    try {
      const response = await fetch(url);
      const blob = await response.blob();
      const downloadUrl = window.URL.createObjectURL(blob);
      
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.download = fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      window.URL.revokeObjectURL(downloadUrl);
    } catch (err) {
      console.error('Error downloading file:', err);
      alert('حدث خطأ أثناء تنزيل الملف');
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center p-8">
        <div className="relative">
          <div className="w-12 h-12 border-4 border-[#0ABB87]/30 border-t-[#0ABB87] rounded-full animate-spin"></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
            <FaPaperclip size={16} className="text-[#0ABB87]" />
          </div>
        </div>
        <p className="mr-3 text-gray-600">جاري تحميل البيانات...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-4 bg-[#FD1361]/10 border-r-4 border-[#FD1361] rounded-md">
        <p className="text-[#FD1361]">{error}</p>
      </div>
    );
  }

  if (!attachments) {
    return (
      <div className="p-4 bg-gray-100 border-r-4 border-gray-400 rounded-md">
        <p className="text-gray-600">لا توجد مرفقات للطالب</p>
      </div>
    );
  }

  // التحقق من وجود أي مرفقات
  const hasAnyAttachment = 
    attachments.photo_url || 
    attachments.birth_certificate_url || 
    attachments.id_card_url || 
    attachments.academic_record_url || 
    attachments.health_documents_url;

  if (!hasAnyAttachment) {
    return (
      <div className="p-4 bg-gray-100 border-r-4 border-gray-400 rounded-md">
        <p className="text-gray-600">لا توجد مرفقات للطالب</p>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg p-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {attachments.photo_url && (
          <div className="bg-[#0ABB87]/10 p-4 rounded-lg">
            <div className="flex items-center mb-3">
              <div className="w-10 h-10 rounded-full bg-[#0ABB87] flex items-center justify-center text-white ml-4">
                <FaImage size={18} />
              </div>
              <div>
                <h3 className="font-medium text-gray-800">الصورة الشخصية</h3>
              </div>
            </div>
            
            <div className="flex justify-center mb-4">
              <img 
                src={attachments.photo_url} 
                alt="صورة الطالب" 
                className="w-32 h-32 object-cover rounded-lg border-2 border-[#0ABB87]"
              />
            </div>
            
            <div className="flex justify-center space-x-2 space-x-reverse">
              <button
                onClick={() => openFile(attachments.photo_url!)}
                className="flex items-center px-3 py-2 bg-[#0ABB87] text-white rounded-md hover:bg-[#0ABB87]/90 transition-colors duration-200 clickable"
              >
                <FaEye className="ml-2" size={14} />
                عرض
              </button>
              
              <button
                onClick={() => downloadFile(attachments.photo_url!, 'student_photo.jpg')}
                className="flex items-center px-3 py-2 bg-[#5578EB] text-white rounded-md hover:bg-[#5578EB]/90 transition-colors duration-200 clickable"
              >
                <FaDownload className="ml-2" size={14} />
                تنزيل
              </button>
            </div>
          </div>
        )}
        
        {attachments.birth_certificate_url && (
          <div className="bg-[#5578EB]/10 p-4 rounded-lg">
            <div className="flex items-center mb-3">
              <div className="w-10 h-10 rounded-full bg-[#5578EB] flex items-center justify-center text-white ml-4">
                <FaFileAlt size={18} />
              </div>
              <div>
                <h3 className="font-medium text-gray-800">شهادة الميلاد</h3>
              </div>
            </div>
            
            <div className="flex justify-center mb-4">
              <div className="w-32 h-32 bg-[#5578EB]/20 rounded-lg border-2 border-[#5578EB] flex items-center justify-center">
                <FaFileAlt size={40} className="text-[#5578EB]" />
              </div>
            </div>
            
            <div className="flex justify-center space-x-2 space-x-reverse">
              <button
                onClick={() => openFile(attachments.birth_certificate_url!)}
                className="flex items-center px-3 py-2 bg-[#5578EB] text-white rounded-md hover:bg-[#5578EB]/90 transition-colors duration-200 clickable"
              >
                <FaEye className="ml-2" size={14} />
                عرض
              </button>
              
              <button
                onClick={() => downloadFile(attachments.birth_certificate_url!, 'birth_certificate.pdf')}
                className="flex items-center px-3 py-2 bg-[#21ADE7] text-white rounded-md hover:bg-[#21ADE7]/90 transition-colors duration-200 clickable"
              >
                <FaDownload className="ml-2" size={14} />
                تنزيل
              </button>
            </div>
          </div>
        )}
        
        {attachments.id_card_url && (
          <div className="bg-[#FD1361]/10 p-4 rounded-lg">
            <div className="flex items-center mb-3">
              <div className="w-10 h-10 rounded-full bg-[#FD1361] flex items-center justify-center text-white ml-4">
                <FaIdCard size={18} />
              </div>
              <div>
                <h3 className="font-medium text-gray-800">بطاقة الهوية</h3>
              </div>
            </div>
            
            <div className="flex justify-center mb-4">
              <div className="w-32 h-32 bg-[#FD1361]/20 rounded-lg border-2 border-[#FD1361] flex items-center justify-center">
                <FaIdCard size={40} className="text-[#FD1361]" />
              </div>
            </div>
            
            <div className="flex justify-center space-x-2 space-x-reverse">
              <button
                onClick={() => openFile(attachments.id_card_url!)}
                className="flex items-center px-3 py-2 bg-[#FD1361] text-white rounded-md hover:bg-[#FD1361]/90 transition-colors duration-200 clickable"
              >
                <FaEye className="ml-2" size={14} />
                عرض
              </button>
              
              <button
                onClick={() => downloadFile(attachments.id_card_url!, 'id_card.pdf')}
                className="flex items-center px-3 py-2 bg-[#384AD7] text-white rounded-md hover:bg-[#384AD7]/90 transition-colors duration-200 clickable"
              >
                <FaDownload className="ml-2" size={14} />
                تنزيل
              </button>
            </div>
          </div>
        )}
        
        {attachments.academic_record_url && (
          <div className="bg-[#21ADE7]/10 p-4 rounded-lg">
            <div className="flex items-center mb-3">
              <div className="w-10 h-10 rounded-full bg-[#21ADE7] flex items-center justify-center text-white ml-4">
                <FaFileAlt size={18} />
              </div>
              <div>
                <h3 className="font-medium text-gray-800">السجل الأكاديمي</h3>
              </div>
            </div>
            
            <div className="flex justify-center mb-4">
              <div className="w-32 h-32 bg-[#21ADE7]/20 rounded-lg border-2 border-[#21ADE7] flex items-center justify-center">
                <FaFileAlt size={40} className="text-[#21ADE7]" />
              </div>
            </div>
            
            <div className="flex justify-center space-x-2 space-x-reverse">
              <button
                onClick={() => openFile(attachments.academic_record_url!)}
                className="flex items-center px-3 py-2 bg-[#21ADE7] text-white rounded-md hover:bg-[#21ADE7]/90 transition-colors duration-200 clickable"
              >
                <FaEye className="ml-2" size={14} />
                عرض
              </button>
              
              <button
                onClick={() => downloadFile(attachments.academic_record_url!, 'academic_record.pdf')}
                className="flex items-center px-3 py-2 bg-[#0ABB87] text-white rounded-md hover:bg-[#0ABB87]/90 transition-colors duration-200 clickable"
              >
                <FaDownload className="ml-2" size={14} />
                تنزيل
              </button>
            </div>
          </div>
        )}
        
        {attachments.health_documents_url && (
          <div className="bg-[#384AD7]/10 p-4 rounded-lg">
            <div className="flex items-center mb-3">
              <div className="w-10 h-10 rounded-full bg-[#384AD7] flex items-center justify-center text-white ml-4">
                <FaFileMedical size={18} />
              </div>
              <div>
                <h3 className="font-medium text-gray-800">الوثائق الصحية</h3>
              </div>
            </div>
            
            <div className="flex justify-center mb-4">
              <div className="w-32 h-32 bg-[#384AD7]/20 rounded-lg border-2 border-[#384AD7] flex items-center justify-center">
                <FaFileMedical size={40} className="text-[#384AD7]" />
              </div>
            </div>
            
            <div className="flex justify-center space-x-2 space-x-reverse">
              <button
                onClick={() => openFile(attachments.health_documents_url!)}
                className="flex items-center px-3 py-2 bg-[#384AD7] text-white rounded-md hover:bg-[#384AD7]/90 transition-colors duration-200 clickable"
              >
                <FaEye className="ml-2" size={14} />
                عرض
              </button>
              
              <button
                onClick={() => downloadFile(attachments.health_documents_url!, 'health_documents.pdf')}
                className="flex items-center px-3 py-2 bg-[#FD1361] text-white rounded-md hover:bg-[#FD1361]/90 transition-colors duration-200 clickable"
              >
                <FaDownload className="ml-2" size={14} />
                تنزيل
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ViewAttachments;
