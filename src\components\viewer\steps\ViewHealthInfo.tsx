"use client";

import React, { useState, useEffect } from 'react';
import { supabase } from '@/lib/supabase';
import { HealthInfo } from '@/types/table.types';
import { FaHeartbeat, FaAllergies, FaPills, FaUserMd, FaPhone, FaBuilding, FaIdCard } from 'react-icons/fa';

interface ViewHealthInfoProps {
  studentId: string | number;
}

const ViewHealthInfo: React.FC<ViewHealthInfoProps> = ({ studentId }) => {
  const [healthInfo, setHealthInfo] = useState<HealthInfo | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchHealthInfo() {
      try {
        setIsLoading(true);
        setError(null);
        
        if (!studentId) {
          throw new Error('معرف الطالب غير موجود');
        }
        
        const { data, error } = await supabase
          .from('health_info')
          .select('*')
          .eq('student_id', studentId)
          .single();
        
        if (error && error.code !== 'PGRST116') {
          throw new Error(error.message);
        }
        
        setHealthInfo(data);
      } catch (err: any) {
        console.error('Error fetching health info:', err);
        setError(err.message || 'حدث خطأ أثناء جلب المعلومات الصحية');
      } finally {
        setIsLoading(false);
      }
    }
    
    fetchHealthInfo();
  }, [studentId]);

  if (isLoading) {
    return (
      <div className="flex justify-center items-center p-8">
        <div className="relative">
          <div className="w-12 h-12 border-4 border-[#FD1361]/30 border-t-[#FD1361] rounded-full animate-spin"></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
            <FaHeartbeat size={16} className="text-[#FD1361]" />
          </div>
        </div>
        <p className="mr-3 text-gray-600">جاري تحميل البيانات...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-4 bg-[#FD1361]/10 border-r-4 border-[#FD1361] rounded-md">
        <p className="text-[#FD1361]">{error}</p>
      </div>
    );
  }

  if (!healthInfo) {
    return (
      <div className="p-4 bg-gray-100 border-r-4 border-gray-400 rounded-md">
        <p className="text-gray-600">لا توجد معلومات صحية للطالب</p>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg p-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {healthInfo.general_health && (
          <div className="flex items-start">
            <div className="w-10 h-10 rounded-full bg-[#FD1361]/10 flex items-center justify-center text-[#FD1361] ml-4">
              <FaHeartbeat size={18} />
            </div>
            <div>
              <h3 className="text-sm text-gray-500">الحالة الصحية العامة</h3>
              <p className="font-medium">{healthInfo.general_health}</p>
            </div>
          </div>
        )}
        
        {healthInfo.chronic_diseases && (
          <div className="flex items-start">
            <div className="w-10 h-10 rounded-full bg-[#5578EB]/10 flex items-center justify-center text-[#5578EB] ml-4">
              <FaPills size={18} />
            </div>
            <div>
              <h3 className="text-sm text-gray-500">الأمراض المزمنة</h3>
              <p className="font-medium">{healthInfo.chronic_diseases}</p>
            </div>
          </div>
        )}
        
        {healthInfo.drug_allergies && (
          <div className="flex items-start">
            <div className="w-10 h-10 rounded-full bg-[#0ABB87]/10 flex items-center justify-center text-[#0ABB87] ml-4">
              <FaAllergies size={18} />
            </div>
            <div>
              <h3 className="text-sm text-gray-500">الحساسية من الأدوية</h3>
              <p className="font-medium">{healthInfo.drug_allergies}</p>
            </div>
          </div>
        )}
        
        {healthInfo.doctor_name && (
          <div className="flex items-start">
            <div className="w-10 h-10 rounded-full bg-[#384AD7]/10 flex items-center justify-center text-[#384AD7] ml-4">
              <FaUserMd size={18} />
            </div>
            <div>
              <h3 className="text-sm text-gray-500">اسم الطبيب</h3>
              <p className="font-medium">{healthInfo.doctor_name}</p>
            </div>
          </div>
        )}
        
        {healthInfo.doctor_phone && (
          <div className="flex items-start">
            <div className="w-10 h-10 rounded-full bg-[#21ADE7]/10 flex items-center justify-center text-[#21ADE7] ml-4">
              <FaPhone size={18} />
            </div>
            <div>
              <h3 className="text-sm text-gray-500">رقم هاتف الطبيب</h3>
              <p className="font-medium">{healthInfo.doctor_phone}</p>
            </div>
          </div>
        )}
        
        {healthInfo.insurance_company && (
          <div className="flex items-start">
            <div className="w-10 h-10 rounded-full bg-[#FD1361]/10 flex items-center justify-center text-[#FD1361] ml-4">
              <FaBuilding size={18} />
            </div>
            <div>
              <h3 className="text-sm text-gray-500">شركة التأمين</h3>
              <p className="font-medium">{healthInfo.insurance_company}</p>
            </div>
          </div>
        )}
        
        {healthInfo.insurance_policy_number && (
          <div className="flex items-start">
            <div className="w-10 h-10 rounded-full bg-[#0ABB87]/10 flex items-center justify-center text-[#0ABB87] ml-4">
              <FaIdCard size={18} />
            </div>
            <div>
              <h3 className="text-sm text-gray-500">رقم بوليصة التأمين</h3>
              <p className="font-medium">{healthInfo.insurance_policy_number}</p>
            </div>
          </div>
        )}
      </div>
      
      {!healthInfo.general_health && 
       !healthInfo.chronic_diseases && 
       !healthInfo.drug_allergies && 
       !healthInfo.doctor_name && 
       !healthInfo.doctor_phone && 
       !healthInfo.insurance_company && 
       !healthInfo.insurance_policy_number && (
        <div className="p-4 bg-gray-100 border-r-4 border-gray-400 rounded-md">
          <p className="text-gray-600">لا توجد معلومات صحية مفصلة للطالب</p>
        </div>
      )}
    </div>
  );
};

export default ViewHealthInfo;
